steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - task: UseDotNet@2
    inputs:
      version: 6.x

  - task: EsrpCodeSigning@5
    inputs:
      UseMSIAuthentication: true
      ConnectedServiceName: vscode-esrp
      AppRegistrationClientId: $(ESRP_CLIENT_ID)
      AppRegistrationTenantId: $(ESRP_TENANT_ID)
      AuthAKVName: vscode-esrp
      AuthSignCertName: esrp-sign
      FolderPath: .
      Pattern: noop
    displayName: 'Install ESRP Tooling'

  - script: |
      # For legacy purposes, arch for x64 is just 'darwin'
      case $VSCODE_ARCH in
        x64) ASSET_ID="darwin" ;;
        arm64) ASSET_ID="darwin-arm64" ;;
        universal) ASSET_ID="darwin-universal" ;;
      esac
      echo "##vso[task.setvariable variable=ASSET_ID]$ASSET_ID"
    displayName: Set asset id variable

  - script: |
      if [ -z "$(ASSET_ID)" ]; then
        echo "ASSET_ID is empty"
        exit 1
      else
        echo "ASSET_ID is set to $(ASSET_ID)"
      fi
    displayName: Check ASSET_ID variable

  - download: current
    artifact: unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive
    displayName: Download $(VSCODE_ARCH) artifact

  - script: node build/azure-pipelines/common/sign $(Agent.RootDirectory)/_tasks/EsrpCodeSigning_*/*/net6.0/esrpcli.dll sign-darwin $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive VSCode-darwin-$(VSCODE_ARCH).zip
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    displayName: Codesign

  - script: node build/azure-pipelines/common/sign $(Agent.RootDirectory)/_tasks/EsrpCodeSigning_*/*/net6.0/esrpcli.dll notarize-darwin $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive VSCode-darwin-$(VSCODE_ARCH).zip
    env:
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
    displayName: Notarize

  - script: unzip $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive/VSCode-darwin-$(VSCODE_ARCH).zip -d $(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH)
    displayName: Extract signed app

  - script: |
      set -e
      APP_ROOT="$(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH)"
      APP_NAME="`ls $APP_ROOT | head -n 1`"
      APP_PATH="$APP_ROOT/$APP_NAME"
      codesign -dv --deep --verbose=4 "$APP_PATH"
      "$APP_PATH/Contents/Resources/app/bin/code" --export-default-configuration=.build
    displayName: Verify signature
    condition: and(succeeded(), ne(variables['VSCODE_ARCH'], 'arm64'))

  - script: mv $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive/VSCode-darwin-x64.zip $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive/VSCode-darwin.zip
    displayName: Rename x64 build to its legacy name
    condition: and(succeeded(), eq(variables['VSCODE_ARCH'], 'x64'))

  - task: 1ES.PublishPipelineArtifact@1
    inputs:
      targetPath: $(Pipeline.Workspace)/unsigned_vscode_client_darwin_$(VSCODE_ARCH)_archive/VSCode-$(ASSET_ID).zip
      artifactName: vscode_client_darwin_$(VSCODE_ARCH)_archive
      sbomBuildDropPath: $(Agent.BuildDirectory)/VSCode-darwin-$(VSCODE_ARCH)
      sbomPackageName: "VS Code macOS $(VSCODE_ARCH)"
      sbomPackageVersion: $(Build.SourceVersion)
    displayName: Publish client archive
