# JoyCoder Plugin Integration Guide

本文档说明如何在JoyCoder IDE开发环境中集成和调试JoyCoder插件。

## 🚀 快速开始

### 1. 验证依赖安装

```bash
npm run verify-deps
```

这个命令会检查：
- Node.js版本（需要 >= 18）
- pnpm版本
- 主项目依赖
- JoyCoder插件依赖
- 构建输出目录

### 2. 开发模式

```bash
npm run dev
```

这个命令会同时启动：
- React组件的watch模式
- JoyCoder插件的watch模式
- 主IDE的watch模式

所有组件都会实时重新编译，支持热重载调试。

### 3. 构建生产版本

```bash
# macOS ARM64
npm run build:dev-arm

# macOS Intel
npm run build:dev-intel

# Linux
npm run build:dev-linux

# Windows
npm run build:dev-win
```

所有构建命令都会自动构建JoyCoder插件并集成到最终的IDE中。

## 📁 项目结构

```
JoyCoder-IDE/
├── extensions/
│   └── JoyCoder-Plugin/          # JoyCoder插件源码
│       ├── packages/             # 插件子包
│       ├── src/                  # 插件主要源码
│       ├── dist/                 # 插件构建输出
│       └── package.json          # 插件配置
├── scripts/
│   ├── start-dev.js             # 开发启动脚本
│   └── verify-dependencies.js   # 依赖验证脚本
├── JoyCoder-release-scripts/    # 构建脚本
└── src/                         # IDE主要源码
```

## 🔧 开发工作流

### 实时调试

1. **启动开发环境**：
   ```bash
   npm run dev
   ```

2. **修改插件代码**：
   - 编辑 `extensions/JoyCoder-Plugin/src/` 下的文件
   - 插件会自动重新编译
   - IDE会自动重新加载插件

3. **修改IDE代码**：
   - 编辑 `src/` 下的文件
   - IDE会自动重新编译

### 插件独立开发

如果只需要开发插件：

```bash
cd extensions/JoyCoder-Plugin
pnpm run watch
```

### 构建验证

在提交代码前，建议运行完整构建：

```bash
npm run build:dev-arm  # 或其他平台
```

## 🛠️ 构建流程说明

### 开发模式构建流程

1. **React组件编译** - 编译IDE中的React组件
2. **JoyCoder插件构建** - 使用pnpm构建插件
3. **内置扩展编译** - 编译其他内置扩展
4. **主IDE编译** - 编译主IDE代码
5. **插件集成** - 将插件集成到IDE中

### 生产构建流程

1. **依赖检查** - 验证所有依赖已安装
2. **JoyCoder插件构建** - 构建生产版本插件
3. **内置扩展编译** - 编译所有内置扩展
4. **主IDE构建** - 构建主IDE
5. **签名和打包** - 对应用进行签名和打包
6. **插件集成** - 将插件集成到最终应用中

## 📝 配置说明

### 插件配置

JoyCoder插件使用pnpm workspace管理多个子包：

- `@joycoder/agent-common` - 通用代理功能
- `@joycoder/agent-driven` - 代理驱动功能
- `@joycoder/plugin-base-ai` - AI基础功能
- 等等...

### 构建配置

插件支持多种构建模式：

- `pnpm build` - 生产构建
- `pnpm watch` - 开发监听模式
- `pnpm build:web` - Web组件构建
- `pnpm build:agent-driven` - 代理驱动组件构建

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**：
   ```bash
   # 清理并重新安装
   npm run clear-all  # 在插件目录下
   pnpm install
   ```

2. **构建失败**：
   ```bash
   # 验证依赖
   npm run verify-deps
   
   # 清理构建缓存
   rm -rf out out-build extensions/JoyCoder-Plugin/dist
   ```

3. **插件未加载**：
   - 检查插件是否正确构建到 `dist/` 目录
   - 检查IDE是否正确集成了插件

### 调试技巧

1. **查看构建日志**：
   开发模式会显示详细的构建日志，包括插件构建状态

2. **独立测试插件**：
   ```bash
   cd extensions/JoyCoder-Plugin
   pnpm build
   pnpm test  # 如果有测试
   ```

3. **检查插件集成**：
   构建完成后，检查最终应用中是否包含插件文件

## 📚 相关文档

- [JoyCoder IDE架构文档](./JoyCoder-IDE-Architecture-README.md)
- [构建流程文档](./build-process-arm.md)
- [插件开发指南](../extensions/JoyCoder-Plugin/README.md)

## 🤝 贡献指南

1. 确保所有依赖已正确安装
2. 运行 `npm run verify-deps` 验证环境
3. 使用 `npm run dev` 进行开发
4. 提交前运行完整构建测试
5. 遵循现有的代码风格和约定
