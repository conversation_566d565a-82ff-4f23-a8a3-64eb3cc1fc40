# Do not run this unless you know what you're doing.
# Don't run this when JoyCode is open, or <PERSON> will confuse the two versions (run in terminal or VS Code).

set -e

# 清理钥匙串中的证书
security find-certificate -a -c "Developer ID Application: Beijing Haiyi Tongzhan Information Technology Co., Ltd" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
security find-certificate -a -c "Developer ID Certification Authority" -Z ~/Library/Keychains/login.keychain-db 2>/dev/null | grep SHA-1 | awk '{print $3}' | xargs -I {} security delete-certificate -Z {} ~/Library/Keychains/login.keychain-db 2>/dev/null || true
echo "已清理钥匙串中的证书"

# 导入证书
./import_certificates.sh 20241218

# 清理 内置构建
rm -rf ../extensions/ai-resource/out && rm -rf ../extensions/clouddev/out

# 构建JoyCoder插件
echo "🔌 Building JoyCoder Plugin..."
cd ../extensions/JoyCoder-Plugin && pnpm build && cd ../../JoyCoder-release-scripts

# 编译内置扩展
cd .. && npm run gulp compile-extension:ai-resource && cd JoyCoder-release-scripts
cd .. && npm run gulp compile-extension:clouddev && cd JoyCoder-release-scripts
# Build, sign and package x64
./mac-sign.sh buildDev x64 dev
npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6
./cp-vsix.sh joycoder-editor joycode
# ./cp-vsix.sh clouddev- jdcom
./move-joycoder-extension.sh VSCode-darwin-x64 joycode.joycoder-editor
# ./move-joycoder-extension.sh VSCode-darwin-x64 jdcom.clouddev
# ./mac-sign.sh buildreh x64
./mac-sign.sh sign x64
./mac-sign.sh notarize x64
./mac-sign.sh rawapp x64
# ./mac-sign.sh hashrawapp x64
# ./mac-sign.sh packagereh x64


