@echo off
REM Windows批处理文件，执行与windows.sh相同的操作

echo 开始构建JoyCode Windows版本...

REM 构建React部分
call npm run buildreact
if %errorlevel% neq 0 (
    echo React构建失败！
    exit /b %errorlevel%
)

REM 清理内置扩展构建
echo 清理内置扩展构建...
if exist "..\extensions\ai-resource\out" rmdir /s /q "..\extensions\ai-resource\out"
if exist "..\extensions\clouddev\out" rmdir /s /q "..\extensions\clouddev\out"

REM 构建JoyCoder插件
echo 🔌 Building JoyCoder Plugin...
cd ..\extensions\JoyCoder-Plugin
call pnpm build
if %errorlevel% neq 0 (
    echo JoyCoder插件构建失败！
    exit /b %errorlevel%
)
cd ..\..\JoyCoder-release-scripts

REM 编译内置扩展
echo 编译ai-resource扩展...
call npm run gulp compile-extension:ai-resource
if %errorlevel% neq 0 (
    echo ai-resource扩展编译失败！
    exit /b %errorlevel%
)

echo 编译clouddev扩展...
call npm run gulp compile-extension:clouddev
if %errorlevel% neq 0 (
    echo clouddev扩展编译失败！
    exit /b %errorlevel%
)

REM 构建主应用
call npm run gulp-dev vscode-win32-x64-min
if %errorlevel% neq 0 (
    echo 主应用构建失败！
    exit /b %errorlevel%
)

REM 更改图标和构建更新器
call npm run gulp vscode-win32-x64-inno-updater
if %errorlevel% neq 0 (
    echo 更新器构建失败！
    exit /b %errorlevel%
)

REM 修改commit
call npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6
if %errorlevel% neq 0 (
    echo 更新commit失败！
    exit /b %errorlevel%
)

REM 移动扩展
call npm run move:extension -- joycoder-editor joycode VSCode-win32-x64 joycode.joycoder-editor
if %errorlevel% neq 0 (
    echo 移动joycoder-editor扩展失败！
    exit /b %errorlevel%
)

@REM call npm run move:extension -- clouddev- jdcom VSCode-win32-x64 jdcom.clouddev
@REM if %errorlevel% neq 0 (
@REM     echo 移动clouddev扩展失败！
@REM     exit /b %errorlevel%
@REM )

REM 构建系统安装程序（运行两次）
echo 构建系统安装程序（第一次）...
call npm run gulp vscode-win32-x64-system-setup
if %errorlevel% neq 0 (
    echo 系统安装程序构建失败（第一次）！
    exit /b %errorlevel%
)

echo 构建系统安装程序（第二次）...
call npm run gulp vscode-win32-x64-system-setup
if %errorlevel% neq 0 (
    echo 系统安装程序构建失败（第二次）！
    exit /b %errorlevel%
)

echo 构建完成！输出文件位于.build/目录中。
