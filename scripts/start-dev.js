const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting JoyCoder IDE development environment...');

// 存储所有子进程
const processes = [];

// 启动watchreact
console.log('📦 Starting React watch...');
const watchreact = spawn('npm', ['run', 'watchreact'], { stdio: 'inherit', shell: true });
processes.push(watchreact);

// 启动JoyCoder插件watch
console.log('🔌 Starting JoyCoder Plugin watch...');
const pluginWatch = spawn('pnpm', ['run', 'watch'], {
	stdio: 'inherit',
	shell: true,
	cwd: path.join(__dirname, '../extensions/JoyCoder-Plugin')
});
processes.push(pluginWatch);

// 等待5秒后启动主watch
setTimeout(() => {
	console.log('⚡ Starting main IDE watch...');
	const watch = spawn('npm', ['run', 'watch'], { stdio: 'inherit', shell: true });
	processes.push(watch);

	// 监听主进程退出
	watch.on('close', (code) => {
		console.log('🛑 Shutting down all processes...');
		processes.forEach(proc => {
			if (!proc.killed) {
				proc.kill();
			}
		});
		process.exit(code);
	});
}, 5000);

// 优雅退出处理
process.on('SIGINT', () => {
	console.log('\n🛑 Received SIGINT, shutting down gracefully...');
	processes.forEach(proc => {
		if (!proc.killed) {
			proc.kill('SIGINT');
		}
	});
	process.exit(0);
});

process.on('SIGTERM', () => {
	console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
	processes.forEach(proc => {
		if (!proc.killed) {
			proc.kill('SIGTERM');
		}
	});
	process.exit(0);
});
