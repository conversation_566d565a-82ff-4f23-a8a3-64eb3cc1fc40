#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Verifying JoyCoder IDE dependencies...\n');

// 检查主项目依赖
function checkMainDependencies() {
    console.log('📦 Checking main project dependencies...');
    
    const nodeModulesPath = path.join(__dirname, '../node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
        console.log('❌ Main project node_modules not found');
        return false;
    }
    
    // 检查关键依赖
    const keyDependencies = [
        '@anthropic-ai/sdk',
        '@google/generative-ai',
        'electron',
        'react',
        'react-dom',
        'typescript',
        'webpack'
    ];
    
    let allFound = true;
    keyDependencies.forEach(dep => {
        const depPath = path.join(nodeModulesPath, dep);
        if (fs.existsSync(depPath)) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - Missing`);
            allFound = false;
        }
    });
    
    return allFound;
}

// 检查JoyCoder插件依赖
function checkPluginDependencies() {
    console.log('\n🔌 Checking JoyCoder Plugin dependencies...');
    
    const pluginPath = path.join(__dirname, '../extensions/JoyCoder-Plugin');
    const nodeModulesPath = path.join(pluginPath, 'node_modules');
    
    if (!fs.existsSync(nodeModulesPath)) {
        console.log('❌ JoyCoder Plugin node_modules not found');
        return false;
    }
    
    // 检查插件关键依赖
    const keyDependencies = [
        '@ant-design/icons',
        '@babel/parser',
        'webpack',
        'typescript',
        'eslint'
    ];
    
    let allFound = true;
    keyDependencies.forEach(dep => {
        const depPath = path.join(nodeModulesPath, dep);
        if (fs.existsSync(depPath)) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - Missing`);
            allFound = false;
        }
    });
    
    return allFound;
}

// 检查pnpm版本
function checkPnpmVersion() {
    console.log('\n📋 Checking pnpm version...');
    try {
        const version = execSync('pnpm --version', { encoding: 'utf8' }).trim();
        console.log(`✅ pnpm version: ${version}`);
        return true;
    } catch (error) {
        console.log('❌ pnpm not found or not working');
        return false;
    }
}

// 检查Node.js版本
function checkNodeVersion() {
    console.log('\n📋 Checking Node.js version...');
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
        console.log(`✅ Node.js version: ${version}`);
        return true;
    } else {
        console.log(`❌ Node.js version: ${version} (requires >= 18)`);
        return false;
    }
}

// 检查构建输出目录
function checkBuildOutputs() {
    console.log('\n🏗️ Checking build outputs...');
    
    const outputs = [
        { path: '../out', name: 'Main IDE output' },
        { path: '../extensions/JoyCoder-Plugin/dist', name: 'JoyCoder Plugin dist' }
    ];
    
    let allFound = true;
    outputs.forEach(output => {
        const outputPath = path.join(__dirname, output.path);
        if (fs.existsSync(outputPath)) {
            console.log(`✅ ${output.name}`);
        } else {
            console.log(`⚠️ ${output.name} - Not built yet`);
        }
    });
    
    return allFound;
}

// 主函数
function main() {
    const checks = [
        { name: 'Node.js Version', fn: checkNodeVersion },
        { name: 'pnpm Version', fn: checkPnpmVersion },
        { name: 'Main Dependencies', fn: checkMainDependencies },
        { name: 'Plugin Dependencies', fn: checkPluginDependencies },
        { name: 'Build Outputs', fn: checkBuildOutputs }
    ];
    
    let allPassed = true;
    const results = [];
    
    checks.forEach(check => {
        const result = check.fn();
        results.push({ name: check.name, passed: result });
        if (!result) allPassed = false;
    });
    
    console.log('\n📊 Summary:');
    console.log('='.repeat(50));
    results.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`${status} ${result.name}`);
    });
    
    if (allPassed) {
        console.log('\n🎉 All dependencies are properly installed!');
        console.log('You can now run:');
        console.log('  npm run dev     - Start development mode');
        console.log('  npm run build:* - Build for specific platform');
    } else {
        console.log('\n⚠️ Some issues found. Please install missing dependencies.');
        console.log('Run the following commands:');
        console.log('  npm install                              # Install main dependencies');
        console.log('  cd extensions/JoyCoder-Plugin && pnpm install  # Install plugin dependencies');
    }
    
    process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
    main();
}

module.exports = { checkMainDependencies, checkPluginDependencies, checkPnpmVersion, checkNodeVersion };
