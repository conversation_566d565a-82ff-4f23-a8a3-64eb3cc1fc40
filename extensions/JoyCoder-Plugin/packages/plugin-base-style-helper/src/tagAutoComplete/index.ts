import * as vscode from 'vscode';

async function provideCompletionItems(
  document: vscode.TextDocument,
  position: vscode.Position
): Promise<vscode.CompletionItem[]> {
  const items: vscode.CompletionItem[] = [];

  const inputTextReg = /([a-zA-Z]+)\_(\w{1,})/;
  const line = document.lineAt(position);
  // 当前行光标之前的字符
  const linePrefix = line.text.slice(0, position.character);

  const matches = linePrefix.match(inputTextReg);
  if (!matches) return [];

  const tag = matches[1];
  const TagString = `<${tag} className={styles.${matches[2]}}>${'${1}'}</${tag}>`;

  const completionItem = new vscode.CompletionItem(`${matches[0]}`, vscode.CompletionItemKind.Text);

  completionItem.detail = 'JoyCode';
  completionItem.insertText = new vscode.SnippetString(TagString);

  completionItem.command = {
    title: 'insertCss',
    command: 'JoyCode.insertCssByClassName',
    arguments: [matches[2], 6],
  };

  items.push(completionItem);
  return items;
}

export const tagAutoComplete = vscode.languages.registerCompletionItemProvider(
  ['javascript', 'javascriptreact', 'typescript', 'typescriptreact'],
  {
    provideCompletionItems,
  },
  ''
);
