import * as vscode from 'vscode';
import { transform } from './utils';
import { reportRd } from '@joycoder/shared';

export const cssModuleTransform = vscode.commands.registerCommand('JoyCode.cssModuleTransform', async () => {
  // Get the active text editor
  const editor = vscode.window.activeTextEditor;
  const Selection = vscode.Selection;
  const Position = vscode.Position;

  if (editor) {
    const document = editor.document;
    const selection = editor.selection;

    // Get the word within the selection
    const word = document.getText(selection);

    reportRd(9);

    // 如果没有选中文本,则默认选中当前行
    if (word === '') {
      const line = selection.active.line;
      const lineSelection = new Selection(new Position(line, 0), new Position(line + 1, 0));
      const lineWord = document.getText(lineSelection);
      const reversed = transform(lineWord);

      editor.edit((editBuilder) => {
        editBuilder.replace(lineSelection, reversed);
      });

      return;
    }

    const reversed = transform(word);

    editor.edit((editBuilder) => {
      editBuilder.replace(selection, reversed);
    });
  }
});
