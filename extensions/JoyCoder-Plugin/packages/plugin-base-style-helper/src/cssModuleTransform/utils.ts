import { getVscodeConfig } from '@joycoder/shared';

/** 去除首尾空格以及中间多余空格 */
export function trim(str: string) {
  return str.trim().replace(/\s+/g, ' ');
}

/** 匹配单词前缀 */
export function matchPrefix(ignorePrefix: string | string[], word: string): boolean {
  if (typeof ignorePrefix === 'string') {
    return !!ignorePrefix && new RegExp(`^${ignorePrefix}`).test(word);
  } else {
    return !!ignorePrefix.filter((n) => !!n && new RegExp(`^${n}`).test(word)).length;
  }
}

/** 获取括号 */
function getBrackets(str: string, express = '=') {
  return express === '=' ? str : '';
}

/**
 * 正则匹配转换
 * @param str 选中文本
 * @param conf 配置
 */
export function transform(str: string) {
  const styleVariable = 'styles';
  const ignorePrefix = getVscodeConfig('JoyCode.config.cssModuleTransformIgnorePrefix', '');

  const className = /className/.test(str) ? 'className' : 'class';
  const reg = new RegExp(`(${className})\\s*([=:])\\s*['"]([\\w\\s-]+)['"]`, 'g');

  return str.replace(reg, (_, $1, $2, $3) => {
    let re = '';
    let arr: any = [];
    const className = $1;
    const express = $2;
    const value = $3;

    if (trim(value) === '') {
      return `${className}${express}""`;
    }

    arr = trim(value).split(' ');

    // 单个类名的情况
    if (arr.length === 1) {
      if (matchPrefix(ignorePrefix, arr[0])) {
        re = `${className}${express}"${arr[0]}"`;
      } else if (/-/.test(arr[0])) {
        re = `${className}${express}${getBrackets('{', express)}${styleVariable}["${arr[0]}"]${getBrackets(
          '}',
          express
        )}`;
      } else {
        re = `${className}${express}${getBrackets('{', express)}${styleVariable}.${arr[0]}${getBrackets('}', express)}`;
      }

      return re;
    }

    // 多个类名的情况
    let isAllIgnore = true;
    const str = arr
      .map((item: string) => {
        if (matchPrefix(ignorePrefix, item)) {
          return item;
        } else if (/-/.test(item)) {
          isAllIgnore = false;
          return `\${${styleVariable}["${item}"]}`;
        } else {
          isAllIgnore = false;
          return `\${${styleVariable}.${item}}`;
        }
      })
      .join(' ');

    if (isAllIgnore) {
      re = `${className}${express}"${str}"`;
    } else {
      re = `${className}${express}${getBrackets('{', express)}\`${str}\`${getBrackets('}', express)}`;
    }

    return re;
  });
}
