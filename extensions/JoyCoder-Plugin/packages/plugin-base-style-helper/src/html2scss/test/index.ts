import HtmlConverterService from '../converter';

const htmlConverterService = new HtmlConverterService({
  reduceSiblings: true,
  combineParents: true,
  hideTags: true,
  convertBEM: true,
  preappendHtml: false, // 粘贴时是否注入html代码
});

const code = `
<View className="coupon">
  <View className="coupon__left">
    <View className="coupon__left-xx"></View>
  </View>
  <View className="coupon__right"></View>
</View>
`;

const result = htmlConverterService.convert(code, 'scss');

console.log(result);

// 运行测试脚本：ts-node ./packages/plugin-base-style-helper/src/html2scss/test/index.ts
