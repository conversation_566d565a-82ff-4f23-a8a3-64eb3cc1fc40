import * as vscode from 'vscode';
import { reportRd, getLastAcitveTextEditor, Logger } from '@joycoder/shared';
import HtmlConverterService from './converter';
import { serializeError } from 'serialize-error';

export const html2scss = vscode.commands.registerCommand('JoyCode.html2scss', async () => {
  const editor = vscode.window.activeTextEditor || getLastAcitveTextEditor();
  if (!editor) return Logger.showErrorMessage('请先在编辑器中选中要插入的位置~');

  const clipboardText = await vscode.env.clipboard.readText();
  if (!clipboardText) return Logger.showErrorMessage('请先复制HTML节点代码~');

  const htmlConverterService = new HtmlConverterService({
    reduceSiblings: true,
    combineParents: true,
    hideTags: true,
    convertBEM: true,
    preappendHtml: false, // 粘贴时是否注入html代码
  });

  if (htmlConverterService.isStringHtml(clipboardText)) {
    try {
      const filePath = editor.document.fileName.toLowerCase();
      const fileExtension = htmlConverterService.getFileExtension(filePath);
      const selection: vscode.Selection = editor.selection;
      const codePosition = selection.start;
      const scssString = htmlConverterService.convert(clipboardText, fileExtension);
      await editor.insertSnippet(new vscode.SnippetString(scssString), codePosition);
    } catch (error) {
      Logger.showErrorMessage('HTML转CSS失败~' + JSON.stringify(serializeError(error)));
    }
  } else {
    Logger.showErrorMessage('剪切板中不是有效的HTML节点代码~');
  }

  reportRd(30);
});
