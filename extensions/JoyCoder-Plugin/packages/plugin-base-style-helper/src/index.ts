import * as vscode from 'vscode';
import { cssDefinition } from './cssDefinition';
import { cssModuleTransform } from './cssModuleTransform';
import { insertCssByClassName } from './insertCssByClassName';
import { tagAutoComplete } from './tagAutoComplete';
import { html2scss } from './html2scss';

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(cssModuleTransform);
  context.subscriptions.push(insertCssByClassName);
  context.subscriptions.push(tagAutoComplete);
  context.subscriptions.push(html2scss);
  cssDefinition(context);
}
