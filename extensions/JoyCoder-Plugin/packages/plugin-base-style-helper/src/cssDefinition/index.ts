import * as vscode from 'vscode';
import { findStyle, IStylePosition } from './findStyle';
import { findStyleDependencies } from './findStyleDependencies';
import findStyleSelectors from './findStyleSelectors';
import { reportRd, getFocusCodeInfo } from '@joycoder/shared';

const SUPPORT_LANGUAGES = ['javascript', 'javascriptreact', 'typescript', 'typescriptreact'];

// 点击跳转到css代码
function provideDefinition(document: vscode.TextDocument, position: vscode.Position) {
  const { line, word, fileName, directory } = getFocusCodeInfo(document, position);

  if (!/style|className|class/g.test(line.text)) return;

  const matched = findStyle(directory, word, findStyleDependencies(fileName).styleDependencies);
  if (matched) {
    const matchedPosition: IStylePosition = matched.position;
    return new vscode.Location(
      vscode.Uri.file(matched.file),
      new vscode.Position(matchedPosition.start.line - 1, matchedPosition.start.column - 1)
    );
  }
}

// 悬浮提示css
function provideHover(document: vscode.TextDocument, position: vscode.Position) {
  const { line, word, fileName, directory } = getFocusCodeInfo(document, position);

  if (!/style|className|class/g.test(line.text)) return;

  const matched = findStyle(directory, word, findStyleDependencies(fileName).styleDependencies);
  if (matched) {
    reportRd(4);
    return new vscode.Hover(`**JoyCode** \n \`\`\`css \n ${matched.code} \n \`\`\`\``);
  }
}

// 类名自动补全
function provideCompletionItems(document: vscode.TextDocument, position: vscode.Position) {
  const { line, fileName, directory } = getFocusCodeInfo(document, position);
  if (!/style|className|class/g.test(line.text)) return;

  // In case of cursor shaking
  const word = line.text.substring(0, position.character);
  const styleDependencies = findStyleDependencies(fileName).styleDependencies;

  for (let i = 0, l = styleDependencies.length; i < l; i++) {
    if (
      // className=xxx in vue is class=xxx
      /className=/.test(line.text) ||
      /class=/.test(line.text) ||
      // style={styles.xxx}
      (styleDependencies[i].identifier && new RegExp(`${styleDependencies[i].identifier}\\.$`).test(word))
    ) {
      const items = findStyleSelectors(directory, styleDependencies).map((selector: string) => {
        // 当使用 styles.xxx时，移除类选择器 `.`
        const completionItem = new vscode.CompletionItem(selector.replace('.', ''), vscode.CompletionItemKind.Variable);
        completionItem.detail = 'JoyCode';
        completionItem.command = {
          title: 'reportRd',
          command: 'JoyCode.reportRd',
          arguments: [6],
        };
        return completionItem;
      });
      return items;
    }
  }
}

export function cssDefinition(context: vscode.ExtensionContext) {
  context.subscriptions.push(vscode.languages.registerDefinitionProvider(SUPPORT_LANGUAGES, { provideDefinition }));

  SUPPORT_LANGUAGES.forEach((language) => {
    context.subscriptions.push(vscode.languages.registerHoverProvider(language, { provideHover }));

    context.subscriptions.push(
      vscode.languages.registerCompletionItemProvider(language, { provideCompletionItems }, '.', '"', "'", ' ')
    );
  });
}
