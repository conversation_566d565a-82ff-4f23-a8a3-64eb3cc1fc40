import * as fs from 'fs';
import * as babelParser from '@babel/parser';
import traverse from '@babel/traverse';

const supportFiles = ['css', 'scss', 'sass', 'less'];
let _lastImportDeclaration: any = {};

// import styles from './xxx.css'; -> { source: './xxx.css', identifier: 'styles' }
// import './xxx.css'; -> { source: './xxx.css', identifier: null }
export interface IStyleDependency {
  source: string;
  identifier: string | null;
}

// 查找依赖的样式文件，例如import style form './index.css';
export function findStyleDependencies(file: string) {
  const styleDependencies: IStyleDependency[] = [];

  try {
    _lastImportDeclaration = {};

    const ast = babelParser.parse(fs.readFileSync(file, 'utf-8'), {
      plugins: ['typescript', 'jsx'],
      sourceType: 'module',
    });

    // @ts-ignore
    traverse(ast, {
      ImportDeclaration(path) {
        const { node } = path;
        if (
          new RegExp(`${supportFiles.map((supportFile) => `\\.${supportFile}$`).join('|')}`, 'i').test(
            node.source.value
          )
        ) {
          styleDependencies.push({
            source: node.source.value,
            identifier: node.specifiers[0] ? node.specifiers[0].local.name : null,
          });
        } else {
          // 最后一个import语句的位置，用于插入代码
          _lastImportDeclaration = path.node;
        }
      },
    });
  } catch (e) {
    console.error(e);
  }

  return {
    styleDependencies,
    lastImportDeclaration: _lastImportDeclaration,
  };
}
