import * as fs from 'fs';
import * as path from 'path';
import * as css from 'css';
import flatten from 'css-flatten';
import strip from 'strip-comments';
import { IStyleDependency } from './findStyleDependencies';

// https://www.npmjs.com/package/css
export interface IStylePosition {
  start: {
    line: number;
    column: number;
  };
  end: {
    line: number;
    column: number;
  };
}

export interface IStyle {
  type: string;
  selectors: string[];
  position: IStylePosition;
  file: string;
  code: string;
}

export function findStyle(
  directory: string,
  className: string,
  styleDependencies: IStyleDependency[] = []
): IStyle | undefined {
  let matched: any = null;

  for (let i = 0, l = styleDependencies.length; i < l; i++) {
    const file = path.join(directory, styleDependencies[i].source);
    const fileContent = fs.readFileSync(file, 'utf-8');
    let cssContent = fileContent;

    // 移除 media 和 keyframes, 会导致css.parse报错
    cssContent = cssContent.replace(/@[media|keyframes][^{]+\{([\s\S]+?})\s*}/g, '');

    // 打平SASS或LESS
    if (/s(c|a)ss$|\.less$/.test(file)) {
      cssContent = cssContent.replace(/{\n}/g, '{//placeholder \n}'); // 防止自动补全的空标签被flatten掉
      cssContent = flatten(cssContent);
    }

    const { stylesheet } = css.parse(strip(cssContent));
    if (stylesheet) {
      matched = stylesheet.rules.find(
        (rule: any) => rule.selectors && rule.selectors.find((selector: any) => selector.endsWith(className))
      );
    }

    if (matched) {
      matched.file = file;
      matched.code = css.stringify({
        type: 'stylesheet',
        stylesheet: { rules: [matched] },
      });
      break;
    }
  }

  return matched;
}
