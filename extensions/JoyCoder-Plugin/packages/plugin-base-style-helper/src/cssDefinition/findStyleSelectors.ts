import * as fs from 'fs';
import * as path from 'path';
import * as css from 'css';
import flatten from 'css-flatten';
import strip from 'strip-comments';
import { IStyleDependency } from './findStyleDependencies';

export default function findStyleSelectors(directory: string, styleDependencies: IStyleDependency[] = []): string[] {
  let selectors: string[] = [];

  for (let i = 0, l = styleDependencies.length; i < l; i++) {
    const file = path.join(directory, styleDependencies[i].source);

    const fileContent = fs.readFileSync(file, 'utf-8');
    let cssContent = fileContent;

    // 移除 media 和 keyframes, 会导致css.parse报错
    cssContent = cssContent.replace(/@[media|keyframes][^{]+\{([\s\S]+?})\s*}/g, '');

    if (/s(c|a)ss$|\.less$/.test(file)) {
      cssContent = cssContent.replace(/{\n}/g, '{//placeholder \n}'); // 防止自动补全的空标签被flatten掉
      cssContent = flatten(cssContent);
    }

    const { stylesheet } = css.parse(strip(cssContent));

    if (stylesheet) {
      stylesheet.rules.forEach((rule: any) => {
        if (rule.selectors) {
          selectors = selectors.concat(
            rule.selectors.map((selector: any) => {
              // .foo .bar => .bar
              return selector.split(' ').pop() || '';
            })
          );
        }
      });
    }
  }

  return selectors;
}
