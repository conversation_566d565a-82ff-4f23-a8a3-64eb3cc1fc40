import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as fse from 'fs-extra';
import { reportRd } from '@joycoder/shared';
import { findStyleDependencies } from '../cssDefinition/findStyleDependencies';
import findStyleSelectors from '../cssDefinition/findStyleSelectors';

export const insertCssByClassName = vscode.commands.registerCommand(
  'JoyCode.insertCssByClassName',
  async (className, rd) => {
    reportRd(rd);

    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const fileName = editor.document.fileName;
    const { styleDependencies, lastImportDeclaration } = findStyleDependencies(fileName);
    const fileDir = path.dirname(fileName);
    const jsFileName = path.parse(fileName).name;
    const cssFileName = `${jsFileName}.module.scss`;
    const cssFilePath = path.join(fileDir, `./${cssFileName}`);

    // 没有引入css文件时自动创建css文件并自动引入
    if (!styleDependencies.length) {
      fse.ensureFileSync(cssFilePath);

      const position = new vscode.Position(lastImportDeclaration?.loc?.start?.line || 0, 0);
      await editor.insertSnippet(new vscode.SnippetString(`import styles from './${cssFileName}'\n`), position);
    }

    // 没有对应的css选择器时自动追加
    const styles = findStyleSelectors(fileDir, styleDependencies) || [];
    const selector = `.${className}`;
    if (!styles.includes(selector)) {
      fs.appendFileSync(cssFilePath, `${selector} {\n}\n`);
    }
  }
);
