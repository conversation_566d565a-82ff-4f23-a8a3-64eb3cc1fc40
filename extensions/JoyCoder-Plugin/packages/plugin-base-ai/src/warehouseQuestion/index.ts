import * as vscode from 'vscode';
import * as path from 'path';
import * as fsPromise from 'fs/promises';
import * as fs from 'fs';
import { getRepoName, Logger, sleep } from '@joycoder/shared';
import { defaultIgnoreDir, defaultIgnoreFile, defaultIgnoreFileList } from '@joycoder/shared';
import { formatTree, getDirectoryTree } from './tree';

export interface FileTreeNode {
  filepath?: string;
  content?: string;
  fileLanguage?: string;
  // 文件名称
  fileName: string;
  // 文件路径
  filePath: string;
  // 文件内容
  fileContent: string;
  // 目录路径
  folder?: string;
  // 标签-展示在列表选择的
  label?: string;
  // 类型：fileContext、folderContext
  type?: string | 'fileContent' | 'folderContext';
}

/**
 * WarehouseQAOfFiles 类用于处理文件和文件夹的操作。
 */
class WarehouseQAOfFiles {
  private rootPath = vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
  private openFilePath;
  private abortController: AbortController;
  constructor(rootPath?: string) {
    this.rootPath = rootPath ?? this.rootPath;
    this.openFilePath = vscode.window.activeTextEditor?.document?.uri;
  }
  /**
   * 异步搜索文件名
   * @param uris 文件URI数组
   * @param searchTerm 搜索关键词
   * @returns 匹配文件节点数组
   */
  async searchFilesByName(searchTerm: string): Promise<any> {
    // this.abortController && !this.abortController.signal.aborted && this.abortController.abort();
    // this.abortController = new AbortController();
    // // const ragApi = new RAGApiClient();
    // const workspaceName = path.basename(this.rootPath);
    // const repoName = (await getRepoName(this.rootPath)) ?? workspaceName;
    // // const results = (await ragApi.searchFiles(searchTerm, repoName, this.abortController)) ?? [];
    // const directoryFiles = results.map((item) => {
    //   const filePath = item.filepath ?? '';
    //   const fileName = path.basename(filePath);
    //   return {
    //     ...item,
    //     label: `@${fileName}`,
    //     fileName,
    //     filePath: filePath.replace(this.rootPath, '@'),
    //     fileContent: item.content,
    //     type: 'fileContext',
    //   };
    // });
    // return directoryFiles;
  }
  /**
   * 获取当前打开文件及其所在文件夹的文件列表。
   *
   * @returns {Promise<FileTreeNode[]>} 文件列表。
   */
  async getFilesOfOpenFileAndSameFolder() {
    try {
      const files: FileTreeNode[] = [];
      const openFiles = this.getOpenFiles() ?? [];
      if (this.openFilePath) {
        const folderPath = path.dirname(this.openFilePath.fsPath);

        const openFileFiles = await this.getDirectoryTreeWithTimeout([vscode.Uri.parse(folderPath)]);
        files.unshift(...openFileFiles);
      }
      if (this.rootPath) {
        const sameFolderFiles = await this.getSameFolderFiles(this.rootPath);
        files.push(...sameFolderFiles);
      }
      files.unshift(...openFiles);

      const filePathMap = new Map<string, 1>();
      return files
        .map((file) => ({
          ...file,
          filepath: file.filePath,
          filePath: file.filePath.replace(this.rootPath, '@'),
          label: '@' + file.fileName,
          type: 'fileContext',
        }))
        .filter((file) => {
          if (filePathMap.get(file.filePath)) return false;
          filePathMap.set(file.filePath, 1);
          return true;
        });
    } catch (error) {
      return [];
    }
  }

  async getDirectoryTreeWithTimeout(folderPath) {
    // 执行getDirectoryTree操作的Promise
    const getDirectoryTreePromise = this.getDirectoryTree([vscode.Uri.parse(folderPath)]);

    // 使用Promise.race来竞赛两个Promise
    const openFileFiles = await Promise.race([getDirectoryTreePromise, sleep(2000)]);

    return openFileFiles || [];
  }

  /**
   * 获取当前打开的文件信息。
   * @returns 包含文件名、文件路径和内容的对象数组。
   * @throws 如果获取文件信息时发生错误，返回空数组。
   */
  getOpenFiles() {
    try {
      const openFiles: FileTreeNode[] = vscode.workspace.textDocuments
        .filter((doc) => {
          if (!doc.uri.fsPath.startsWith(this.rootPath)) return false;
          const activeEditor = vscode.window.activeTextEditor;
          const targetFileName = path.basename(doc.fileName);
          const isIgnoreFile =
            defaultIgnoreFile.ignores(targetFileName) || defaultIgnoreFileList.includes(targetFileName);
          if (activeEditor) {
            const fileName = activeEditor.document.fileName;
            // const fileType = '.' + fileName.split('.').pop();
            return doc.fileName !== fileName && !isIgnoreFile;
          }
          return !isIgnoreFile;
        })
        .map((doc) => {
          return {
            fileName: path.basename(doc.fileName),
            filePath: doc.uri.fsPath,
            fileContent: doc.getText(),
            type: 'fileContext',
          };
        });
      return openFiles;
    } catch (error) {
      return [];
    }
  }

  /**
   * 异步获取指定文件夹中的文件列表。
   * @param folderPath - 文件夹路径。
   * @returns 文件名数组。
   * @throws 如果读取文件夹失败，则抛出错误。
   */
  async getSameFolderFiles(folderPath: string): Promise<FileTreeNode[]> {
    let fileList: FileTreeNode[] = [];
    try {
      const entries = await fsPromise.readdir(folderPath, { withFileTypes: true });
      const files = entries.filter((entry) => !entry.isDirectory()).map((entry) => entry.name);
      if (files.length > 0) {
        fileList = await this.getFileList(files, folderPath);
      }
      return fileList;
    } catch (error) {
      return fileList;
    }
  }

  async getFileList(files: string[], folderPath: string) {
    try {
      let filesList = files;
      filesList = files.filter((fileName) => {
        const targetFileName = path.basename(fileName);
        const isIgnoreFile =
          defaultIgnoreFile.ignores(targetFileName) || defaultIgnoreFileList.includes(targetFileName);
        return !isIgnoreFile;
      });
      const fileList = await Promise.all(
        filesList.map(async (fileName) => {
          const filePath = path.join(folderPath, fileName);
          try {
            const fileContent = await fsPromise.readFile(filePath, 'utf8');
            return { fileName, fileContent, filePath };
          } catch (err) {
            return { fileName, fileContent: '', filePath };
          }
        })
      );
      return fileList;
    } catch (error) {
      return [];
    }
  }
  /**
   * 异步获取指定文件夹中包含特定名称的文件列表。
   * @param fileName - 要匹配的文件名。
   * @param folderPath - 文件夹路径。
   * @returns 包含匹配文件名的文件列表。
   * @throws 如果读取文件夹失败，则抛出错误。
   */
  async getFilesByName(fileName, folderPath) {
    try {
      folderPath = folderPath ?? this.rootPath;
      const files = await fsPromise.readdir(folderPath);
      const matchingFiles = files.filter((file) => file.includes(fileName));
      return matchingFiles;
    } catch (error) {
      throw error;
    }
  }

  async readWorkspaceFolders(): Promise<FileTreeNode[]> {
    const folderTreeNodes: FileTreeNode[] = [];
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
      return folderTreeNodes;
    }
    for (const workspaceFolder of workspaceFolders) {
      const workspaceFolderPath = workspaceFolder.uri;
      const entries = await vscode.workspace.fs.readDirectory(workspaceFolderPath);

      for (const [entryName, entryType] of entries) {
        if (entryType === vscode.FileType.Directory && !defaultIgnoreDir.ignores(entryName)) {
          const folderPath = workspaceFolderPath.with({ path: `${workspaceFolderPath.path}/${entryName}` }).fsPath;
          const relativeFolderPath = folderPath.replace(this.rootPath, '');
          folderTreeNodes.push({
            label: `@${relativeFolderPath}`,
            fileName: entryName,
            filePath: folderPath,
            fileContent: '',
            folder: folderPath,
            type: 'folderContext',
          });
        }
      }
    }
    return folderTreeNodes;
  }

  /**
   * 异步获取目录树。
   *
   * @param uris - 要读取的文件或目录的URI数组。
   * @returns 包含文件树节点的Promise数组。
   */
  async getDirectoryTree(uris: vscode.Uri[]): Promise<FileTreeNode[]> {
    const result: FileTreeNode[] = [];
    try {
      const readFile = async (fileUri: vscode.Uri): Promise<FileTreeNode> => {
        let filePath = fileUri.fsPath;
        const folderPath = path.dirname(fileUri.fsPath);
        const fileName = path.basename(fileUri.fsPath);

        let fileContent = '';
        try {
          fileContent = await fsPromise.readFile(filePath, 'utf8');
          filePath = filePath.replace(this.rootPath, '@');
        } catch (error) {
          Logger.error(`Error reading file ${filePath}:`, error);
        }
        return {
          fileName,
          filePath,
          fileContent,
          folder: folderPath,
        };
      };

      const readDirectory = async (dirUri: vscode.Uri) => {
        const entries = await vscode.workspace.fs.readDirectory(dirUri);
        for (const [name, type] of entries) {
          const entryUri = vscode.Uri.joinPath(dirUri, name);
          if (type === vscode.FileType.File) {
            result.push(await readFile(entryUri));
          } else if (type === vscode.FileType.Directory) {
            await readDirectory(entryUri);
          }
        }
      };

      for (const uri of uris) {
        const fileStat = await vscode.workspace.fs.stat(uri);
        if (fileStat.type === vscode.FileType.File) {
          result.push(await readFile(uri));
        } else if (fileStat.type === vscode.FileType.Directory) {
          await readDirectory(uri);
        }
      }
    } catch (error) {
      console.error(error);
    }

    return result;
  }
  async retriveFolderWithQuestion(folderPath: string, textQuestion: string): Promise<string> {
    try {
      let str = '';
      const tree = await getDirectoryTree(folderPath);
      const treeString = formatTree(tree);
      if (treeString) {
        str += `\n目录结构如下:\n${treeString}\n`;
      }
      // TODO: 必须先将用户输入的内容分词，否则BM25的效果很差！
      if (textQuestion.split(' ').length >= 5) return str;
      // let results = await retrieveFts(textQuestion, 5, { filterDirectory: folderPath });
      // const pathResults = await retrieveFts(textQuestion, 5, { isRetrievePath: true, filterDirectory: folderPath });
      // results = results.concat(pathResults);
      // const fileContentString = results
      //   .map((file) => {
      //     const fileContent = file.content;
      //     const filePath = file.filepath.replace(this.rootPath, '@');
      //     const fileName = path.basename(file.filepath);
      //     return this.getContent({
      //       fileName,
      //       filePath,
      //       fileContent,
      //     });
      //   })
      //   .filter(Boolean)
      //   .join('\n\n');
      // if (fileContentString) {
      //   str += `n目录内部分文件如下:\n${fileContentString}\n`;
      // }
      return str;
    } catch (error) {
      return '';
    }
  }

  async retriveFilesContent(selectedFileList: FileTreeNode[]) {
    const filesContentPromises = selectedFileList.map(async (file) => {
      try {
        if (file.fileContent) {
          return file.fileContent;
        }
        if (file.filepath) file.fileContent = await fsPromise.readFile(file.filepath, 'utf8');
        return this.getContent(file);
      } catch (error) {
        return this.getContent(file);
      }
    });

    const filesContents = await Promise.all(filesContentPromises);
    return filesContents.join('\n\n');
  }

  getContent(file: FileTreeNode) {
    const path = file.filepath || file.label || file.fileName;
    let str = '';
    if (path) {
      str += `\n文件路径：${file.filepath || file.label || file.fileName}\n`;
    }
    const content = file.fileContent;
    if (content) {
      str += `文件内容：${file.fileContent}\n`;
    }
    return str;
  }

  async retrieveTextQuestion(textQuestion: string) {
    // TODO: 必须先将用户输入的内容分词，否则BM25的效果很差！
    if (textQuestion.split(' ').length >= 5) return '';
    // const results = await retrieveFts(textQuestion, 5);
    // const pathResults = await retrieveFts(textQuestion, 5, { isRetrievePath: true });
    // results.concat(pathResults);
    // return results
    //   .map((file) => {
    //     const fileContent = file.content;
    //     const filePath = file.filepath.replace(this.rootPath, '@');
    //     const fileName = path.basename(file.filepath);
    //     return this.getContent({
    //       fileName,
    //       filePath,
    //       fileContent,
    //     });
    //   })
    //   .filter(Boolean)
    //   .join('\n\n');
  }
  getAllPossibleDirectories(input: string, prefix: string): string[] {
    try {
      const directories: string[] = [];
      const parts = input.split(path.sep);

      let currentPath = '';
      let latestCurrentPath = '';
      parts.some((part) => {
        if (part) {
          if (!currentPath) currentPath = path.resolve(`${part}${path.sep}`);
          else currentPath = path.resolve(currentPath, part);
          if (latestCurrentPath) {
            const stats = fs.statSync(currentPath);
            if (stats.isDirectory()) {
              directories.push(currentPath);
            }
            return true;
          }
          if (currentPath.indexOf(prefix) >= 0) {
            directories.push(currentPath);
            latestCurrentPath = currentPath;
          }
        }
      });

      return directories;
    } catch (error) {
      console.error(error);
      return [];
    }
  }
}

export default WarehouseQAOfFiles;
