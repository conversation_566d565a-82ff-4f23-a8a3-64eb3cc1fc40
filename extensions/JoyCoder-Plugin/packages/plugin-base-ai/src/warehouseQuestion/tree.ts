import { defaultIgnoreDir, defaultIgnoreFile, defaultIgnoreFileList } from '@joycoder/shared';
import * as fs from 'fs';
import * as path from 'path';

interface TreeNode {
  name: string;
  path: string;
  children?: TreeNode[];
}

export async function getDirectoryTree(dir: string): Promise<TreeNode> {
  const name = path.basename(dir);
  const tree = { name, path: dir, children: [] as TreeNode[] };

  const items = await fs.promises.readdir(dir, { withFileTypes: true });

  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    if (item.isDirectory()) {
      const isIgnoreDir = defaultIgnoreDir.ignores(item.name);
      if (!isIgnoreDir) {
        const childTree = await getDirectoryTree(fullPath);
        tree.children.push(childTree);
      }
    } else {
      const isIgnoreFile = defaultIgnoreFile.ignores(item.name) || defaultIgnoreFileList.includes(item.name);
      if (!isIgnoreFile) {
        tree.children.push({ name: item.name, path: fullPath });
      }
    }
  }

  return tree;
}

export function formatTree(node: TreeNode, prefix = '', isLast = true): string {
  let treeString = prefix + (isLast ? '└─' : '├─') + node.name + '\n';

  if (node.children) {
    const childPrefix = prefix + (isLast ? '  ' : '│ ');
    node.children.forEach((child, index) => {
      const isLastChild = node.children && index === node.children.length - 1;
      treeString += formatTree(child, childPrefix, isLastChild);
    });
  }

  return treeString;
}
