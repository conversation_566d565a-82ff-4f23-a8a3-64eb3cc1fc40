import { GlobalState, isBusiness } from '@joycoder/shared';
import { v4 as uuidv4 } from 'uuid';
import { GPTTokens } from 'gpt-tokens';
import { ChatModelConfig, getChatModelAndConfig } from '../model';

export const DEFAULT_SYSTEM_MESSAGE = isBusiness()
  ? '你是一个AI编程助手，使用的是京东公司开发的JoyCoder模型，善于回答计算机与编程相关的问题。对于政治敏感问题、安全和隐私问题，你将拒绝回答。请尽可能使用中文回答。'
  : "You are an AI programming assistant, utilizing the JoyCode model, developed by JD Company. Answer in user's language, give concise responses to very simple questions, but provide thorough responses to more complex and open-ended questions. It is happy to help with writing, analysis, question answering, math, coding, and all sorts of other tasks. It uses markdown for coding. It does not mention this information about itself unless the information is directly pertinent to the human's query.";

export interface TextMessage {
  type: 'text';
  text: string;
}
export interface ImageMessage {
  type: 'image_url';
  image_url: string | { url: string; detail: string };
}

export type MessageContent = TextMessage | ImageMessage;

export interface MessageItem {
  role: 'system' | 'assistant' | 'user';
  content: string | MessageContent[];
  extendMsg?: Record<string, any>;
}

const ONE_MONTH = 3600 * 24 * 1000 * 30;

/**
 * 批量获取聊天历史
 *
 * @export
 * @param {{ idList: string[] }} params
 * @param {(res: MessageItem[]) => void} callback
 * @return {*}
 */
export default async function getBatchChatHistory(
  params: { idList: string[] },
  callback: (res: MessageItem[]) => void
) {
  const { idList } = params;
  if (!idList || !idList.length) {
    callback([]);
    return;
  }
  const task = idList.map((id: string) => getChatHistory({ conversationId: id }));
  const res = await Promise.all(task);
  callback(res.flat());
}

async function getChatHistory(params) {
  const list: MessageItem[] = getHistoryMessageList(params.conversationId) || [];
  if (!list.length) {
    return [];
  }
  return list;
}

const HISTORY_STORAGE_KEY = 'joycoder_business_chat_history';

function getHistoryStorage() {
  let storage = GlobalState.get(HISTORY_STORAGE_KEY);
  if (!storage) {
    storage = {};
    GlobalState.update(HISTORY_STORAGE_KEY, storage);
  }
  return storage;
}

function setHistoryStorage(storage) {
  GlobalState.update(HISTORY_STORAGE_KEY, storage);
  return storage;
}

/**
 * 读取历史消息列表
 *
 * @export
 * @param {string} conversationId
 * @param {string} [parentMessageId]
 * @return {*}
 */
export function getHistoryMessageList(conversationId: string, parentMessageId?: string) {
  const { list = [] } = getHistoryStorage()[conversationId] || {};
  if (parentMessageId) {
    let removeIndex = list.length - 1;
    for (let i = list.length - 1; i > 0; i--) {
      if (list[i].extendMsg?.parentMessageId === parentMessageId) {
        removeIndex = i;
        break;
      }
    }
    if (removeIndex > 0 && removeIndex < list.length - 1) {
      // 删除removeIndex及之后的所有元素
      list.splice(removeIndex, list.length - removeIndex);
    }
  }
  return list;
}

/**
 * 更新内存中的历史消息
 *
 * @export
 * @param {string} conversationId
 * @param {Message} message
 */
export function setHistoryMessage({
  message,
  conversationId,
  modelConfig,
}: {
  message: MessageItem;
  conversationId: string;
  modelConfig: ChatModelConfig;
}) {
  message.extendMsg = message.extendMsg || {};
  message.extendMsg.model = modelConfig.label || getChatModelAndConfig().label;
  const storage = getHistoryStorage();
  storage[conversationId] = storage[conversationId] || { exp: Date.now() + ONE_MONTH };
  let { list } = storage[conversationId];
  if (!list) {
    list = [message];
  } else {
    // 补充parentMessageId
    const parentMessageId = message.extendMsg?.parentMessageId || list[list.length - 1]?.extendMsg?.id;
    if (parentMessageId) {
      message.extendMsg.parentMessageId = parentMessageId;
    }
    list.push(message);
  }
  storage[conversationId].list = list;
  setHistoryStorage(storage);
  return storage[conversationId].list;
}

/**
 * 清除过期的历史消息
 *
 */
function clearExpConversationStorage() {
  const storage = getHistoryStorage();
  Object.keys(storage).forEach((conversationId) => {
    if (storage[conversationId].exp < Date.now()) {
      delete storage[conversationId];
    }
  });
  setHistoryStorage(storage);
}

/**
 * 构建消息列表
 *
 * @export
 * @param {{
 *   message: string;
 *   systemMessage: string;
 *   conversationId: string;
 *   parentMessageId: string;
 *   respMaxTokens: number;
 *   maxTotalTokens?: number;
 * }} {
 *   message,
 *   systemMessage,
 *   conversationId,
 *   parentMessageId,
 *   respMaxTokens,
 *   maxTotalTokens,
 * }
 * @return {*}  {Message[]}
 */
export function buildMessageList({
  message,
  systemMessage,
  conversationId,
  parentMessageId,
  respMaxTokens,
  maxTotalTokens,
  modelConfig,
  isRepository,
}: {
  message: string | MessageContent[];
  systemMessage: string;
  conversationId: string;
  parentMessageId: string;
  respMaxTokens: number;
  maxTotalTokens?: number;
  isRepository?: boolean;
  modelConfig: ChatModelConfig;
}): MessageItem[] {
  let messageList: MessageItem[] = getHistoryMessageList(conversationId, parentMessageId);
  if (!messageList || !messageList.length) {
    if (systemMessage) {
      if (modelConfig.systemMessage !== '[NONE]') {
        setHistoryMessage({
          conversationId,
          message: {
            role: 'system',
            content: systemMessage,
          },
          modelConfig,
        });
      } else {
        // Claude3兼容
        setHistoryMessage({
          conversationId,
          message: {
            role: 'user',
            content: systemMessage || DEFAULT_SYSTEM_MESSAGE,
            extendMsg: {
              id: uuidv4(),
              conversationId,
            },
          },
          modelConfig,
        });
        setHistoryMessage({
          conversationId,
          message: {
            role: 'assistant',
            content: '明白！',
            extendMsg: {
              id: uuidv4(),
              conversationId,
            },
          },
          modelConfig,
        });
      }
    }
  }
  // 追加用户消息，同时读取最新的messageList
  messageList = setHistoryMessage({
    conversationId,
    message: {
      role: 'user',
      content: message,
      extendMsg: {
        id: uuidv4(),
        conversationId,
        parentMessageId,
      },
    },
    modelConfig,
  });
  if (!!isRepository) return [];
  const isSupportVision = modelConfig.features?.includes('vision');

  messageList = messageList.map((item) => {
    // 不支持视觉时，将图片消息转换为文本
    if (!isSupportVision && Array.isArray(item.content)) {
      return {
        role: item.role,
        content: item.content
          .map((item) => {
            if (item.type === 'image_url') {
              return '[图片]';
            } else {
              return item.text;
            }
          })
          .join('\n'),
      };
    }
    return { role: item.role, content: item.content };
  });

  if (!maxTotalTokens) return messageList;

  let totalTokens = getUsedTokensByMessageList(messageList) + respMaxTokens;

  while (totalTokens >= maxTotalTokens) {
    let removeIndex = 0;
    messageList.some((message, index) => {
      if (message.role !== 'system') {
        removeIndex = index;
        return true;
      }
      return false;
    });
    const removeMessages = messageList.splice(removeIndex, 1);
    totalTokens -= getUsedTokensByMessageList(removeMessages);
  }

  // 每次构建messageList的同时清除过期存储
  setTimeout(clearExpConversationStorage);

  return messageList;
}

function getUsedTokensByMessageList(messageList: MessageItem[]) {
  let imageTokenCount = 0;
  const usageInfo = new GPTTokens({
    model: 'gpt-3.5-turbo-1106',
    messages: messageList.map((item) => {
      if (Array.isArray(item.content)) {
        return {
          ...item,
          content: item.content
            .map((item) => {
              if (item.type === 'image_url') {
                // 参考 https://github.com/songquanpeng/one-api/blob/0acee9a0655aa84a81b346a23ad3ae5b3781fb15/relay/adaptor/openai/token.go#L131
                // TODO: 先简单按每张图片1700个token计算，后续按图片大小计算
                imageTokenCount += 1700;
                return '';
              } else {
                return item.text;
              }
            })
            .join('\n'),
        };
      }
      return {
        ...item,
        content: typeof item.content === 'string' ? item.content : JSON.stringify(item.content),
      };
    }),
  });
  return usageInfo.usedTokens + imageTokenCount;
}
