import { Logger } from '@joycoder/shared';
import { createParser } from 'eventsource-parser';
import fetch from 'node-fetch';

class SSEError extends Error {
  statusCode?: number;
  statusText?: string;
}

export const TIMEOUT_MESSAGE = '[TIMEOUT]';
const DEFAULT_TIMEOUT = 20 * 1000;

/**
 * Node版本sse封装
 *
 * @export
 * @param {string} url
 * @param {(Parameters<typeof fetch>[1] & { onMessage: (data: string) => void })} options
 */
export async function fetchSSE(
  url: string,
  options: Parameters<typeof fetch>[1] & { onMessage: (data: string) => void }
) {
  const timer = setTimeout(() => onMessage(TIMEOUT_MESSAGE), DEFAULT_TIMEOUT);
  const { onMessage, ...fetchOptions } = options;
  const res = await fetch(url, fetchOptions);
  const resContentType = res.headers.get('content-type') || '';
  if ((!res || !res.ok) && resContentType.indexOf('application/json;') >= 0) {
    timer && clearTimeout(timer);
    onMessage(await res.text());
    return;
  }

  const parser = createParser((event) => {
    if (event.type === 'event') {
      timer && clearTimeout(timer);
      if (typeof event.data === 'string' && (/^data:/.test(event.data) || /^data:\s*/.test(event.data))) {
        onMessage(event.data.slice(5).trim());
        return;
      }
      onMessage(event.data);
    }
  });

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  if (!res.body?.getReader) {
    // Vercel polyfills `fetch` with `node-fetch`, which doesn't conform to
    // web standards, so this is a workaround...
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const body: NodeJS.ReadableStream = res.body as any;

    if (!body.on || !body.read) {
      throw new SSEError('unsupported "fetch" implementation');
    }

    body.on('readable', () => {
      let chunk: string | Buffer = '';
      while (null !== (chunk = body.read())) {
        parser.feed(chunk.toString());
      }
    });
    body.on('end', () => {
      Logger.log('end------------------->SSE END');
    });
    body.on('error', () => {
      Logger.log('error------------------->SSE ERR');
    });
  } else {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    for await (const chunk of streamAsyncIterable<BufferSource>(res.body)) {
      const str = new TextDecoder().decode(chunk);
      parser.feed(str);
    }
  }
}

/**
 * stream流递归读取
 *
 * @template T
 * @param {ReadableStream<T>} stream
 */
async function* streamAsyncIterable<T>(stream: ReadableStream<T>) {
  const reader = stream.getReader();
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        return;
      }
      yield value;
    }
  } finally {
    reader.releaseLock();
  }
}
