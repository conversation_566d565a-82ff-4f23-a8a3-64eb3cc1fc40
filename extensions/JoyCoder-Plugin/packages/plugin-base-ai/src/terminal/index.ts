import * as vscode from 'vscode';
import { askChatGPT_simplePrompt } from '../dialog';
import { RightMenuChatType } from '../dialog/constant';
import { Logger } from '@joycoder/shared';

export default function (context) {
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.ai.terminal.explain', async (terminalInfo) => {
      const select = terminalInfo.selection || '';
      if (!select) {
        Logger.showInformationMessage('请先选中要解释的内容~');
        return;
      }
      askChatGPT_simplePrompt(RightMenuChatType.terminalExplain, `以下信息来自Terminal，请帮忙解释一下：\n\n${select}`);
    })
  );
}
