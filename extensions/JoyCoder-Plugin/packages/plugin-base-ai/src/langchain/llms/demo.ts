import { JoyCoderAI } from './index';

export default async function test() {
  const model = new JoyCoderAI();

  // const res = await model.call('你是谁');
  // console.log('joycoderllm|res:', res);

  const res = await model.invoke('你是谁');
  console.log(res);

  // const stream = await model.stream('你是谁');
  // for await (const chunk of stream) {
  //   console.log('joycoderllm|chunk:', chunk);
  // }
}
