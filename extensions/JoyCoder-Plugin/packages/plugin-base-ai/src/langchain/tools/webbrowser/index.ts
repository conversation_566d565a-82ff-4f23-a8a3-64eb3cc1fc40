import { WebBrowser, parseInputs, getText } from 'langchain/tools/webbrowser';
import type { WebBrowserArgs } from 'langchain/tools/webbrowser';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { Document } from '@langchain/core/documents';
import { MemoryVectorStore } from 'langchain/vectorstores/memory';
import { formatDocumentsAsString } from 'langchain/util/document';
import { RunnableSequence } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { getFullHtmlByPlaywright } from '../../document_loaders';

/**
 * 基于playwright的网络爬虫
 * langchain自带的WebBrowser是通过axios实现的，只能爬取直出页面
 */
export class PlaywrightWebBrowser extends WebBrowser {
  static lc_name() {
    return 'PlaywrightWebBrowser';
  }
  // @ts-ignore
  get lc_namespace() {
    // @ts-ignore
    return [...super.lc_namespace, 'playwrightwebbrowser'];
  }

  constructor(options: WebBrowserArgs) {
    super({ ...options });
  }

  /** @ignore */
  async _call(inputs: string, runManager?: CallbackManagerForToolRun) {
    const [baseUrl, task] = parseInputs(inputs);
    const doSummary = !task;

    let text = '';
    try {
      const loaderResult = await getFullHtmlByPlaywright(baseUrl, {
        delay: 1500,
      });
      const html = loaderResult?.[0]?.pageContent ?? '';
      text = getText(html, baseUrl, doSummary);
    } catch (e) {
      if (e) {
        return e.toString();
      }
      return '爬取网页失败';
    }

    // @ts-ignore
    const texts = await this.textSplitter.splitText(text);

    let context = '';
    // if we want a summary grab first 4
    if (doSummary) {
      context = texts.slice(0, 4).join('\n');
    }
    // search term well embed and grab top 4
    else {
      const docs = texts.map(
        (pageContent) =>
          new Document({
            pageContent,
            metadata: [],
          })
      );

      // @ts-ignore
      const vectorStore = await MemoryVectorStore.fromDocuments(docs, this.embeddings);
      const results = await vectorStore.similaritySearch(task, 4, undefined, runManager?.getChild('vectorstore'));
      context = formatDocumentsAsString(results);
    }

    const input = `Text:${context}\n\nI need ${
      doSummary ? 'a summary' : task
    } from the above text, also provide up to 5 markdown links from within that would be of interest (always including URL and text). Links should be provided, if present, in markdown syntax as a list under the heading "Relevant Links:".`;

    // @ts-ignore
    const chain = RunnableSequence.from([this.model, new StringOutputParser()]);
    return chain.invoke(input, runManager?.getChild());
  }
}
