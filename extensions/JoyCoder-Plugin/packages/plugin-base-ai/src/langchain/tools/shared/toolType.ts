export enum AgentToolType {
  BaiDuSearch = 'baiduSearch',
  GoogleSearch = 'googleSearch',
  GoogleAPI = 'googleApi',
  TavilyAPI = 'tavilyApi',
  DuckDuckGoSearch = 'duckduckgoSearch',
  TaroSearch = 'taroSearch',
  SerpAPIBaiDu = 'serpApiBaidu',
  SerpAPIGoogle = 'serpApiGoogle',
  SerpAPIDuckDuckGo = 'serpApiDuckDuckGo',
  WebBrowser = 'webBrowser',
  PlaywrightWebBrowser = 'playwrightWebBrowser',
  Knowledge = 'knowledge',
  MultiAgent = 'multiAgent', //多种工具，自动调用
  D2C = 'd2c',
}

export enum AgentSceneType {
  CustomHelper = 'customhelper',
  Pic2code = 'pic2code',
  D2C = 'd2c',
  Search = 'search2llm',
  Webcrawler = 'webcrawler',
  Knowledge = 'knowledge',
  Text2chart = 'text2chart',
  MultiAgent = 'multiAgent',
}
