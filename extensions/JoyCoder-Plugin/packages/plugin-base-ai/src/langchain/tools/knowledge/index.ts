import { Tool } from '@langchain/core/tools';
import { AgentToolType } from '../shared/toolType';
import { searchUserKnowledge, KnowledgeDocType, KnowledgeType } from '@joycoder/plugin-base-ai/src/knowledge/common';
import { Logger, getRemoteConfigSync, getVscodeConfig } from '@joycoder/shared';

export class KnowledgeQA extends Tool {
  name = AgentToolType.Knowledge;
  knowledgeId = '';
  source = KnowledgeDocType.all as string;

  constructor(
    fields = {
      type: KnowledgeType.user,
      doc: KnowledgeDocType.all as string,
    }
  ) {
    // eslint-disable-next-line prefer-rest-params
    super(...arguments);

    if (fields.doc !== KnowledgeDocType.all) {
      this.source = fields.doc;
    }
    if (fields.type !== KnowledgeType.user) {
      this.knowledgeId = fields.type;
    }
  }

  /** @ignore */
  async _call(input: string) {
    const searchParams = {
      knowledgeId: this.knowledgeId !== KnowledgeType.user ? this.knowledgeId : '',
      question: input,
      ...getVscodeConfig('JoyCode.config.chatKnowledge.options'),
      filter: '',
    };
    if (this.source && this.source !== KnowledgeDocType.all) {
      searchParams.filter = JSON.stringify([
        {
          term: {
            mark: ['knowledge'],
          },
        },
        {
          term: {
            source: this.source,
          },
        },
      ]);
    }
    const searchResults = await searchUserKnowledge(searchParams);

    Logger.log('知识库搜索结果：', searchResults);

    if (!searchResults.source_documents || !searchResults.source_documents.length) {
      return '没有相关知识点';
    }
    const appendPrompt =
      (getRemoteConfigSync().chatGPTKnowledgePrompt ||
        `\n仅参考用户问题相关的知识点回答（包含markdown格式的相关代码片段和图片！）\n\n`) +
      `\n本次直接根据当前历史会话回答，不要返回tool_calls。\n\n`;

    let results = searchResults.source_documents
      .map(({ content, source }, index) => `参考知识点${index + 1}（来源：${source}）：\n${content}`)
      .join('\n');
    results += appendPrompt;
    return results;
  }

  // description = 'a knowledge base QA - useful for when you need to ask any questions.';
  description = '一个知识库 - 回答任何提问之前都可以参考其中的知识点';
}
