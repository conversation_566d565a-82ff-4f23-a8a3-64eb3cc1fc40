import { <PERSON><PERSON>rowser } from 'langchain/tools/webbrowser';
import { Tool } from 'langchain/tools';
import { GoogleCustomSearch } from '@langchain/community/tools/google_custom_search';
import { TavilySearchResults } from '@langchain/community/tools/tavily_search';
import { SerpAPI } from '@langchain/community/tools/serpapi';
import { getVscodeConfig } from '@joycoder/shared';
import { GoogleSearch } from './search/google';
import { BaiduSearch } from './search/baidu';
import { DuckDuckGo } from './search/duckduckgo';
import { TaroSearch } from './search/taro';
import { PlaywrightWebBrowser } from './webbrowser';
import { KnowledgeQA } from './knowledge';
import { JoyCoderAIEmbeddings } from '../embeddings';
import { ChatJoyCoderAI } from '../chat_models';
import { AgentToolType } from './shared/toolType';

// 支持传数组，第二位是参数
type ToolItem<T = any> = string | [string, T];

export default function getTools(tools: ToolItem[] = []) {
  return tools
    .map((toolType) => {
      return getTool(toolType);
    })
    .filter((tool) => tool !== null);
}

function getTool(toolType: ToolItem) {
  const type = Array.isArray(toolType) ? toolType[0] : toolType;
  const args = Array.isArray(toolType) ? toolType[1] : {};
  let tool: Tool | null = null;
  switch (type) {
    case AgentToolType.GoogleSearch:
      tool = new GoogleSearch();
      break;
    case AgentToolType.BaiDuSearch:
      tool = new BaiduSearch();
      break;
    case AgentToolType.DuckDuckGoSearch:
      tool = new DuckDuckGo();
      break;
    case AgentToolType.SerpAPIBaiDu:
    case AgentToolType.SerpAPIGoogle:
    case AgentToolType.SerpAPIDuckDuckGo:
      const enginesMap = {
        [AgentToolType.SerpAPIGoogle]: 'google',
        [AgentToolType.SerpAPIBaiDu]: 'Baidu',
        [AgentToolType.SerpAPIDuckDuckGo]: 'duckduckgo',
      };
      const { serpApiKey } = getVscodeConfig('JoyCode.config.searchApiKey');
      // https://serpapi.com/manage-api-key
      tool = new SerpAPI(serpApiKey, {
        // @ts-ignore
        engine: enginesMap[type],
        location: 'China',
        hl: 'zh-cn',
        gl: 'cn',
      });
      break;
    case AgentToolType.TaroSearch:
      tool = new TaroSearch();
      break;

    case AgentToolType.GoogleAPI:
      const { googleApiKey, googleCSEId } = getVscodeConfig('JoyCode.config.searchApiKey');
      tool = new GoogleCustomSearch({
        apiKey: googleApiKey,
        googleCSEId: googleCSEId,
      });
      break;
    case AgentToolType.TavilyAPI:
      const { tavilyApiKey } = getVscodeConfig('JoyCode.config.searchApiKey');
      tool = new TavilySearchResults({
        apiKey: tavilyApiKey,
        maxResults: 2,
      });
      break;
    case AgentToolType.WebBrowser: {
      const model = new ChatJoyCoderAI({ temperature: 0 });
      const embeddings = new JoyCoderAIEmbeddings();
      tool = new WebBrowser({ model, embeddings });
      break;
    }
    case AgentToolType.PlaywrightWebBrowser:
      const model = new ChatJoyCoderAI({ temperature: 0 });
      const embeddings = new JoyCoderAIEmbeddings();
      tool = new PlaywrightWebBrowser({ model, embeddings });
      break;
    case AgentToolType.Knowledge: {
      tool = new KnowledgeQA(args);
      break;
    }
    default:
      break;
  }

  return tool;
}
