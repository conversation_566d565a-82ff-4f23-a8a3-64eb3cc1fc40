import { decode } from 'html-entities';
import { convert as htmlToText } from 'html-to-text';
import { Tool } from '@langchain/core/tools';
import * as cheerio from 'cheerio';
import { Logger, getRandomUserAgent } from '@joycoder/shared';
import { AgentToolType } from '../shared/toolType';
import { SearchResults } from '../shared/types';

export class BaiduSearch extends Tool {
  name = AgentToolType.BaiDuSearch;
  maxResults = 3;

  /** @ignore */
  async _call(input: string) {
    const searchResults = await search(input, this.maxResults);

    Logger.log('百度搜索结果：', searchResults);

    if (searchResults.results.length === 0) {
      return '没有找到好的搜索结果';
    }

    const results = searchResults.results
      .map(({ title, description, url }) => `网页链接：\n[${title}](${url})\n网页内容：\n${htmlToText(description)}`)
      .join('\n\n');
    return results;
  }

  description =
    'a search engine. useful for when you need to answer questions about current events. input should be a search query.';
}

async function search(input: string, maxResults: number): Promise<SearchResults> {
  const results: SearchResults = {
    results: [],
  };

  const headers = new Headers();
  headers.append('User-Agent', getRandomUserAgent());

  const url = `https://www.baidu.com/s?f=8&ie=utf-8&rn=${maxResults}&wd=${encodeURIComponent(input)}`;
  const resp = await fetch(url, {
    headers: headers,
  });

  const html = await resp.text();
  const respCheerio = cheerio.load(html);

  respCheerio('div.c-container.new-pmd')
    .slice(0, maxResults)
    .each((i, elem) => {
      const item = cheerio.load(elem);
      const linkElement = item('a');
      const url = (linkElement.attr('href') ?? '').trim();
      if (url !== '' && url !== '#') {
        const title = decode(linkElement.text());
        const description = item
          .text()
          .replace(/\n+\s+/g, ' ')
          .trim();
        results.results.push({
          url,
          title,
          description,
        });
      }
    });

  return results;
}
