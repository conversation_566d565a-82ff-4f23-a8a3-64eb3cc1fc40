import { decode } from 'html-entities';
import { Tool } from '@langchain/core/tools';
import * as cheerio from 'cheerio';
import { URL } from 'url';
import { NodeHtmlMarkdown } from 'node-html-markdown';
import { getFullHtmlByPlaywright } from '../../document_loaders';
import { SearchResults, SearchResult } from '../shared/types';

const _baseUrl = 'https://taro-docs.jd.com';

export class TaroSearch extends Tool {
  name = 'taro_search';
  maxResults = 1;

  /** @ignore */
  async _call(input: string) {
    const searchResults = await search(input, this.maxResults);

    if (searchResults.results.length === 0) {
      return '没有找到好的搜索结果';
    }

    const results = searchResults.results
      .map(({ title, description, url }) => `网页链接：\n[${title}](${url})\n网页内容：\n${description}`)
      .join('\n\n');
    return results;
  }

  description =
    'a TaroJS(Taro) search engine. useful for when you need to answer questions about Taro. input should be a keyword extracted from the question.';
}

async function search(input: string, maxResults = 1): Promise<SearchResults> {
  const results: SearchResults = {
    results: [],
  };

  const url = `https://taro-docs.jd.com/search?q=${encodeURIComponent(input)}`;

  const loaderResult = await getFullHtmlByPlaywright(url, {
    waitForSelector: 'div.container article',
  });
  const html = loaderResult?.[0]?.pageContent ?? '';
  const respCheerio = cheerio.load(html);

  // 搜索页列表中的链接
  const searchList: SearchResult[] = [];
  const pathList: string[] = [];
  respCheerio('div.container article')
    .slice(0, maxResults)
    .each((i, elem) => {
      const item = cheerio.load(elem);
      const linkElement = item('a');
      const url = _baseUrl + (linkElement.attr('href') ?? '').trim();
      const urlParsed = new URL(url);
      if (!pathList.includes(urlParsed.pathname)) {
        const title = decode(linkElement.text());
        const description = item.text().replace(title, '').trim();
        searchList.push({
          url,
          title,
          description,
        });
        pathList.push(urlParsed.pathname); // 防止链接重复
      }
    });

  // 逐个访问搜索列表中的页面
  for await (const searchItem of searchList) {
    const searchItemLoaderResult = await getFullHtmlByPlaywright(searchItem.url, {
      waitForSelector: 'div.theme-doc-markdown',
    });
    const searchItemHtml = searchItemLoaderResult?.[0]?.pageContent ?? '';
    const searchItemRespCheerio = cheerio.load(searchItemHtml);
    searchItemRespCheerio('div.theme-doc-markdown').each((i, elem) => {
      const item = cheerio.load(elem);
      const headerElement = item('header');
      const title = decode(headerElement.text());
      const description = NodeHtmlMarkdown.translate(item.html()).trim();
      results.results.push({
        url: searchItem.url,
        title,
        description,
      });
    });
  }

  return results;
}
