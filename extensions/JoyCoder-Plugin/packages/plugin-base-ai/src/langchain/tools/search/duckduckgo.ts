import { decode } from 'html-entities';
import { convert as htmlToText } from 'html-to-text';
import { Tool } from '@langchain/core/tools';

const SEARCH_REGEX = /DDG\.pageLayout\.load\('d',(\[.+\])\);DDG\.duckbar\.load\('images'/;
const IMAGES_REGEX = /;DDG\.duckbar\.load\('images', ({"ads":.+"vqd":{".+":"\d-\d+-\d+"}})\);DDG\.duckbar\.load\('news/;
const NEWS_REGEX = /;DDG\.duckbar\.load\('news', ({"ads":.+"vqd":{".+":"\d-\d+-\d+"}})\);DDG\.duckbar\.load\('videos/;
const VIDEOS_REGEX =
  /;DDG\.duckbar\.load\('videos', ({"ads":.+"vqd":{".+":"\d-\d+-\d+"}})\);DDG\.duckbar\.loadModule\('related_searches/;
const RELATED_SEARCHES_REGEX =
  /DDG\.duckbar\.loadModule\('related_searches', ({"ads":.+"vqd":{".+":"\d-\d+-\d+"}})\);DDG\.duckbar\.load\('products/;
const VQD_REGEX = /vqd=['"](\d+-\d+(?:-\d+)?)['"]/;

interface CallbackSearchResult {
  /** 网站描述 */
  a: string;
  /** 未知 */
  ae: null;
  /** ddg!bang 信息（例如 w Wikipedia en.wikipedia.org) */
  b?: string;
  /** URL */
  c: string;
  /** 某种 URL*/
  d: string;
  /** 类名关联*/
  da?: string;
  /** 未知 */
  h: number;
  /** 网站主机名 */
  i: string;
  /** 未知 */
  k: null;
  /** 未知 */
  m: number;
  /** 未知 */
  o: number;
  /** 未知 */
  p: number;
  /** 未知 */
  s: string;
  /** 网站标题 */
  t: string;
  /** 网站 URL */
  u: string;
}

interface CallbackNextSearch {
  /** 下一页结果的 URL */
  n: string;
}

interface CallbackDuckbarPayload<T> {
  ads: null | any[];
  query: string;
  queryEncoded: string;
  response_type: string;
  results: T[];
  vqd: {
    [query: string]: string;
  };
}

interface DuckbarImageResult {
  /** 图像的高度（以像素为单位）*/
  height: number;
  /** 图像 URL*/
  image: string;
  /** 图片来源*/
  source: string;
  /** 缩略图 URL*/
  thumbnail: string;
  /** 图片的标题（或说明）*/
  title: string;
  /** 图片来源的网站 URL*/
  url: string;
  /** 图片的宽度（像素）*/
  width: number;
}

interface DuckbarVideoResult {
  /** 视频的 URL */
  content: string;
  /** 视频描述 */
  description: string;
  /** 视频的持续时间 */
  duration: string;
  /** 嵌入视频的 HTML */
  embed_html: string;
  /** 嵌入视频的 URL */
  embed_url: string;
  /** 视频的缩略图 */
  images: {
    large: string;
    medium: string;
    motion: string;
    small: string;
  };
  /** 此搜索结果的来源 */
  provider: string;
  /** 上传的 ISO 时间戳 */
  published: string;
  /** 视频在哪个网站上 */
  publisher: string;
  /** 各种统计数据 */
  statistics: {
    /** 视频的观看次数 */
    viewCount: number | null;
  };
  /** 视频标题 */
  title: string;
  /** 视频上传者名称(?) */
  uploader: string;
}

interface DuckbarRelatedSearch {
  display_text: string;
  text: string;
  web_search_url: string;
}

interface DuckbarNewsResult {
  date: number;
  excerpt: string;
  image?: string;
  relative_time: string;
  syndicate: string;
  title: string;
  url: string;
  use_relevancy: number;
  is_old?: number;
  fetch_image?: number;
}

interface SearchResults {
  /** 是否未找到结果*/
  noResults: boolean;
  /** 搜索查询的 VQD*/
  vqd: string;
  /** 搜索的网页结果*/
  results: SearchResult[];
  /** 搜索的图片结果*/
  images?: DuckbarImageResult[];
  /** 搜索的新闻文章结果*/
  news?: NewsResult[];
  /** 视频搜索结果*/
  videos?: VideoResult[];
  /** 查询的相关搜索结果*/
  related?: RelatedResult[];
}

interface VideoResult {
  /** 视频的 URL*/
  url: string;
  /** 视频标题*/
  title: string;
  /** 视频描述*/
  description: string;
  /** 视频的图片 URL*/
  image: string;
  /** 视频的持续时间(即 "9:20"） */
  duration: string;
  /** 视频发布的 ISO 时间戳*/
  published: string;
  /** 视频的发布位置(例如 "YouTube"） */
  publishedOn: string;
  /** 视频上传者的姓名*/
  publisher: string;
  /** 视频的观看次数*/
  viewCount?: number;
}

interface NewsResult {
  /** 文章创建的时间戳*/
  date: number;
  /** 文章的例外*/
  excerpt: string;
  /** 文章中使用的图片 URL*/
  image?: string;
  /** 文章发布的相对时间，以人类可读格式表示*/
  relativeTime: string;
  /** 这篇文章的索引来源*/
  syndicate: string;
  /** 文章标题*/
  title: string;
  /** 文章的 URL*/
  url: string;
  /** 这篇文章是否被归类为旧文章*/
  isOld: boolean;
}

interface SearchResult {
  /** 网站的主机名(即 "google.com"） */
  hostname: string;
  /** 结果的 URL*/
  url: string;
  /** 结果的标题*/
  title: string;
  /**
   * 经过消毒的结果描述
   * 粗体标记仍将出现在此字符串中
   */
  description: string;
  /** 结果描述*/
  rawDescription: string;
  /** 网站的图标*/
  icon: string;
  /** 网站的 ddg!bang 信息（如果有）*/
  bang?: SearchResultBang;
}

interface SearchResultBang {
  /** bang 的前缀(例如，"w "表示 !w) */
  prefix: string;
  /** bang 的标题*/
  title: string;
  /** bang 的域*/
  domain: string;
}

interface RelatedResult {
  text: string;
  raw: string;
}

enum SearchTimeType {
  /** 从任何时候开始*/
  ALL = 'a',
  /** 来自过去一天*/
  DAY = 'd',
  /** 来自过去一周*/
  WEEK = 'w',
  /** 从上个月开始*/
  MONTH = 'm',
  /** 从去年开始*/
  YEAR = 'y',
}

interface SearchOptions {
  /** 搜索的安全搜索类型*/
  safeSearch?: SafeSearchType;
  /** 搜索的时间范围，可以是 SearchTimeType 或日期范围（"2021-03-16...2021-03-30"） */
  time?: SearchTimeType | string;
  /** 搜索的语言（?） 默认为 "en-us"*/
  locale?: string;
  /** 搜索的区域默认为 "wt-wt "或所有地区*/
  region?: string;
  /** 搜索的市场区域（？默认为 "美国"*/
  marketRegion?: string;
  /** 搜索结果的偏移量*/
  offset?: number;
  /**
   * 作为搜索关键字的字符串
   * 如果您使用相同的查询进行搜索，则设置该字符串
   */
  vqd?: string;
}

enum SafeSearchType {
  /** 严格过滤，无 NSFW 内容*/
  STRICT = 0,
  /** 适度过滤*/
  MODERATE = -1,
  /** 无过滤*/
  OFF = -2,
}

const defaultOptions: SearchOptions = {
  safeSearch: SafeSearchType.OFF,
  time: SearchTimeType.ALL,
  locale: 'en-us',
  region: 'wt-wt',
  offset: 0,
  marketRegion: 'us',
};

export class DuckDuckGo extends Tool {
  name = 'duckduckgo_search';
  maxResults = 4;

  /** @ignore */
  async _call(input: string) {
    const searchResults = await search(input, {
      safeSearch: SafeSearchType.OFF,
    });

    if (searchResults.noResults) {
      return '没有找到好的搜索结果';
    }

    const results = searchResults.results
      .slice(0, this.maxResults)
      .map(({ title, description, url }) => htmlToText(description))
      .join('\n\n');

    return results;
  }

  description =
    'a search engine. useful for when you need to answer questions about current events. input should be a search query.';
}

async function search(query: string, options?: SearchOptions): Promise<SearchResults> {
  if (!query) throw new Error('Query cannot be empty!');
  if (!options) options = defaultOptions;
  else options = sanityCheck(options);

  let vqd = options.vqd!;
  if (!vqd) vqd = await getVQD(query, 'web');

  const queryObject: Record<string, string> = {
    q: query,
    ...(options.safeSearch !== SafeSearchType.STRICT ? { t: 'D' } : {}),
    l: options.locale!,
    ...(options.safeSearch === SafeSearchType.STRICT ? { p: '1' } : {}),
    kl: options.region || 'wt-wt',
    s: String(options.offset),
    dl: 'en',
    ct: 'US',
    ss_mkt: options.marketRegion!,
    df: options.time! as string,
    vqd,
    ...(options.safeSearch !== SafeSearchType.STRICT ? { ex: String(options.safeSearch) } : {}),
    sp: '1',
    bpa: '1',
    biaexp: 'b',
    msvrtexp: 'b',
    ...(options.safeSearch === SafeSearchType.STRICT
      ? {
          videxp: 'a',
          nadse: 'b',
          eclsexp: 'a',
          stiaexp: 'a',
          tjsexp: 'b',
          related: 'b',
          msnexp: 'a',
        }
      : {
          nadse: 'b',
          eclsexp: 'b',
          tjsexp: 'b',
          // cdrexp: 'b'
        }),
  };

  const response = await fetch(`https://links.duckduckgo.com/d.js?${queryString(queryObject)}`);
  const data = await response.text();

  if (data.includes('DDG.deep.is506')) throw new Error('A server error occurred!');

  const searchResults = JSON.parse(SEARCH_REGEX.exec(data)![1].replace(/\t/g, '    ')) as (
    | CallbackSearchResult
    | CallbackNextSearch
  )[];

  if (searchResults.length === 1 && !('n' in searchResults[0])) {
    const onlyResult = searchResults[0] as CallbackSearchResult;
    /* istanbul ignore next */
    if ((!onlyResult.da && onlyResult.t === 'EOF') || !onlyResult.a || onlyResult.d === 'google.com search')
      return {
        noResults: true,
        vqd,
        results: [],
      };
  }

  const results: SearchResults = {
    noResults: false,
    vqd,
    results: [],
  };

  for (const search of searchResults) {
    if ('n' in search) continue;
    // eslint-disable-next-line init-declarations
    let bang: SearchResultBang | undefined;
    if (search.b) {
      const [prefix, title, domain] = search.b.split('\t');
      bang = { prefix, title, domain };
    }
    results.results.push({
      title: search.t,
      description: decode(search.a),
      rawDescription: search.a,
      hostname: search.i,
      icon: `https://external-content.duckduckgo.com/ip3/${search.i}.ico`,
      url: search.u,
      bang,
    });
  }

  // 图片
  const imagesMatch = IMAGES_REGEX.exec(data);
  if (imagesMatch) {
    const imagesResult = JSON.parse(
      imagesMatch[1].replace(/\t/g, '    ')
    ) as CallbackDuckbarPayload<DuckbarImageResult>;
    results.images = imagesResult.results.map((i) => {
      i.title = decode(i.title);
      return i;
    });
  }

  // 新闻
  const newsMatch = NEWS_REGEX.exec(data);
  if (newsMatch) {
    const newsResult = JSON.parse(newsMatch[1].replace(/\t/g, '    ')) as CallbackDuckbarPayload<DuckbarNewsResult>;
    results.news = newsResult.results.map((article) => ({
      date: article.date,
      excerpt: decode(article.excerpt),
      image: article.image,
      relativeTime: article.relative_time,
      syndicate: article.syndicate,
      title: decode(article.title),
      url: article.url,
      isOld: !!article.is_old,
    })) as NewsResult[];
  }

  // 视频
  const videosMatch = VIDEOS_REGEX.exec(data);
  if (videosMatch) {
    const videoResult = JSON.parse(videosMatch[1].replace(/\t/g, '    ')) as CallbackDuckbarPayload<DuckbarVideoResult>;
    results.videos = [];
    /* istanbul ignore next */
    for (const video of videoResult.results) {
      results.videos.push({
        url: video.content,
        title: decode(video.title),
        description: decode(video.description),
        image: video.images.large || video.images.medium || video.images.small || video.images.motion,
        duration: video.duration,
        publishedOn: video.publisher,
        published: video.published,
        publisher: video.uploader,
        viewCount: video.statistics.viewCount || undefined,
      });
    }
  }

  // 相关搜索
  const relatedMatch = RELATED_SEARCHES_REGEX.exec(data);
  if (relatedMatch) {
    const relatedResult = JSON.parse(
      relatedMatch[1].replace(/\t/g, '    ')
    ) as CallbackDuckbarPayload<DuckbarRelatedSearch>;
    results.related = [];
    for (const related of relatedResult.results) {
      results.related.push({
        text: related.text,
        raw: related.display_text,
      });
    }
  }
  return results;
}

function queryString(query: Record<string, string>) {
  return new URLSearchParams(query).toString();
}

async function getVQD(query: string, ia = 'web') {
  try {
    const response = await fetch(`https://duckduckgo.com/?${queryString({ q: query, ia })}`);
    const data = await response.text();
    return VQD_REGEX.exec(data)![1];
  } catch (e) {
    throw new Error(`Failed to get the VQD for query "${query}".`);
  }
}

function sanityCheck(options: SearchOptions) {
  options = Object.assign({}, defaultOptions, options);

  if (!(options.safeSearch! in SafeSearchType))
    throw new TypeError(`${options.safeSearch} is an invalid safe search type!`);

  /* istanbul ignore next */
  if (typeof options.safeSearch! === 'string')
    options.safeSearch = SafeSearchType[options.safeSearch!] as any as SafeSearchType;

  if (typeof options.offset !== 'number') throw new TypeError(`Search offset is not a number!`);

  if (options.offset! < 0) throw new RangeError('Search offset cannot be below zero!');

  if (
    options.time &&
    !Object.values(SearchTimeType).includes(options.time as SearchTimeType) &&
    !/\d{4}-\d{2}-\d{2}..\d{4}-\d{2}-\d{2}/.test(options.time as string)
  )
    throw new TypeError(`${options.time} is an invalid search time!`);

  if (!options.locale || typeof options.locale! !== 'string') throw new TypeError('Search locale must be a string!');

  if (!options.region || typeof options.region! !== 'string') throw new TypeError('Search region must be a string!');

  if (!options.marketRegion || typeof options.marketRegion! !== 'string')
    throw new TypeError('Search market region must be a string!');

  if (options.vqd && !/\d-\d+-\d+/.test(options.vqd)) throw new Error(`${options.vqd} is an invalid VQD!`);

  return options;
}
