import { getJdhLoginInfo, isDebug } from '@joycoder/shared';

/**
 * https://joyspace.jd.com/pages/x5Z1ozZIzmjXMZWwh3v0
 */
export interface IExtParams {
  bizId: string;
  bizToken: string;
  ssoClientId: string;
  jdhLoginParams: {
    userToken?: string;
    userName?: string;
    erp?: string;
    sourceType: string;
  };
}

/**
 * 获取OpenAI定制接口的扩展参数
 * @returns IExtParams
 */
export function getExtParams(): IExtParams {
  return {
    bizId: 'joycoderfe',
    bizToken: 'd99fa9a5-3655-4251-8141-e0f84dccb099',
    ssoClientId: 'SSO1.0',
    jdhLoginParams: { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' },
  };
}

/**
 * 开发模式时打印openai包中的请求日志，日志以 OpenAI:DEBUG: 开头
 * 直接在launch.json中设置DEBUG环境变量会被覆盖
 */
export function setShowOpenAILog() {
  if (isDebug()) {
    process.env.DEBUG = 'true';
  }
}

setShowOpenAILog();
