import { getRemoteConfigSync, getVscodeConfig, isBusiness, isFirstUse, setVscodeConfig } from '@joycoder/shared';

export interface ChatModelConfig {
  label: string;
  description: string;
  avatar: string;
  chatApiModel: string;
  chatApiUrl?: string;
  maxTotalTokens: number;
  bizId?: string;
  bizToken?: string;
  systemMessage?: string;
  respMaxTokens?: number;
  // 标记类
  hidden?: boolean;
  prefer?: boolean;
  context?: boolean;
  features?: string[];
  temperature?: number;
  stream?: boolean;
  modelFunctionType?: string;
}

export enum ChatModel {
  GPT35 = 'GPT-3.5-Turbo',
  GPT4 = 'GPT-4-Turbo',
  GPT4o = 'GPT-4-OMNI',
  CHATRHINO = '京东言犀',
}
export const DIFF_VIEW_URI_SCHEME = 'JoyCode-fixup';

// 默认展示的模型名称
export const DEFAULT_CHAT_MODEL = isBusiness() ? 'JoyCode-Pro' : ChatModel.CHATRHINO;
// 默认调用接口的模型
export const DEFAULT_CHAT_API_MODEL = isBusiness() ? 'JoyCode-chat-m2' : 'Chatrhino-81B';
// 默认模型配置
const DEFAULT_MODEL_CONFIG: ChatModelConfig = {
  label: DEFAULT_CHAT_MODEL,
  description: 'Chatrhino-81B，京东集团自主研发的通用对话大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域',
  avatar: 'https://img12.360buyimg.com/img/jfs/t1/233632/24/14048/2756/6606b975Fc01ea416/82d100921c2627e3.jpg',
  chatApiModel: DEFAULT_CHAT_API_MODEL,
  maxTotalTokens: 128000,
};

/**
 * 获取ChatGPT模型和配置
 *
 * @export
 * @param {string} [modelLabel]
 * @return {*}  {[string, Partial<ChatModelConfig>]}
 */
export function getChatModelAndConfig(_modelLabel?: string): ChatModelConfig {
  let modelLabel = _modelLabel || (getVscodeConfig('JoyCode.config.chatModel') as string);
  let chatGPTModelConfigs: ChatModelConfig[] = getRemoteConfigSync().chatGPTModelConfigs || [];
  const chatModelConfigsTest = isBusiness() ? '[]' : getVscodeConfig('JoyCode.config.test-llm-list');
  chatGPTModelConfigs = [...chatGPTModelConfigs, ...JSON.parse(chatModelConfigsTest)];

  // 过滤掉隐藏的模型，一些历史过期模型逐步淘汰
  chatGPTModelConfigs = chatGPTModelConfigs.filter(
    (modelConfig) =>
      !modelConfig.hidden &&
      (!modelConfig.hasOwnProperty('modelFunctionType') ||
        modelConfig.modelFunctionType === 'ALL' ||
        modelConfig.modelFunctionType === 'chat')
  );

  // 读取FAAS下发的模型配置 @see http://xingyun.jd.com/codingRoot/jx-promote-fe/craftx-project/blob/master/ide_JoyCoderFE/vscode.js#L169
  let modelConfig: ChatModelConfig | undefined = chatGPTModelConfigs.find(
    (config: ChatModelConfig) => config.label == modelLabel
  );

  if (!modelConfig || !modelConfig.label) {
    // 说明用户本地的模型，不在远程模型中，系统帮忙自动选择第一个
    modelConfig =
      // 优先找prefer
      chatGPTModelConfigs.find((config: ChatModelConfig) => !!config.prefer) ||
      // 然后找hidden=false的第一个
      chatGPTModelConfigs.filter((item) => !item.hidden)?.[0] ||
      // 然后取所有配置的第一个
      chatGPTModelConfigs[0];
    // 最后找DEFAULT_CHAT_MODEL
    modelLabel = modelConfig?.label || DEFAULT_CHAT_MODEL;
    modelConfig = chatGPTModelConfigs.find((config: ChatModelConfig) => config.label == modelLabel);
    setChatModel(modelLabel as ChatModel);
  }

  // [model]: 调用接口传的model参数可能要重命名成标准命名（GPT-4 -> gpt-4）
  return modelConfig || chatGPTModelConfigs[0] || DEFAULT_MODEL_CONFIG;
}

/**
 * 用于首次启动，设置首选ChatModel
 *
 * @export
 */
export function setPreferChatModel() {
  const chatGPTModelConfigs = getRemoteConfigSync().chatGPTModelConfigs || [];
  const preferModelConfig = chatGPTModelConfigs.find((config: ChatModelConfig) => !!config.prefer);
  if (isFirstUse() && preferModelConfig && preferModelConfig.label)
    setVscodeConfig('JoyCode.config.chatModel', preferModelConfig.label);
}

export function setChatModel(model: string) {
  setVscodeConfig('JoyCode.config.chatModel', model);
}

export function getChatModel() {
  return getVscodeConfig('JoyCode.config.chatModel');
}
