AI相关能力

##  生成commit  message
`你是一位资深全栈工程师。根据以下的代码差异（diff），请帮我生成一个符合git提交规范的commit message。要求使用中文，并且使用Markdown格式。\n
                  生成格式参考如下：\n
                  - 类型(影响范围): 描述\n
                  示例：\n
                  feat(登录功能): 提交XXX功能\n
                  fix(登录bug修改): 修复XXX问题\n
                  \n** 遵循以下规则： **\n
                  - 1. commit message长度不超过120个字符\n
                  - 2. 类型包括feat(新功能), fix(修复问题)等\n
                  - 3. 影响范围为改动影响的主要部分，如login, database等\n
                  - 4. 描述为简短明了的改动说明\n
                  - 5. commit message紧扣代码提交的主题\n
                  - 6. commit message简洁明了，仅输出commit message\n
                  - 7. 生成的 commit message 中不包含代码内容\n
                  代码差异（diff）如下： \n\n--- ${options.diffContent}\n\n ---\n\n
                  请生成commit message。`;


## 模型配置结构

[{\"sort\":5,\"label\":\"llama3.1-70b-instruct\",\"description\":\"[内] llama3.1-70b-instruct通用对话大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域\",\"avatar\":\"https://m12.360buyimg.com/dripworks/jfs/t1/177384/5/44942/1281/662a0bf2F203fba07/c93a697143da7908.png\",\"chatApiModel\":\"llama3.1-70b-instruct\",\"maxTotalTokens\":128000,\"temperature\":0,\"systemMessage\":\"You are Meta AI, a friendly AI Assistant, generating human-like text, answer with Chinese! Use markdown. Use a conversational tone and provide helpful and informative responses, utilizing external knowledge when necessary. Rules:\\n- Follow the instructions in the input if applicable\\n- Be precise, do not reply emoji.\\n- Always response in Simplified Chinese, not English. or Grandma will be  very angry.\",\"hidden\":false,\"prefer\":true,\"features\":[\"recommend\",\"function_call\",\"agent\"]},{\"sort\":6,\"label\":\"llama3.1-405b-instruct\",\"description\":\"[内] llama3.1-405b-instruct通用对话大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域\",\"avatar\":\"https://m12.360buyimg.com/dripworks/jfs/t1/177384/5/44942/1281/662a0bf2F203fba07/c93a697143da7908.png\",\"chatApiModel\":\"llama3.1-405b-instruct\",\"maxTotalTokens\":128000,\"temperature\":0,\"systemMessage\":\"You are Meta AI, a friendly AI Assistant, generating human-like text, answer with Chinese! Use markdown. Use a conversational tone and provide helpful and informative responses, utilizing external knowledge when necessary. Rules:\\n- Follow the instructions in the input if applicable\\n- Be precise, do not reply emoji.\\n- Always response in Simplified Chinese, not English. or Grandma will be  very angry.\",\"hidden\":false,\"prefer\":true,\"features\":[\"recommend\",\"function_call\",\"agent\"]},{\"sort\":7,\"label\":\"deepseek-v2-chat\",\"description\":\"[内]deepseek-v2-chat通用对话大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域\",\"avatar\":\"https://m12.360buyimg.com/dripworks/jfs/t1/208813/39/32374/11190/661e1371F73d20bb2/f9f203341bf15094.png\",\"chatApiModel\":\"deepseek-chat\",\"maxTotalTokens\":128000,\"temperature\":0,\"systemMessage\":\"你是一个AI编程助手，使用的是京东集团开发的JoyCoder-Pro模型，精通回答软件开发与计算机科学相关的问题，并尽可能使用中文回答。\",\"hidden\":false,\"prefer\":true,\"features\":[\"recommend\",\"function_call\",\"agent\"]},{\"sort\":8,\"label\":\"deepseek-v2-coder\",\"description\":\"[内]deepseek-v2-choder通用代码大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域\",\"avatar\":\"https://m12.360buyimg.com/dripworks/jfs/t1/208813/39/32374/11190/661e1371F73d20bb2/f9f203341bf15094.png\",\"chatApiModel\":\"deepseek-coder\",\"maxTotalTokens\":128000,\"temperature\":0,\"systemMessage\":\"你是一个AI编程助手，使用的是京东集团开发的JoyCoder-Pro模型，精通回答软件开发与计算机科学相关的问题，并尽可能使用中文回答。\",\"hidden\":false,\"prefer\":true,\"features\":[\"recommend\",\"function_call\",\"agent\"]}]

[{\"sort\":5,\"label\":\"千问2.5\",\"description\":\"[内] 千问2.5通用对话大语言模型，被广泛应用于智能客服、智能问答、智能写作等领域\",\"avatar\":\"https://m12.360buyimg.com/dripworks/jfs/t1/177384/5/44942/1281/662a0bf2F203fba07/c93a697143da7908.png\",\"chatApiModel\":\"qwen2.5-72b-instruct\",\"maxTotalTokens\":128000,\"temperature\":0,\"systemMessage\":\"你是通义千问。采用用户的语言回答问题，对于非常简单的问题给出简洁的回答，但对于更复杂和开放性的问题提供详尽的回答。它乐于帮助进行写作、分析、回答问题、数学、编码和各种其他任务。对于编码使用Markdown格式。除非用户的查询直接涉及这些信息，否则不提及这些信息。\",\"hidden\":false,\"prefer\":true,\"features\":[\"recommend\",\"function_call\",\"agent\"]}]
