import { IReportParam } from '../multiDocumentTranslation/codeTranslateLanguage';
import * as vscode from 'vscode';
export default class FileTreeNode {
  public fileType: string;
  public fileName: string;
  public fileIconPath?: string;
  public filePath?: string;
  public originalFilePath?: string; // 原文件
  public originalFileName?: string; // 原文件名称
  public fileLanguage?: string;
  public code?: string;
  public statusIconPath?: { light: vscode.Uri; dark: vscode.Uri };
  public cancelIcon?: string;
  public controllerId?: string;
  public folder?: string;
  public diffIcon?: string;
  public fileContent?: string;
  public fileController?: AbortController;
  public transformCodeReport?: IReportParam;
  public children?: FileTreeNode[];

  constructor(
    fileType: string,
    fileName: string,
    fileIconPath?: string,
    filePath?: string,
    originalFilePath?: string,
    originalFileName?: string,
    fileLanguage?: string,
    code?: string,
    statusIconPath?: { light: vscode.Uri; dark: vscode.Uri },
    cancelIcon?: string,
    controllerId?: string,
    folder?: string,
    diffIcon?: string,
    fileContent?: string,
    fileController?: AbortController,
    transformCodeReport?: IReportParam,
    children?: FileTreeNode[]
  ) {
    try {
      this.fileIconPath = fileIconPath;
      this.fileName = fileName;
      this.filePath = filePath;
      this.originalFilePath = originalFilePath;
      this.originalFileName = originalFileName;
      this.fileType = fileType;
      this.fileLanguage = fileLanguage;
      this.code = code;
      this.statusIconPath = statusIconPath;
      this.cancelIcon = cancelIcon;
      this.controllerId = controllerId;
      this.folder = folder;
      this.diffIcon = diffIcon;
      this.fileContent = fileContent;
      this.fileController = fileController;
      this.transformCodeReport = transformCodeReport;
      this.children = children;
    } catch {}
  }
}
