import * as vscode from 'vscode';
import FileTreeNode from './fileTreeNode';
import { Logger, getAssetsFilePath } from '@joycoder/shared';
import CodeTranslateLanguage from '../multiDocumentTranslation/codeTranslateLanguage';
import { CodeTranslateChatType } from '@joycoder/plugin-base-ai/src/dialog/constant';

export default class FileTreeDataProvider implements vscode.TreeDataProvider<FileTreeNode> {
  private _onDidChangeTreeData: vscode.EventEmitter<FileTreeNode | undefined> = new vscode.EventEmitter<
    FileTreeNode | undefined
  >();
  readonly onDidChangeTreeData: vscode.Event<FileTreeNode | undefined> = this._onDidChangeTreeData.event;

  public fileList: FileTreeNode[];
  private batchSize = 5;
  processType = ''; // 处理类型 CR 转flutter、转Taro等

  constructor(fileList: FileTreeNode[], processType: string) {
    this.processType = processType;
    this.fileList = fileList;
  }

  setFileList(fileList) {
    this.fileList = fileList;
  }
  setProcessType(processType) {
    this.processType = processType;
  }
  // file树UI
  getTreeItem(element: FileTreeNode): vscode.TreeItem {
    const label = element.fileName;
    const description = element.filePath;
    return {
      iconPath: element.statusIconPath,
      label,
      description,
      collapsibleState:
        element.children && element.children.length > 0
          ? vscode.TreeItemCollapsibleState.Collapsed
          : vscode.TreeItemCollapsibleState.None,
      command:
        element.fileType !== 'directory'
          ? {
              command: 'JoyCoderFE.multi.translation.command',
              title: '节点操作',
              arguments: [element],
            }
          : undefined,
    };
  }

  private getCustomTreeItem(treeNode: FileTreeNode): FileTreeNode[] {
    const node = new FileTreeNode(
      treeNode.fileType,
      treeNode.fileName,
      treeNode.filePath,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      treeNode.transformCodeReport,
      []
    );
    return [node];
  }

  /**
   * 获取子节点。
   *
   * @param element 可选的文件树节点。
   * @returns 包含子节点的Promise对象。
   */
  getChildren(element?: FileTreeNode): Thenable<FileTreeNode[]> {
    if (element) {
      return Promise.resolve(element.children || []);
    } else {
      return Promise.resolve(this.getFileData(this.fileList)).then((children) => {
        if (children && children?.length === 0) {
          // 初始化init、正在检查checking、空empty
          return this.getCustomTreeItem({ fileType: '', fileName: '' });
        }
        return children;
      });
    }
  }

  refresh(): void {
    this._onDidChangeTreeData.fire(undefined);
  }
  /**
   * 更新节点并触发树数据变化事件。
   * @param item - 要更新的文件树节点。
   */
  updateNode(item: FileTreeNode): void {
    this._onDidChangeTreeData.fire(item);
  }
  /**
   * 以批次处理数组中的项目。
   *
   * @template T - 输入项目的类型。
   * @template R - 输出结果的类型。
   * @param {T[]} items - 要处理的项目数组。
   * @param {number} batchSize - 每个批次的大小。
   * @param {(item: T, index: number) => Promise<R>} fn - 处理每个项目的异步函数。
   * @returns {Promise<R[]>} - 包含所有处理结果的Promise数组。
   */
  private async processInBatches<T, R>(
    items: T[],
    batchSize: number,
    fn: (item: T, index: number) => Promise<R>
  ): Promise<R[]> {
    const result: R[] = new Array(items.length);

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map((item, batchIndex) => fn(item, i + batchIndex)));
      batchResults.forEach((batchResult, batchIndex) => {
        result[i + batchIndex] = batchResult;
      });
    }
    return result;
  }

  /**
   * 异步获取文件数据。
   * @param {FileTreeNode[]} params - 文件树节点数组。
   * @returns {Promise<FileTreeNode[]>} 处理后的文件树节点数组。
   */
  private async getFileData(params: FileTreeNode[]): Promise<FileTreeNode[]> {
    const processNode = async (node: FileTreeNode): Promise<FileTreeNode> => {
      // 处理当前节点
      const processedNode = await this.processFile(node);

      // 如果节点有子节点，递归处理子节点
      if (processedNode.children && processedNode.children.length > 0) {
        processedNode.children = await this.processInBatches(
          processedNode.children,
          this.batchSize,
          async (child, index) => {
            return processNode(child);
          }
        );
      }

      return processedNode;
    };

    // 处理根节点
    const resultArrCheck = await this.processInBatches(params, this.batchSize, async (item, index) => {
      return processNode(item);
    });
    if (resultArrCheck.length > 0) {
      Logger.showInformationMessage(
        `代码翻译: 代码正在批量转换为${this.processType}，请点击查看详细内容，点击查看详细内容`,
        '查看'
      ).then((result: string) => {
        // if (result === '查看') {
        //   vscode.commands.executeCommand('workbench.view.extension.multiDocumentTranslation');
        // }
      });
    }
    return resultArrCheck;
  }

  /**
   * 处理文件节点的异步函数。
   *
   * @param item - 文件树节点。
   * @returns 处理后的文件树节点。
   */
  private async processFile(item: FileTreeNode) {
    // 处理类型
    let processType = this.processType;
    if (processType == CodeTranslateChatType['React-JS']) {
      processType = item.fileType == 'ts' ? CodeTranslateChatType['React-TS'] : processType;
    }
    try {
      const codeTranslateLanguage = new CodeTranslateLanguage(item, processType);
      item = codeTranslateLanguage.getFileNode();
      item.statusIconPath = this.getIconPath('loading');

      codeTranslateLanguage.getTranslateData().then((res) => {
        // 刷新树视图
        item.statusIconPath = this.getIconPath(res ? item.fileType?.toLowerCase() : 'abort');
        item.fileController = codeTranslateLanguage.getAbortController(item.controllerId ?? '');
        this.updateNode(item);
      });
    } catch (error) {
      Logger.warn('%c [ error ]-115', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    return item;
  }
  getIconPath(name: string) {
    return {
      light: vscode.Uri.file(getAssetsFilePath('translation/' + name + '.svg').fsPath), // 亮色主题下的图标路径
      dark: vscode.Uri.file(getAssetsFilePath('translation/' + name + '.svg').fsPath), // 暗色主题下的图标路径
    };
  }
  getFileList() {
    return this.fileList;
  }
}
