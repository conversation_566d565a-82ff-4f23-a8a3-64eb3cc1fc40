import { hasCommentMarker } from '@joycoder/plugin-base-code-completion/src/utils/elidableText';
import { getCodeReviewData } from './getCodeReviewData';
import { v4 as uuidv4 } from 'uuid';

/**
 * 提交代码审查
 * @param params - 包含代码和语言的参数对象
 * @returns 包含审查结果的对象或默认结果
 */
export async function commitCodeReview(params) {
  const { code, lang } = params || {};
  try {
    if (code) {
      const res = await getCodeReviewData(code, lang);
      if (res) {
        const { crId, crResults } = res;
        const crResult = JSON.parse(crResults) || [];
        const crArray = crResult.map((item, index) => {
          let optimizedCode = item.optimizedCode;
          const regex = /^[\u4e00-\u9fa5\u3400-\u4DBF]+$/;
          const isComment = hasCommentMarker({ source: item.optimizedCode, languageId: lang });
          if (!optimizedCode || regex.test(optimizedCode) || isComment) {
            const optimizedFullCode = crResult.find(
              (result) => result?.optimizedCode?.split?.('\n')?.length > 5 && result?.optimizedCode?.length > 50
            );
            optimizedCode = optimizedFullCode?.optimizedCode ?? '';
          }
          const reg = /```(.*?)\s*((.|\s)*?)\s```/g;
          const codeReg = reg.test(optimizedCode);
          if (!codeReg && optimizedCode) {
            optimizedCode = `\`\`\`\n${optimizedCode || ''}\n\`\`\``;
          }
          return {
            comment: item.content,
            cwe: 'CR-MULTI-' + index,
            end_line: item.endLine,
            id: crId,
            start_line: item.startLine,
            vulnerability: item.content,
            optimizedCode,
            ...params,
          };
        });
        return {
          cwe: 'CR-MULTI-LLM-' + uuidv4(),
          end_line: 0,
          id: crId,
          start_line: 0,
          vulnerability: '',
          fileName: params?.fileName,
          crArray,
        };
        // return crArray;
      }
    }
  } catch (error) {}
  return [
    {
      comment: '未发现漏洞',
      cwe: '',
      end_line: 0,
      id: '',
      start_line: 0,
      vulnerability: '',
      ...params,
    },
  ];
}

export async function reviewCommitFix(crItem, index, deepFixResult) {
  if (crItem.optimizedCode) {
    deepFixResult[index] = {
      cwe: crItem.cwe,
      fileName: crItem.fileName,
      text: crItem.optimizedCode.trim() || '',
      fullText: crItem.optimizedCode.trim(),
    };
  } else {
    deepFixResult[index] = {
      cwe: '',
      text: '',
      fileName: '',
      fullText: '',
    };
  }
  return deepFixResult[index];
}
export function setTreeData(data) {
  const root: any = [];
  if (data) {
    for (let index = 0; index < data.length; index++) {
      const element = data[index];
      const fileIndex = root.findIndex((item) => element.fileName === item.fileName);
      if (root.length == 0) {
        root.push(element);
      } else if (fileIndex > -1) {
        root.push(element);
      }
    }
  }
  return root;
}
export function uniqueArrayObjects(arr, field: string) {
  return arr.filter((item, index, self) => index === self.findIndex((findItem) => findItem[field] === item[field]));
}
