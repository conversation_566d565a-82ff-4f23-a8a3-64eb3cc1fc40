import { Logger } from '@joycoder/shared';
import * as vscode from 'vscode';
import * as fs from 'fs';
import FileTreeNode from '../tree/fileTreeNode';

// 获取文件夹下所有结构
export async function getDirectoryTree(uris: vscode.Uri[]): Promise<FileTreeNode[]> {
  const result: FileTreeNode[] = [];

  async function readDir(directoryUri: vscode.Uri): Promise<FileTreeNode[]> {
    const files = await vscode.workspace.fs.readDirectory(directoryUri);
    const nodes: FileTreeNode[] = [];

    for (const [file, fileType] of files) {
      const fileUri = vscode.Uri.joinPath(directoryUri, file);
      const filePath = fileUri.fsPath.replace(/\\/g, '/');
      const folderPath = directoryUri.fsPath.replace(/\\/g, '/');
      const fileExtension = file.substring(file.lastIndexOf('.') + 1);

      if (fileType === vscode.FileType.Directory) {
        const children = await readDir(fileUri);
        nodes.push({ fileName: file, filePath, fileType: 'directory', folder: folderPath, children });
      } else if (fileType === vscode.FileType.File) {
        let fileContent = '';
        try {
          fileContent = await getFileContent(filePath);
          nodes.push({
            fileName: file,
            filePath,
            fileContent,
            fileType: fileExtension,
            folder: folderPath,
            children: [],
          });
        } catch (error) {
          nodes.push({ fileName: file, filePath, fileType: fileExtension, folder: folderPath, children: [] });
        }
      }
    }

    return nodes;
  }

  async function readFile(fileUri: vscode.Uri): Promise<FileTreeNode> {
    const filePath = fileUri.fsPath.replace(/\\/g, '/');
    const folderPath = fileUri.fsPath.substring(0, fileUri.fsPath.lastIndexOf('/')).replace(/\\/g, '/');
    const fileName = fileUri.fsPath.substring(fileUri.fsPath.lastIndexOf('/') + 1);
    const fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1);

    let fileContent = '';
    try {
      fileContent = await getFileContent(filePath);
    } catch (error) {
      // Handle error if needed
    }

    return {
      fileName,
      filePath,
      fileContent,
      fileType: fileExtension,
      folder: folderPath,
      children: [],
    };
  }

  for (const uri of uris) {
    const fileStat = await vscode.workspace.fs.stat(uri);
    if (fileStat.type === vscode.FileType.Directory) {
      const dirNode: FileTreeNode = {
        fileName: uri.fsPath.substring(uri.fsPath.lastIndexOf('/') + 1),
        filePath: uri.fsPath.replace(/\\/g, '/'),
        fileType: 'directory',
        folder: uri.fsPath.replace(/\\/g, '/'),
        children: await readDir(uri),
      };
      result.push(dirNode);
    } else if (fileStat.type === vscode.FileType.File) {
      result.push(await readFile(uri));
    }
  }

  return result;
}

// 修改结构
export const modifyFileTreeNode = (tree: FileTreeNode[], translateType: string, rule?: string): FileTreeNode[] => {
  rule = rule ?? 'new-JoyCode';

  return tree.flatMap((node) => {
    // 创建当前节点的新副本
    const newNode: FileTreeNode = {
      ...node,
      children: node.children ? modifyFileTreeNode(node.children, translateType, rule) : undefined,
    };

    // 修改当前节点的 fileName 和 filePath
    if (newNode.fileType !== 'directory') {
      const languageType = newNode.fileType;
      const newLanguageType = getFileLanguage(translateType, languageType) ?? languageType;
      newNode.originalFileName = newNode?.fileName;
      newNode.originalFilePath = newNode?.filePath;
      newNode.fileName = `${rule}-${newNode.fileName.replace('.' + languageType, '')}.${newLanguageType}`;
      newNode.filePath = newNode?.filePath
        ?.replace(/([^/]+)$/, `${rule}-$1`)
        .replace('.' + languageType, '.' + newLanguageType);
      newNode.fileContent = newNode?.fileContent ?? '';
      newNode.fileType = newLanguageType;

      // 如果是 TSX 或 JSX 文件，生成对应的 CSS 文件
      if (newLanguageType === 'tsx' || newLanguageType === 'jsx') {
        const cssFileName = `${rule}-${node.fileName.replace(/\.(tsx|jsx)$/, '')}.css`;
        const cssFilePath = newNode.filePath?.replace(/\.(tsx|jsx)$/, '.css');
        const cssFileNode: FileTreeNode = {
          fileName: cssFileName,
          filePath: cssFilePath,
          fileType: 'css',
          fileContent: newNode?.fileContent ?? '', // 可以根据需要添加默认的 CSS 内容
          originalFileName: cssFileName,
          originalFilePath: cssFilePath,
        };

        return [newNode, cssFileNode];
      }
    }

    return [newNode];
  });
};

/**
 * 异步读取文件内容
 * @param filePath 文件路径
 * @returns 文件内容字符串
 */
export async function getFileContent(filePath: string) {
  return new Promise<string>((resolve, reject) => {
    try {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          resolve('');
          return;
        }
        resolve(data);
      });
    } catch (error) {
      reject(`${error}`);
    }
  });
}

function getFileLanguage(translateType: string, languageType: string) {
  translateType = translateType?.toLowerCase();
  languageType = languageType?.toLowerCase();
  const fileLanguage = {
    'vue2.x': 'vue',
    'vue3.x': 'vue',
    'react-js': 'jsx',
    'react-ts': 'tsx',
    'taro-react-ts': 'tsx',
    'taro-react': 'tsx',
    'taro-vue-ts': 'tsx',
    flutter3: 'dart',
    java: 'java',
    'harmony-arkts': 'ts',
  };
  const feLanguage = ['react-ts', 'react-js', 'vue3.x', 'vue2.x', 'taro-react', 'taro-vue-ts'];
  const TsJsCSSLanguage = ['ts', 'js', 'css', 'sass', 'scss', 'less'];
  if (feLanguage.includes(translateType) && TsJsCSSLanguage.includes(languageType)) {
    return translateType;
  }

  return fileLanguage[translateType] ?? '';
}

/**
 * 检测内容所属的编程语言。
 * @param content - 要检测的内容。
 * @returns 匹配的语言标识符，如果没有匹配则返回 null。
 */
export async function detectLanguageFromContent(content) {
  // 获取所有已注册的语言
  const languages = await vscode.languages.getLanguages();

  // 遍历所有语言，尝试匹配内容
  for (const language of languages) {
    const languageId = language;

    // 创建一个虚拟的 TextDocument 对象
    const document = await vscode.workspace.openTextDocument({
      language: languageId,
      content: content,
    });

    // 使用 `vscode.languages.match` 方法来判断内容是否匹配该语言
    const score = vscode.languages.match({ language: languageId }, document);
    if (score > 0) {
      return languageId;
    }
  }

  // 如果没有匹配的语言，返回 null 或其他默认值
  return null;
}

export function getFileType(languageId) {
  // 语言 ID 到文件扩展名的映射
  const languageToExtensionMap = {
    typescript: '.ts',
    javascript: '.js',
    python: '.py',
    java: '.java',
    csharp: '.cs',
    cpp: '.cpp',
    go: '.go',
    ruby: '.rb',
    php: '.php',
    dart: '.dart',
    html: '.html',
    css: '.css',
    json: '.json',
    scss: '.scss',
    sass: '.sass',
    less: '.less',
    vue: '.vue',
    typescriptreact: '.tsx',
    javascriptreact: '.jsx',
  };
  return languageToExtensionMap[languageId];
}
/**
 * 将文件树扁平化为文件节点数组。
 * @param {vscode.Uri[]} uris - 文件目录的URI数组。
 * @returns {Promise<FileTreeNode[]>} - 扁平化后的文件节点数组。
 */
export async function flattenFileTree(uris: vscode.Uri[]): Promise<FileTreeNode[]> {
  const tree = await getDirectoryTree(uris);
  const flatList: FileTreeNode[] = [];

  function flatten(nodes: FileTreeNode[]) {
    for (const node of nodes) {
      flatList.push({
        fileName: node.fileName,
        filePath: node.filePath,
        fileContent: node.fileContent,
        fileType: node.fileType,
        folder: node.folder,
        children: [],
      });
      if (node.children && node.children.length > 0) {
        flatten(node.children);
      }
    }
  }

  flatten(tree);
  return flatList;
}

/**
 * 批量替换文件内容并删除临时文件
 * @param nodes 文件树节点数组
 */
export async function batchReplacementFile(nodes: FileTreeNode[]) {
  try {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      try {
        const fileContent = await getFileContent(node.filePath ?? '');
        if (fileContent.length > 0) {
          fs.writeFileSync(node.originalFilePath ?? '', fileContent);
          // 删除临时文件
          fs.unlinkSync(node.filePath ?? '');
        }
      } catch {}
      // 递归遍历子节点
      if (node.children && node.children.length > 0) {
        batchReplacementFile(node.children);
      }
    }
  } catch (error) {
    Logger.log('%c [ error ]-197', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

/**
 * 从文件中读取指定行的内容。
 * @param filePath - 文件路径。
 * @param startLine - 起始行号（从1开始）。
 * @param endLine - 结束行号（包含）。
 * @returns 包含指定行内容的字符串数组。
 */
export async function getLines(filePath: string, startLine: number, endLine: number) {
  return new Promise<string[]>((resolve, reject) => {
    try {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          resolve([]);
          return;
        }
        const lines = data.split('\n');
        const selectedLines = lines.slice(startLine, endLine);
        resolve(selectedLines);
      });
    } catch (error) {
      reject(`${error}`);
    }
  });
}
