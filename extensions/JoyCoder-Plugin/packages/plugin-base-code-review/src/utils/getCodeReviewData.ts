import { getVscodeConfig } from '@joycoder/shared';
// import * as vscode from 'vscode';
import { constant } from '@joycoder/plugin-base-ai/src/dialog/constant';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { ChatProxyService, Scene } from '@joycoder/plugin-base-ai/src/proxy';
import { Logger } from '@joycoder/shared';

export async function getCodeReviewData(
  originalContent: string,
  lang: string,
  isIHub?: boolean,
  iHubRules?: Record<string, string>[]
): Promise<any> {
  const modelConfig = getChatModelAndConfig();
  try {
    if (originalContent) {
      const message = getPromptStr(originalContent, lang, '', '', isIHub, iHubRules);
      const service = new ChatProxyService();
      const resGPT = await service.invoke({ ...modelConfig, message, scene: Scene.CodeReview });
      // 尝试用正则从```xxx```中取出代码，否则把所有返回当做代码
      const fullText = resGPT.fullText || '';
      const codeMatch = fullText.match(/```(\w+)?\s?([\s\S]*?)\s?```/);
      const crResults = codeMatch?.[2] ?? fullText;
      return {
        crId: resGPT.conversationId || '',
        model: modelConfig?.label,
        temperature: 0.1,
        crResults,
      };
    }
  } catch (err) {
    Logger.error(err);
    return {
      crId: '',
      model: modelConfig?.label,
      temperature: 0.1,
      crResults: [],
    };
  }
}

export const getPromptStr = (
  inputText?: string,
  lang?: string,
  promptType?: string,
  allCode?: string,
  isIHub?: boolean,
  iHubRules?: Record<string, string>[]
) => {
  if (isIHub) {
    const names = iHubRules?.map((item, i) => `${i + 1}. ` + item.name).join('\n');
    const rules = iHubRules?.map((item, i) => `${i + 1}. ` + item.description).join('\n');
    const goodCases = iHubRules?.map((item, i) => `${i + 1}. ` + item.goodCase).join('\n');
    const badCases = iHubRules?.map((item, i) => `${i + 1}. ` + item.badCase).join('\n');
    return `作为一位资深的代码评审专家，请你根据以下规则对提供的代码片段进行评审检查。
      ## 关键评审点包括:
      \n
      ${names}
      \n
      ## 具体评审细则如下:\n
      ${rules}
      \n
      ## 优秀代码示例:\n
        ${goodCases}
      \n
      ## 不良代码示例: \n
        ${badCases}
      \n
      请严格按照以下JSON格式输出评审结果:
      [
        {
          "name": "关键评审点",
          "description": "问题描述，限100字以内",
          "passed": true/false
        },
        ...
      ]

      ## 注意事项:
        1. 仅输出符合上述JSON格式的评审结果
        2. 命中任一[评审细则]或[关键评审点]，评审结果即为不通过
        2. 严格按评审细则检查，参考示例代码，给出通过/不通过结论
        3. 每条规则给出简洁具体的评审意见(限100字)
        4. 符合规则passed为true，否则为false
        5. 不符合规则时简要说明问题即可

      评审的代码语言为: ${lang || 'JavaScript'}

      现在请对以下代码进行严格评审检查:

      --- \n
      ${inputText}
      \n---

    `;
  }
  const otherRules = getVscodeConfig('JoyCode.config.codeReview.otherRules');
  const otherRuleStr = otherRules?.join?.('\n');
  const modelConfig = getChatModelAndConfig();
  if (!!promptType) {
    return `${constant(promptType, {
      lang,
    })}\n\n 提供的转换代码如下：--- \n${inputText} \n --- \n\n\n，参考的全量代码如下：\n\n${allCode}，\n\n`;
  }
  // 效果不好加--------
  if (modelConfig.chatApiModel.includes('Chatrhino')) {
    const promptStr = `以下是一个代码文件，请对以下代码进行评审，并输出完整代码：\n---\`InputText\`: \n\`\`\`${inputText}\`\`\`\n---\n假设代码使用的编程语言是${lang}，\n\n作为代码评审专家，你的任务是对以上代码进行全面评审。你需要关注以下几个关键领域：“安全漏洞”、“错误和异常处理”、“逻辑错误”、“代码质量”、“性能问题”以及“编码规范”的遵循情况。\n\n评审规则如下：\n1. 针对代码块中识别到的问题进行代码优化，给出<改进建议的描述>和<改进后的全量代码>，将<改进建议的描述>放到\"content\"，将<改进后的全量代码>放到 \"optimizedCode\"中。\n2. 如果多条建议针对的是同一起始行或者\`\"startLine\"\`相同，请将这些建议合并到单个JSON对象中。\n3. 起始行或者\`\"startLine\"\`和\`\"endLine\"\`是空行，不要输出建议。\n4. 请只提供关于重大和关键问题的改进建议，忽略次要或中等重要性的建议。\n5. 请只提供**一条**重大且关键的改进建议。\n6. 改进建议的描述应简洁明了，不超过150个字。\n\n参考的团队代码评审规范如下：\n${otherRuleStr}。\n\n按照下面格式输出评审意见：\n\`\`\`json\n[{\n \"startLine\": \"建议的起始行号\",\n \"endLine\": \"建议的结束行号\",\n \"content\": \"<改进建议的描述>\",\n \"optimizedCode\": \"<改进后的全量代码>\"\n}]\n\`\`\`\n**注意：如果\"optimizedCode\"里包含双引号\`\"\`或者反斜杠\`\\\`，那么就要转义，在 \`\"\`或\`\\\`前输出 \`\\\`。**\n**注意：<改进后的全量代码>要给出InputText修改后的所有代码，包括所有的comments和没有修改的代码。**\n\n\`\`\`json\n[{\n \"startLine\": \"建议的起始行号\",\n \"endLine\": \"建议的结束行号\",\n \"content\": \"<改进建议的描述>\",\n \"optimizedCode\": \"<改进后的全量代码>\\\\n\\\"包括这句话\\\"\\\\n需要被转义\"\n}]\n\`\`\``;
    return promptStr;
  }
  const promptStr = `作为一个代码评审专家，你的任务是根据提供的代码片段，使用指定的编程语言进行全面的代码评审。你需要关注以下几个关键领域：安全漏洞、错误和异常处理、逻辑错误、代码质量、性能问题以及编码规范的遵循情况。
  \n
  请按照以下要求，将你的评审建议格式化为JSON对象输出：

  - 每个JSON对象必须包含四个键：\`"startLine"\`（建议的起始行号），\`"endLine"\`（建议的结束行号），\`"content"\`（改进建议的描述），以及\`"optimizedCode"\`（改进后的全量代码）。
  - 把改进后的全量代码赋值给第一个对象的“optimizedCode”，必须一定要输出改进后的全量代码不能省略改进后的全量代码。
  - 如果多条建议针对的是同一起始行或者startLine（起始行）相同，请将这些建议合并到单个JSON对象中。
  - 起始行或者startLine（起始行）和endLine（结束行）是空行，不要输出建议。
  - 请只提供关于重大和关键问题的改进建议，忽略次要或中等重要性的建议。
  - 如果返回的数据超出最大tokens，请只提供一条重大且关键的改进建议。
  - 改进建议的描述应简洁明了，不超过150个字。

  示例输出JSON格式如下：
  [{\"startLine\": 2, \"endLine\": 30, \"content\": \"存在无限循环的风险\", \"optimizedCode\": \"const line:number = 1;\"}]

  **注意：**仅需输出改进建议的JSON对象。

  现在，请根据这些指示开始对以下代码进行评审：--- \n${inputText} \n ---

  假设代码使用的编程语言是${lang || 'javascript'}，\n\n参考的团队代码评审规范如下：\n\n${otherRuleStr}。`;
  return JSON.stringify(promptStr);
};
