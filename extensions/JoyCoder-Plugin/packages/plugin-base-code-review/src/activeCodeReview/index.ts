import * as vscode from 'vscode';
import * as cp from 'child_process';
import _ from 'lodash';
import { ActionType, ImmediateChangeConfiguration, Logger } from '@joycoder/shared';
import { SecCommitCheckParams } from '@joycoder/plugin-base-sec/src/SecTreeDataProvider';
import { updateSecIssuesPanelData } from '@joycoder/plugin-base-sec';

/**
 * ImmediateCodeReview 类用于处理即时代码审查功能。
 */
export default class ActiveCodeReview {
  onDidChangeActiveDisposable: vscode.Disposable = new vscode.Disposable(() => {});
  context: vscode.ExtensionContext | null = null;
  previousEditor = vscode.window.activeTextEditor;
  constructor(context) {
    this.context = context;
  }
  immediateCodeReview() {
    new ImmediateChangeConfiguration({
      commandId: 'JoyCode.config.codeReview.immediate',
      onChange: (isImmediate) => {
        this.init(isImmediate);
      },
    });
  }

  init(isImmediate) {
    try {
      if (isImmediate) {
        // 防抖7秒调用一次
        const debounceInitEvent = _.debounce(async (editor: vscode.TextEditor) => {
          await this.initEvent(editor);
        }, 7000);
        this.onDidChangeActiveDisposable && this.onDidChangeActiveDisposable.dispose();
        this.onDidChangeActiveDisposable = vscode.window.onDidChangeActiveTextEditor(
          async (editor: vscode.TextEditor) => {
            debounceInitEvent(editor);
          }
        );

        this.context && this.context.subscriptions.push(this.onDidChangeActiveDisposable);
      }
    } catch (error) {
      Logger.error('%c [ error ]-45', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
  async initEvent(editor) {
    try {
      const diffInfo: SecCommitCheckParams = await this.getGitDiffFileContent();
      this.previousEditor = editor;
      if (!diffInfo.diffContent || diffInfo.code === diffInfo.diffContent) {
        return;
      }
      const paths: SecCommitCheckParams[] = [diffInfo];
      updateSecIssuesPanelData(paths);
    } catch (error) {
      Logger.error(error);
    }
  }
  async getGitDiffFileContent(): Promise<SecCommitCheckParams> {
    return new Promise((resolve, reject) => {
      try {
        const activeEditor = this.previousEditor;
        if (!activeEditor) {
          resolve({ fileName: '', filePath: '', fileLanguage: '', code: '' });
          return;
        }
        const document = activeEditor.document;
        const fileContent = document.getText();
        const uri = document.uri;
        const fileName = document.fileName;
        const fileLanguage = document.languageId;
        if (uri.scheme !== 'file') {
          resolve({
            fileName,
            filePath: '',
            fileLanguage,
            code: fileContent,
            fileType: fileLanguage,
            fileSource: ActionType.activeCodeReviewEnum,
          });
          return; // 当前文档不是文件
        }
        const filePath = uri.fsPath;
        const gitCommand = `git diff \"${filePath}\"`;
        cp.exec(gitCommand, { cwd: vscode.workspace.rootPath }, (err, stdout) => {
          if (err) {
            resolve({
              fileName,
              filePath,
              fileLanguage,
              code: fileContent,
              fileType: fileLanguage,
              fileSource: ActionType.activeCodeReviewEnum,
            });
            return;
          }
          stdout = stdout.length > 0 ? stdout : fileContent;
          resolve({
            fileName,
            filePath,
            fileLanguage,
            code: fileContent,
            diffContent: stdout,
            fileType: fileLanguage,
            fileSource: ActionType.activeCodeReviewEnum,
          });
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
