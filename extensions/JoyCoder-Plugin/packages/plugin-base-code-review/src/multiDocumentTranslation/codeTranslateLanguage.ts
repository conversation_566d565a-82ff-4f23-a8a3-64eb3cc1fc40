import * as fs from 'fs';
import { CodeTranslateChatType } from '@joycoder/plugin-base-ai/src/dialog/constant';
import { ActionType, Logger, getRemoteConfigSync, getVscodeConfig, reportAction } from '@joycoder/shared';
import FileTreeNode from '../tree/fileTreeNode';
import { getPromptStr } from '../utils/getCodeReviewData';
import { ChatModelConfig, getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { ChatGPTAskResponseData, ChatProxyService, Scene } from '@joycoder/plugin-base-ai/src/proxy';
import { findTopLevelDeclarations } from '@joycoder/plugin-base-code-completion/src/utils/elidableText';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';

export interface IReportParam {
  question: string;
  modelConfig: ChatModelConfig;
  result: any;
  accept: number;
}
/**
 * CodeTranslateLanguage类用于处理代码翻译。
 */
class CodeTranslateLanguage {
  fileType: string = CodeTranslateChatType['Flutter3'];
  fileNode: FileTreeNode;
  abortControllers: Map<string, AbortController> = new Map();
  maxReqLines = getVscodeConfig('JoyCode.config.translationRows') || 200;
  endLine: number;

  constructor(fileNode: FileTreeNode, fileType?: string) {
    this.fileNode = fileNode;
    this.fileType = fileType ?? this.fileType;
  }

  /**
   * 异步获取翻译后的数据。
   *
   * @returns {Promise<boolean | FileNode>} 翻译成功返回文件节点，失败返回false。
   */
  async getTranslateData() {
    if (this.fileNode.fileType != 'directory') {
      const message = getPromptStr(this.fileNode.fileContent, this.fileNode.fileLanguage || '', this.fileType);
      const modelConfig = getChatModelAndConfig();
      const requestId = this.generateRequestId(); // 生成唯一请求ID
      const abortController = new AbortController();
      this.abortControllers.set(requestId, abortController);

      try {
        const { resGPT, result: crResults } = await this.getCodeBlockTranslateData(modelConfig, requestId);
        this.startReportLog({
          question: message,
          modelConfig: modelConfig,
          result: { ...resGPT, fullText: this.fileNode?.fileContent ?? '' },
          accept: 0,
        });
        if (crResults) {
          this.fileNode.transformCodeReport = {
            question: message,
            modelConfig: modelConfig,
            result: { ...resGPT, fullText: crResults },
            accept: 1,
          } as IReportParam;
          try {
            const fileStatus = await this.generateFiles(crResults);
            if (fileStatus) {
              return this.fileNode;
            }
          } catch {}
        }
      } finally {
        this.abortControllers.delete(requestId); // 请求完成后删除对应的AbortController
      }
    }
    return false;
  }
  /**
   * 异步获取代码块翻译数据
   * @param modelConfig - 模型配置
   * @param requestId - 请求ID
   * @returns 翻译后的代码块
   */
  async getCodeBlockTranslateData(modelConfig, requestId) {
    let result = '';
    let resGPT: ChatGPTAskResponseData = {} as ChatGPTAskResponseData;
    try {
      const code = this.fileNode.fileContent ?? '';
      const lines = code.split('\n');
      const roundedUp = Math.ceil(lines.length / this.maxReqLines);
      // 使用 Promise.all 并行处理多个代码块
      const promises = Array.from({ length: roundedUp }, (_, i) =>
        this.processCodeBlock(lines, i, modelConfig, requestId)
      );
      const results = await Promise.all(promises);
      // 合并所有结果
      results.forEach(({ crResults }) => {
        result += crResults ?? '' + '\n';
      });
      // result = this.mergeAndRemoveDuplicates(result);
      resGPT = results?.[results?.length - 1]?.resGPT;
    } catch (error) {
      Logger.log('%c [ error ]-99', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    return { resGPT, result };
  }

  async processCodeBlock(lines, index, modelConfig, requestId) {
    const code = this.fileNode.fileContent ?? '';
    const startLine = index * this.maxReqLines;
    const maxLines = (index + 1) * this.maxReqLines;
    const totalLines = lines.length;
    const endLine = maxLines > totalLines ? totalLines : maxLines;
    const codeBlock = lines.slice(startLine, endLine).join('\n');
    let message = getPromptStr(codeBlock, '', this.fileType, code);
    if (totalLines > this.maxReqLines) {
      message = message.replace('$_$_$_$', ``);
      message += `\n要求如下：\n
      -- 仅转换提供的转换代码，不要转换全量代码。\n
      -- 与本次转换无关代码不要输出，重复代码不要输出。\n
      -- 本次仅输出转换后的代码，不用关注完整性，但是不能丢失结尾的任何字符。\n
      **注意：**仅输出转换后的代码`;
    } else {
      message = message.replace('$_$_$_$', ``);
    }
    const respMaxTokens = getVscodeConfig('JoyCode.config.translationTokens') || 2000;
    const resGPT = await new ChatProxyService().invoke(
      { ...modelConfig, message, scene: Scene.Translate, respMaxTokens },
      this.getOptions(requestId)
    );
    const fullText = resGPT.fullText || '';
    let crResults = '';
    let codeTypes: string[] = [];
    if (!fullText.startsWith('网关错误信息') && (resGPT.id || resGPT.conversationId)) {
      // const codeMatch = fullText.match(/```(\w+)?\s*([\s\S]*?)\s*```/);
      // crResults = codeMatch?.[2] ?? fullText;
      const codeMatches = [...fullText.matchAll(/```(\w+)?\s*([\s\S]*?)\s*```/g)];
      crResults = codeMatches.map((match) => `<${match[1]}>${match[2]}</${match[1]}>`).join('\n');
      codeTypes = codeMatches.map((match) => match[1]);
    }
    return { crResults, resGPT, codeTypes };
  }

  getDPTCompletionOption(originalContent, lang) {
    const model = getVscodeConfig('JoyCode.config.chatModel', 'GPT-4-OMNI') as string;
    const modelConfig = (getRemoteConfigSync().chatGPTModelConfigs || []).find((config) => config.label == model) || {};
    return modelConfig ? { ...modelConfig, originalContent, lang } : modelConfig;
  }

  getOptions(requestId: string) {
    const options = {
      abortController: this.abortControllers.get(requestId),
      onProgress: () => {},
    };
    this.fileNode.controllerId = requestId;
    this.fileNode.fileController = this.abortControllers.get(requestId);
    return options;
  }

  /**
   * 生成文件。
   * 成功创建文件后返回true。
   * @returns {Promise<boolean>} 表示是否成功生成文件的Promise对象。
   */
  generateFiles(content: string) {
    try {
      return new Promise<boolean>((resolve, reject) => {
        try {
          const fileName = this.fileNode.fileName;
          const filePath = this.fileNode.filePath;
          let fileText = '';
          const regex: RegExp | null = /<(\w+)>([\s\S]*?)<\/\1>/g; // 修正正则表达式，使用 [\s\S] 来匹配任意字符，包括换行符
          const matches: Array<{ content: string; lang: string }> = [];
          let match: RegExpExecArray | null = null;
          while ((match = regex.exec(content)) !== null) {
            matches.push({ content: match[2] || '', lang: match[1] || '' });
          }
          if (fileName.includes('.css')) {
            const cssMatches = matches.map((match) => (match.lang.toLowerCase() === 'css' ? match.content : ''));
            fileText = cssMatches.join('\n');
          } else {
            const nonCssMatches = matches.map((match) => (match.lang.toLowerCase() !== 'css' ? match.content : ''));
            fileText = nonCssMatches.join('\n');
          }
          if (!fileName || !filePath) {
            resolve(false);
            return;
          }
          // 创建文件
          fs.writeFile(filePath, fileText, (err) => {
            if (err) {
              resolve(false);
              return;
            }
            resolve(true);
          });
        } catch (error) {
          reject(false);
        }
      });
    } catch (error) {
      Logger.warn('%c [ error ]-67', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }

  getFileNode() {
    return this.fileNode;
  }
  getAbortController(requestId: string) {
    return this.abortControllers.get(requestId);
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 启动报告日志
   * @param {Object} params - 参数对象
   * @param {string} params.question - 问题
   * @param {Object} params.modelConfig - 模型配置
   * @param {Object} params.result - 结果
   * @param {boolean} params.accept - 是否接受
   * @returns {void}
   */
  startReportLog({ question, modelConfig, result, accept }) {
    const actionType = accept == 1 ? 'copy' : ActionType.batchCodeTranslateEnum;
    try {
      reportAction({
        question,
        accept,
        actionCate: 'ai',
        actionType: actionType as ActionType,
        conversationId: result.conversationId,
        model: modelConfig.label,
        result: result.fullText,
      });
      AdoptResultCache.setRemote(
        result.fullText,
        modelConfig.label,
        AdoptResultCache.ADOPT_CODE_SOURCE.BATCH_CODE_TRANSLATE,
        result.conversationId
      );
    } catch (error) {
      Logger.warn('%c [ error ]-144', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
  /**
   * 根据文件类型和代码文本获取语言ID。
   * @param fileType 文件类型。
   * @param codeText 代码文本。
   * @returns 对应的语言ID。
   */
  getLanguageId(fileType, codeText) {
    let isTs = false;
    try {
      isTs = codeText.match(/<script.*?lang=["'](ts|typescript)["'].*?(setup)?.*?>/i);
    } catch (error) {}
    const languageIdMapping = {
      py: 'python',
      js: 'javascript',
      vue: isTs ? 'typescript' : 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      tsx: 'typescriptreact',
      go: 'go',
      ruby: 'ruby',
      java: 'java',
      dart: 'dart',
      jue: 'javascript',
    };

    return languageIdMapping[fileType];
  }
  /**
   * 异步查找最接近的类节点。
   *
   * @param languageId - 语言标识符。
   * @param code - 源代码字符串。
   * @param targetLine - 目标行号。
   * @returns 最接近的类节点。
   */
  async findClosestClassNode(languageId, code, targetLine): Promise<{ endPosition: { row: number } } | null> {
    let closestClass: { endPosition: { row: number } } | null = null;
    let minDistance = Infinity;
    const classes = await findTopLevelDeclarations(languageId, code);
    classes.forEach((classNode) => {
      const classStartLine = classNode.endPosition.row;
      const distance = Math.abs(classStartLine - targetLine);
      if (distance < minDistance) {
        minDistance = distance;
        closestClass = classNode;
      }
    });
    return closestClass;
  }
  getNodesInRange(tree, startLine, endLine) {
    const nodes: string[] = [];
    const rootNode = tree.rootNode;
    rootNode?.namedChildren &&
      rootNode.namedChildren.forEach((node) => {
        if (node.startPosition.row >= startLine && node.endPosition.row <= endLine) {
          nodes.push(node.text);
        }
      });

    return nodes;
  }
  mergeAndRemoveDuplicates(...strings: string[]): string {
    try {
      // 将所有字符串按行分割成数组
      const lines = strings.flatMap((str) => str.split('\n'));
      // 使用 Set 去重
      const uniqueLines = Array.from(new Set(lines));
      // 将数组重新合并成字符串
      return uniqueLines.join('\n');
    } catch (error) {
      Logger.warn('%c [ error ]-291', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    return strings.join('\n');
  }
}
export default CodeTranslateLanguage;
