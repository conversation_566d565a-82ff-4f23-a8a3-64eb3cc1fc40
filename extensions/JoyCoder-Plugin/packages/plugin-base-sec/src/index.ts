import * as vscode from 'vscode';
import * as child_process from 'child_process';
const fs = require('fs');
const os = require('os');
const path = require('path');
import * as cp from 'child_process';
import * as diff from 'diff';
import {
  hasJdhLoginCookie,
  forceJdhLogin,
  reportUmp,
  getCurrentFileType,
  getJdhLoginInfo,
  setVscodeConfig,
  getVscodeConfig,
  ActionType,
  Logger,
  reportAction,
  countCodeLinesInMarkdown,
} from '@joycoder/shared';
import { needUpdateVsCodeGuide } from '@joycoder/version';
import to from 'await-to-js';
import axios from 'axios';
import { fetchSSE } from '@joycoder/plugin-base-ai/src/proxy/sse';
import { v4 as uuidv4 } from 'uuid';
import { CommitMessage } from './commitMessage';
import { supportFileExtensions } from './supportFileExtensions';
import SecTreeNode from './SecTreeNode';
import { saveCheckRecord } from './SecTreeDataProvider';
import SecTreeDataProvider, { HistoryResult, SecCommitCheckParams } from './SecTreeDataProvider';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';

export const secBaseUrl = 'http://llm-code-vuln.jd.com';
const secCodeReg = /```(.*?)\s*((.|\s)*?)\s```/g;
export const historyTmpFileName = 'joycoder-sec-fix-results.json';
export const inspectionRecordFileName = 'joycoder-sec-commit-results.json';

const initCommitList = [{ fileName: '', filePath: '', fileLanguage: '', code: '', type: 'init' }];
let isInWhiteList = false;
let secTreeDataProvider: SecTreeDataProvider | undefined = undefined;
let secIssuesTreeView: vscode.TreeView<SecTreeNode> | undefined = undefined;
let hoverProviderDisposable: vscode.Disposable | undefined = undefined;
let commitHash = '';

interface SecAskParams {
  code: string;
}

export interface SecAskResponse {
  answer?: string;
}

export interface SecAskResponseData {
  text: string;
  fullText: string;
  id?: string;
  parentMessageId?: string;
  role?: string;
  conversationId?: string;
  error?: any; // 调集团网关失败时有该字段
}

export const result: SecAskResponseData = {
  text: '',
  fullText: '',
  conversationId: '',
  id: '',
  parentMessageId: '',
  role: 'assistant',
};

export interface commitCheckRecord {
  commitId: string;
  timestamp: number;
}

export const commitCheckRecord: commitCheckRecord = {
  commitId: '',
  timestamp: 0,
};

export function resetSecGPT() {
  result.text = '';
  result.fullText = '';
  result.conversationId = '';
  result.id = '';
  result.parentMessageId = '';
}

/**
 * 右键神医安全检查
 */
export async function askSecGPT(
  params: SecAskParams,
  options?: {
    abortController: AbortController;
    onProgress: (response: SecAskResponseData) => void;
  }
) {
  resetSecGPT();
  let activeEditor = {};
  const editor = vscode.window.activeTextEditor;
  if (editor) {
    // 获取当前检查文件相关信息
    activeEditor = {
      documentText: editor?.document.getText(),
      language: editor?.document.languageId,
      selectionStart: editor?.selection.start,
      selectionEnd: editor?.selection.end,
      uri: editor?.document.uri,
      viewColumn: editor?.viewColumn,
      document: editor?.document,
    };
  }
  const response = await secGPT(params, options);
  // 有返回信息且返回信息不为集团安全神医大模型未发现安全漏洞，进行diff操作
  if (response.fullText && response.fullText != response.text) codeDiffSecRight(activeEditor, response.fullText);
  // if (response.fullText == '') {
  //   response.fullText = '';
  // }
  return response;
}

export async function secGPT(
  params: SecAskParams,
  options?: {
    abortController: AbortController;
    onProgress: (response: SecAskResponseData) => void;
  }
) {
  // 校验登录态
  const isLogin = !!hasJdhLoginCookie();
  const loginGuideText = `未登录，<a id='forceLoginBtn' href="javascript:void(0);">请登录后重试</a>`;

  if (!isLogin) {
    const [err, isLogined] = await to(forceJdhLogin());
    // 点击取消授权或登录接口失败时
    if (err || !isLogined) {
      result.text = loginGuideText;
      result.fullText = loginGuideText;
      return result;
    }
  }
  const languageId = getCurrentFileType();
  const body = {
    code: '```' + languageId + '\n' + params.code + '\n```',
    erp: (getJdhLoginInfo()?.erp || '') + '_v',
  };

  return new Promise<SecAskResponseData>((resolve, reject) => {
    fetchSSE(`${secBaseUrl}/fix_vuln_general`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      signal: options?.abortController.signal,
      onMessage: async (res: string) => {
        try {
          // console.warn('神医安全助手-fix_vuln_general-右键安全检查接口返回内容：' + res);
          // 返回数据结束
          if (res == '[DONE]') {
            result.text = result.text.trim();
            result.fullText = result.fullText.trim();
            result.id = result.id || uuidv4();
            result.conversationId = result.conversationId || uuidv4();
            console.warn('神医安全助手-fix_vuln_general-右键安全检查接口完整内容：' + result.fullText);
            return resolve(result);
          }
          const response: SecAskResponse = JSON.parse(res);
          // const dataStr = String(data);
          const text = response?.answer || '';
          // 返回数据中
          if (text) {
            result.text = text;
            result.fullText = result.fullText + text;
            result.id = result.id || uuidv4();
            result.conversationId = result.conversationId || uuidv4();
            options?.onProgress?.(result);
          }
        } catch (err) {
          console.warn('神医安全助手-fix_vuln_general-右键安全检查-error：', err);
          return reject(err);
        }
      },
    }).catch((error: Error) => {
      console.warn('神医安全助手-fix_vuln_general-右键安全检查-error：', error);
      // 只要不是主动中止的异常，则需要抛出异常
      // 参考：https://github.com/node-fetch/node-fetch/blob/main/docs/ERROR-HANDLING.md
      if (error.name !== 'AbortError') {
        console.warn('request error', error);
        reportUmp(26, 9999, error);
        return reject(error);
      }
    });
  });
}
// 安全检查结果(右键、增量)自动diff
function codeDiffSecRight(activeEditor, response: string, fixFlag = false, element?: SecTreeNode) {
  if (activeEditor?.uri) {
    const reg = secCodeReg;
    const codeArr = response.match(reg);
    // 神医安全检查返回第一段代码为替换代码
    if (codeArr && codeArr[0]) {
      // 创建临时文件的路径
      const tempFilePath = path.join(os.tmpdir(), `temp-${Date.now()}.${activeEditor.language}`);
      let newContent = codeArr[0].replace(/```(.*?)\n/, '').replace('\n```', '');
      if (activeEditor.replaceAll) {
        fs.writeFileSync(tempFilePath, newContent);
      } else {
        // 替换内容并写入临时文件,返回整个新文件内容
        newContent = replaceContentAndWriteToFile(
          activeEditor.documentText,
          {
            start: activeEditor.document.offsetAt(activeEditor.selectionStart),
            end: activeEditor.document.offsetAt(activeEditor.selectionEnd),
          },
          newContent,
          tempFilePath
        );
      }
      // diff操作
      vscode.commands
        .executeCommand('vscode.diff', vscode.Uri.file(tempFilePath), activeEditor.uri, 'New<->Old', {
          viewColumn: activeEditor.viewColumn,
        })
        .then(() => {
          if (fixFlag) {
            const { changeLines = 0, diffCode = '' } = getChangeLines(activeEditor, newContent);
            try {
              // 上报查看
              setCodeReviewReport(0, newContent, changeLines, element);
              setTimeout(() => {
                // 上报采纳
                setCodeReviewReport(1, newContent, changeLines, element);
              }, 10000);
            } catch (error) {}
            setTimeout(() => {
              vscode.window
                .showInformationMessage('是否要将检测后的修复代码一键替换到当前文档中？', '是', '否')
                .then((selection) => {
                  if (selection === '是') {
                    // 代码评审结果缓存，用于后续AI代码占比统计
                    const modelLabel = (getVscodeConfig('JoyCode.config.chatModel') as string) ?? '';
                    AdoptResultCache.setRemote(
                      diffCode,
                      modelLabel,
                      AdoptResultCache.ADOPT_CODE_SOURCE.CODE_REVIEW,
                      element?.id || ''
                    );
                    // 将新的内容写入原文件
                    fs.writeFileSync(activeEditor.uri.fsPath, newContent);
                    // 上报采纳
                    setCodeReviewReport(1, newContent, changeLines, element);
                  } else if (selection === '否') {
                  }
                });
            }, 5000);
          }
          // 删除临时文件
          fs.unlinkSync(tempFilePath);
        });
    }
  }
}

// 发起增量检测并更新commit增量检查结果树
// 增量检测主方法
export function updateSecIssuesPanelData(
  commitList: SecCommitCheckParams[],
  isHistory = false,
  historyList: HistoryResult[] = []
) {
  if (secIssuesTreeView) {
    if (secTreeDataProvider) {
      secTreeDataProvider.commitList = commitList;
      secTreeDataProvider.isHistory = isHistory;
      if (isHistory) secTreeDataProvider.historyList = historyList;
      secTreeDataProvider.refresh(); // 更新数据
    }
  } else {
    secTreeDataProvider = new SecTreeDataProvider(commitList, isHistory, historyList);
    secIssuesTreeView = vscode.window.createTreeView('secIssues', {
      treeDataProvider: secTreeDataProvider,
    });
  }
}

let hasShownVersionWarning = false; // 标志变量，用于跟踪是否已经显示过警告
// commit增量监听
async function handleListenCommit() {
  // 初始化神医安全增量检查结果为[]
  secTreeDataProvider = new SecTreeDataProvider(initCommitList);
  secIssuesTreeView = vscode.window.createTreeView('secIssues', {
    treeDataProvider: secTreeDataProvider,
  });
  const gitExtension = vscode.extensions.getExtension('vscode.git');
  if (gitExtension) {
    const git = await gitExtension.activate();
    const gitAPI = git.getAPI(1);
    gitAPI.onDidOpenRepository(() => {
      gitAPI.repositories.forEach((repo) => {
        try {
          const commitMes = new CommitMessage(repo);
          // 监听提交事件
          repo.onDidCommit(async () => {
            const commitMessage = commitMes.getCommitMessage() ?? '';
            if (commitMessage.length == 0) return;
            commitMes.setCommitMessage('');
            // 在白名单，并且没开启神医安全检查，自动开启
            handleInWhiteList();
            const isChecked =
              getVscodeConfig('JoyCode.config.codeReview.commit') || getVscodeConfig('JoyCode.config.shenYi.commit');
            if (isChecked || isInWhiteList) {
              if (commitHash != repo.state.HEAD.commit) {
                vscode.commands.executeCommand('JoyCoderFE.sec.showSecIssues');
                commitHash = repo.state.HEAD.commit;
                commitCheckRecord.commitId = commitHash;
                commitCheckRecord.timestamp = Date.now();
                saveCheckRecord(commitCheckRecord);

                const commitList = await getChangedFiles(repo.rootUri.fsPath, commitHash);
                if (commitList?.length) {
                  reportAction({
                    actionCate: 'sec',
                    actionType: ActionType.commitCheck,
                    extendMsg: {
                      vulnId: 0,
                    },
                  });
                  updateSecIssuesPanelData(commitList);
                }
              }
            } else {
              updateSecIssuesPanelData(initCommitList);
            }
          });
        } catch (err) {
          const isChecked =
            getVscodeConfig('JoyCode.config.codeReview.commit') || getVscodeConfig('JoyCode.config.shenYi.commit');
          // 如果还没有显示过警告，并且 VSCode 版本低于 1.86.2，则显示警告信息
          if (!hasShownVersionWarning && (isChecked || isInWhiteList)) {
            needUpdateVsCodeGuide('1.86.2');
            hasShownVersionWarning = true; // 更新标志变量，表示已经显示过警告
          }
        }
      });
    });
  }
}

export async function getChangedFiles(
  repoPath: string,
  commitHash: string
): Promise<{ fileName: string; filePath: string; fileLanguage: string; code: string }[]> {
  return new Promise((resolve, reject) => {
    // 获取初始化提交哈希值
    cp.exec('git rev-list --max-parents=0 HEAD', { cwd: repoPath }, (err, stdout) => {
      if (err) {
        console.error('神医安全助手获取初始化提交哈希值失败:', err);
        return;
      }
      const initialCommitHash = stdout.trim();

      // 显示指定提交与其父提交之间的文件变动情况
      let command = `git diff-tree --no-commit-id --name-status -r ${commitHash}`;

      // 特别处理初始化提交
      if (commitHash === initialCommitHash) {
        // 你需要知道或者判断这个哈希值
        command = `git show --pretty="" --name-status ${commitHash}`;
      }
      // 使用 git 命令获取最后一次提交的变动文件的名称和状态
      cp.exec(command, { cwd: repoPath }, (err, stdout) => {
        if (err) {
          reject(err);
          return;
        }
        // 解析 stdout 来获取文件列表和状态
        const fileList = stdout
          .trim()
          .split('\n')
          .map((line) => {
            const [status, fileName] = line.split('\t');
            return { status, fileName };
          })
          .filter(({ fileName }) => supportFileExtensions.includes(path.extname(fileName))); // 过滤不支持的文件类型;
        // 根据文件列表获取每个文件的详细信息
        const fileDetails = fileList.map(({ fileName }) => {
          const filePath = path.join(repoPath, fileName);
          const fileLanguage = path.extname(fileName);
          const code = fs.readFileSync(filePath, 'utf-8'); // 同步读取文件内容
          return {
            fileName,
            filePath,
            fileLanguage: fileLanguage.split('.')[1],
            code,
            fileSource: ActionType.commitCodeReviewEnum,
          };
        });

        resolve(fileDetails);
      });
    });
  });
}

// commit增量检查问题树节点点击打开该文件并导航到安全问题区
let currentDecorationType: vscode.TextEditorDecorationType | undefined = undefined; // 跟踪当前的装饰类型
function handleOpenCommitFileAndNavigate(element: SecTreeNode, context: vscode.ExtensionContext) {
  if (element.filePath) {
    vscode.workspace.openTextDocument(element.filePath).then((doc) => {
      vscode.window.showTextDocument(doc).then((editor) => {
        if (element.code == editor.document.getText()) {
          if (element.start_line && element.end_line) {
            const st = element.start_line - 1; // 行号从0开始
            const et = element.end_line;
            // 获取起始行的第一个非空白字符的索引
            const startCharacter = vscode.window.activeTextEditor?.document.lineAt(st).firstNonWhitespaceCharacterIndex;
            const range = new vscode.Range(st, startCharacter || 0, et, 0);
            editor.revealRange(range, vscode.TextEditorRevealType.InCenter);
            // 清除之前的装饰
            if (currentDecorationType) {
              editor.setDecorations(currentDecorationType, []); // 使用空数组清除装饰
              currentDecorationType.dispose(); // 释放之前的装饰类型资源
            }
            // 创建新的装饰类型
            currentDecorationType = vscode.window.createTextEditorDecorationType({
              overviewRulerLane: vscode.OverviewRulerLane.Right,
              backgroundColor: '#FFEFD5',
              overviewRulerColor: '#FFEFD5',
            });
            editor.setDecorations(currentDecorationType, [range]);
            // 如果之前有注册过悬停提供器，先注销
            if (hoverProviderDisposable) {
              hoverProviderDisposable.dispose();
            }

            // 注册悬停提供器
            const hoverProvider = {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              provideHover(_document, position, _token) {
                if (range.contains(position)) {
                  const contents = new vscode.MarkdownString(
                    `### ${element.cwe}: ${element.vulnerability}\n\n${element.comment}\n___\n##### [${
                      element?.cwe?.startsWith('CR-MULTI') ? 'JoyCode代码评审快速修复' : '神医安全快速修复'
                    }](command:JoyCoderFE.sec.fixCommitIssue?${encodeURIComponent(JSON.stringify(element))})`
                  );

                  contents.isTrusted = true; // 允许执行命令
                  return new vscode.Hover(contents, range);
                }
              },
            };

            // 注册新的悬停提供器，并保存 Disposable 引用
            hoverProviderDisposable = vscode.languages.registerHoverProvider('*', hoverProvider);

            context.subscriptions.push(
              // 注册悬停提供器，适用于所有文件类型
              hoverProviderDisposable
            );
          }
        } else {
          // 文件改动后，原提示销毁
          if (hoverProviderDisposable) {
            hoverProviderDisposable.dispose();
          }
        }
      });
    });
  }
}

function replaceContentAndWriteToFile(originalContent, range, replacement, tempFilePath) {
  // 获取替换前的内容
  const beforeRange = originalContent.slice(0, range.start);
  // 获取替换后的内容
  const afterRange = originalContent.slice(range.end);

  // 创建新的内容
  const newContent = beforeRange + replacement + afterRange;

  // 将新的内容写入临时文件
  fs.writeFileSync(tempFilePath, newContent);
  return newContent;
}

async function fixCommitIssue(element: SecTreeNode) {
  let activeEditor = {};
  const editor = vscode.window.activeTextEditor;
  if (editor && element.start_line && element.end_line) {
    activeEditor = {
      documentText: editor?.document.getText(),
      language: element.fileLanguage,
      uri: editor?.document.uri,
      viewColumn: editor?.viewColumn,
      replaceAll: true,
    };
  }
  if (element.fixResult) {
    if (element?.cwe?.startsWith('CWE-')) {
      reportAction({
        actionCate: 'sec',
        actionType: ActionType.commitFix,
        extendMsg: {
          vulnId: element?.id,
        },
      });
    }
    codeDiffSecRight(activeEditor, element.fixResult, true, element);
  }
}

function showCommitHistory() {
  const tempDir = os.tmpdir();
  const tempFileName = historyTmpFileName;
  const tempFilePath = path.join(tempDir, tempFileName);
  let fixResults: HistoryResult[] = [];

  // 尝试读取现有的修复结果文件
  if (fs.existsSync(tempFilePath)) {
    try {
      fixResults = JSON.parse(fs.readFileSync(tempFilePath, 'utf8'));
      updateSecIssuesPanelData([], true, fixResults);
    } catch (err) {
      vscode.window.showErrorMessage('Failed to read or parse existing sec fix results: ' + err);
    }
  } else {
    vscode.window.showInformationMessage('神医安全近三天无增量检查历史');
  }
}

// 开启神医检测部门人员白名单
export async function getIsInWhiteList() {
  try {
    const res = await axios.get(`http://shenyisec.jd.com/joycoderhelp/isWhite2Commit`, {
      params: {
        erp: getJdhLoginInfo()?.erp || '',
      },
    });
    return res.data;
  } catch (error) {
    console.error('神医安全助手: /isWhite2Commit-' + error?.message);
  }
}

function handleInWhiteList() {
  if (
    (!getVscodeConfig('JoyCode.config.shenYi.commit') || !getVscodeConfig('JoyCode.config.codeReview.commit')) &&
    isInWhiteList
  ) {
    setVscodeConfig('JoyCode.config.shenYi.commit', true);
    setVscodeConfig('JoyCode.config.codeReview.commit', true);
  }
}

export default async function (context: vscode.ExtensionContext) {
  isInWhiteList = await getIsInWhiteList();
  handleInWhiteList();
  // context.subscriptions.push(vscode.commands.registerCommand('JoyCoderFE.sec.listenCommit', handleListenCommit));
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCoderFE.sec.showSecIssues', () => {
      // 执行命令时的逻辑，显示secIssues视图
      vscode.commands.executeCommand('workbench.view.extension.secIssues').then(
        () => {
          // 视图已显示
        },
        (err) => {
          // 处理错误
          vscode.window.showErrorMessage('Failed to show sec issues: ' + err);
        }
      );
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCoderFE.sec.openCommitFileAndNavigate', (element) => {
      handleOpenCommitFileAndNavigate(element, context);
    })
  );
  context.subscriptions.push(
    // 注册有安全问题代码修复命令
    vscode.commands.registerCommand('JoyCoderFE.sec.fixCommitIssue', (element) => {
      fixCommitIssue(element);
    })
  );
  context.subscriptions.push(
    // 注册有安全问题代码修复命令
    vscode.commands.registerCommand('JoyCoderFE.sec.showCommitHistory', () => {
      showCommitHistory();
    })
  );
  // vscode.commands.executeCommand('JoyCoderFE.sec.listenCommit');
  handleListenCommit();
}

/**
 * 计算两个文本内容的差异。
 * @param oldContent - 旧的文本内容。
 * @param newContent - 新的文本内容。
 * @returns 文本差异的结果。
 */
function calculateDiff(oldContent, newContent) {
  try {
    const diffResult = diff.diffLines(oldContent, newContent);
    return diffResult;
  } catch (error) {
    Logger.error(error);
  }
  return '';
}

function setCodeReviewReport(accept: number, newContent, changeLines, element?: SecTreeNode) {
  const actionType = accept == 1 ? 'copy' : element?.fileSource; //  采纳之后actionType统一为copy
  reportAction({
    accept,
    actionCate: 'ai',
    actionType: actionType as ActionType,
    conversationId: element?.id,
    result: newContent,
    extendMsg: {
      //变更行数（本次新增字段）
      changeLines,
      //是否查看diff窗口
      isOpenDiff: 1,
    },
  });
}

function getChangeLines(activeEditor, newContent): { changeLines: number; diffCode: string } {
  try {
    const oldContent = fs.readFileSync(activeEditor.uri.fsPath, 'utf8');
    const diffCodes = calculateDiff(oldContent, newContent) ?? [];
    const diffCode = diffCodes
      //@ts-ignore
      .filter((item) => item.value.trim() !== '' && (item.removed || item.added))
      .map((item) => item.value)
      .join('');
    const changeLines = countCodeLinesInMarkdown(diffCode);
    return { changeLines, diffCode };
  } catch (error) {}
  return { changeLines: 0, diffCode: '' };
}

function getNextBeijingTime(hour, minute, second) {
  const now = new Date();
  const beijingTime = new Date(now.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
  const nextExecution = new Date(beijingTime);
  nextExecution.setHours(hour, minute, second, 0);

  if (nextExecution <= beijingTime) {
    nextExecution.setDate(nextExecution.getDate() + 1);
  }

  return nextExecution.getTime() - beijingTime.getTime();
}

//定时任务
function dailyTask() {
  Logger.log('Daily task executed at:', new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));

  activate();
}

const randomMinute = Math.floor(Math.random() * 40);
const timeUntilNextExecution = getNextBeijingTime(18, randomMinute, 0);

setTimeout(() => {
  dailyTask();
  setInterval(dailyTask, 24 * 60 * 60 * 1000);
}, timeUntilNextExecution);

function getWorkspaceFolder(): string | undefined {
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (workspaceFolders && workspaceFolders.length > 0) {
    return workspaceFolders[0].uri.fsPath;
  }
  return undefined;
}

//git命令执行方法
function executeGitCommand(workingDirectory: string, args: string[]): Promise<string> {
  return new Promise((resolve, reject) => {
    child_process.execFile('git', args, { cwd: workingDirectory }, (error, stdout, stderr) => {
      if (error) {
        reject(stderr);
      } else {
        resolve(stdout);
      }
    });
  });
}

//获取12小时内commit的id
async function getRecentCommitIds(): Promise<string[]> {
  const workspaceFolder = getWorkspaceFolder();
  if (!workspaceFolder) {
    vscode.window.showErrorMessage('No workspace folder found');
    return [];
  }

  try {
    const gitOutput = await executeGitCommand(workspaceFolder, ['log', '--since="12 hours ago"', '--pretty=format:%H']);
    const commitIds = gitOutput
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0 && line.match(/^[0-9a-f]{40}$/));

    return commitIds;
  } catch (error) {
    vscode.window.showErrorMessage(`Error executing Git command: ${error}`);
    return [];
  }
}

//根据commitID获得对应文件路径
async function getChangedFilesForCommit(commitId: string): Promise<string[]> {
  const workspaceFolder = getWorkspaceFolder();
  if (!workspaceFolder) {
    vscode.window.showErrorMessage('No workspace folder found');
    return [];
  }
  try {
    const gitOutput = await executeGitCommand(workspaceFolder, ['show', '--name-status', '--pretty=format:', commitId]);
    const lines = gitOutput
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    const filePaths: string[] = [];
    const processedFiles = new Set<string>();

    for (const line of lines) {
      const [status, ...filePathParts] = line.split(/\s+/);
      if (status === 'M' || status === 'A') {
        const filePath = path.join(workspaceFolder, filePathParts.join(' '));
        if (!processedFiles.has(filePath)) {
          processedFiles.add(filePath);
          filePaths.push(filePath);
        }
      }
    }
    return filePaths;
  } catch (error) {
    vscode.window.showErrorMessage(`Error executing Git command: ${error}`);
    return [];
  }
}

function determineFileLanguage(fileName: string): string {
  return path.extname(fileName).slice(1).toLowerCase() || 'unknown';
}

export async function activate() {
  //const changedFiles = await getRecentCommitsAndChangedFiles();

  const commitIds = await getRecentCommitIds();

  const hadCheckedCommitIds = await getCommitIdsFromFile();

  const needCheckIDList = commitIds.filter((commitId) => !hadCheckedCommitIds.includes(commitId));

  const changedFiles: string[] = [];

  for (const commitId of needCheckIDList) {
    const files = await getChangedFilesForCommit(commitId);
    changedFiles.push(...files);
  }

  if (changedFiles.length === 0) {
    Logger.log('Changed files: 12小时内无提交');
  } else {
    const isChecked =
      getVscodeConfig('JoyCode.config.codeReview.commit') || getVscodeConfig('JoyCode.config.shenYi.commit');
    if (isChecked || isInWhiteList) {
      vscode.commands.executeCommand('JoyCoderFE.sec.showSecIssues');
      const checkList: SecCommitCheckParams[] = [];
      for (const fileName in changedFiles) {
        const fileInfo = await getFileInfo(changedFiles[fileName]);
        checkList.push(...fileInfo);
      }
      updateSecIssuesPanelData(checkList);
    } else {
      Logger.log('Changed files: ');
    }
  }
}

async function getCommitIdsFromFile(): Promise<string[]> {
  try {
    const tempDir = os.tmpdir();
    const tempFileName = inspectionRecordFileName;
    const tempFilePath = path.join(tempDir, tempFileName);

    const data = fs.readFileSync(tempFilePath, 'utf8');
    const commits = JSON.parse(data);
    const commitIds = commits.map((commit: { commitId: string; timestamp: number }) => commit.commitId);
    return commitIds;
  } catch (error) {
    console.error(`Error reading or parsing file: ${error}`);
    return [];
  }
}

async function getFileInfo(
  filePath: string
): Promise<{ fileName: string; filePath: string; fileLanguage: string; code: string }[]> {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      vscode.window.showErrorMessage(`File not found: ${filePath}`);
      return [];
    }

    const fileName = path.basename(filePath);
    const fileLanguage = determineFileLanguage(fileName);
    const code = fs.readFileSync(filePath, 'utf8');

    return [{ fileName, filePath, fileLanguage, code }];
  } catch (error) {
    vscode.window.showErrorMessage(`Error reading file: ${error}`);
    return [];
  }
}
