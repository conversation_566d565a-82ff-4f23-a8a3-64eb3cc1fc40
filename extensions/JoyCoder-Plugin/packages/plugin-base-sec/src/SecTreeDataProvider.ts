import * as vscode from 'vscode';
const fs = require('fs');
const os = require('os');
const path = require('path');
import SecTreeNode from './SecTreeNode';
import { commitCodeReview, reviewCommitFix } from '@joycoder/plugin-base-code-review/src/utils/multiFileCodeReview';
import {
  ActionType,
  Logger,
  getAssetsFilePath,
  getJdhLoginInfo,
  getVscodeConfig,
  reportAction,
  getCurrentFileType,
} from '@joycoder/shared';
import { timestampToDateTime } from './utils';
import axios from 'axios';
import {
  SecAskResponse,
  SecAskResponseData,
  commitCheckRecord,
  historyTmpFileName,
  secBaseUrl,
  inspectionRecordFileName,
} from './index';
import { fetchSSE } from '@joycoder/plugin-base-ai/src/proxy/sse';

export interface SecCommitCheckParams {
  fileName: string;
  filePath: string;
  fileLanguage: string;
  code: string;
  fileType?: any; //CR Code
  fileSource?: string; //CR 来源
  diffContent?: any; //CR Code
  cwe?: string;
  id?: string;
  vulnerability?: string;
  comment?: string;
  vul_display?: string; // vulnerability的中文
  display?: string; // comment的中文
  start_line?: number;
  end_line?: number;
  checkIndex?: number;
  type?: string; // empty,checking,init
  crArray?: any; //CR list
  optimizedCode?: any; //CR Code
}

// 历史树节点
export interface HistoryResult {
  // commit增量提交时间
  timestamp: number;
  // 本次提交有问题文件list
  commitList: SecCommitCheckParams[];
  // 本次提交有问题文件修复建议list
  fullTextList: string[];
}

const batchSize = 5;
const expireTip = '(注：快速修复将在原文件发生改变后失效)';
const secCodeReg = /```(.*?)\s*((.|\s)*?)\s```/g;
let deepFixResult: SecAskResponseData[] = [];
const nth = 3;

// 实现一个TreeDataProvider，提供commit增量检测结果树或历史结果树数据
export default class SecTreeDataProvider implements vscode.TreeDataProvider<SecTreeNode> {
  private _onDidChangeTreeData: vscode.EventEmitter<SecTreeNode | undefined> = new vscode.EventEmitter<
    SecTreeNode | undefined
  >();
  readonly onDidChangeTreeData: vscode.Event<SecTreeNode | undefined> = this._onDidChangeTreeData.event;

  public commitList: SecCommitCheckParams[];
  public isHistory: boolean;
  public historyList: HistoryResult[];

  constructor(commitList: SecCommitCheckParams[], isHistory = false, historyList: HistoryResult[] = []) {
    this.commitList = commitList;
    this.isHistory = isHistory;
    this.historyList = historyList;
  }

  // issues树UI
  getTreeItem(element: SecTreeNode): vscode.TreeItem {
    let label = '';
    let description = '';
    switch (element.type) {
      case 'root':
        label = `神医安全助手-增量扫描漏洞列表${expireTip}`;
        break;
      case 'cr_root':
        label = `JoyCode-代码评审列表${expireTip}`;
        break;
      case 'historyRoot':
        label = `JoyCode-增量扫描历史结果${expireTip}`;
        break;
      case 'file':
        label = `${element.fileName}  `;
        description = `${element.filePath}`;
        break;
      case 'issue':
        label = ` ${element.cwe}: ${element.vulnerability}  `;
        description = `[行${element.start_line}, 行${element.end_line}]`;
        break;
      case 'init':
        label =
          '未进行工作区代码增量扫描' +
          (getVscodeConfig('JoyCode.config.shenYi.commit') || getVscodeConfig('JoyCode.config.codeReview.commit')
            ? ''
            : '，请先开启JoyCoder-神医安全增量扫描配置和代码评审配置');
        break;
      // case 'checking':
      //   label = `神医安全正在检查...`;
      //   break;
      case 'empty':
        label = `扫描代码无安全问题`;
        break;
      // 历史时间节点
      case 'historyTimestamp':
        label = `${element.fileName}`;
        break;
    }
    return {
      iconPath: element.cwe?.startsWith('CR-MULTI')
        ? this.getIconPath('code-review')
        : element.type == 'issue'
        ? this.getIconPath('warn')
        : element.type == 'historyTimestamp'
        ? this.getIconPath('folder')
        : '',
      label,
      description,
      collapsibleState:
        element.children && element.children.length > 0
          ? element.type == 'historyTimestamp'
            ? vscode.TreeItemCollapsibleState.Collapsed
            : vscode.TreeItemCollapsibleState.Expanded
          : vscode.TreeItemCollapsibleState.None,
      command:
        element.type === 'issue'
          ? {
              command: 'JoyCoderFE.sec.openCommitFileAndNavigate',
              title: '',
              arguments: [element],
            }
          : undefined,
    };
  }

  private getCustomTreeItem(type: string): SecTreeNode[] {
    const node = new SecTreeNode(
      type, // 状态
      undefined,
      '',
      ''
    );
    return [node];
  }

  // issues树数据
  getChildren(element?: SecTreeNode): Thenable<SecTreeNode[]> {
    if (element) {
      return Promise.resolve(element.children || []);
    } else {
      if (this.isHistory) {
        return Promise.resolve().then(() => {
          const result: SecTreeNode[] = [];
          this.historyList?.forEach((item) => {
            const tmp = new SecTreeNode(
              'historyTimestamp',
              undefined,
              timestampToDateTime(item.timestamp) + '',
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              undefined,
              item.commitList.map((item1, index1) => {
                return this.createTreeNode(
                  'file',
                  [this.createSecTreeNode(item1, item1, item?.fullTextList ? item?.fullTextList[index1] : undefined)],
                  item1
                );
              })
            );
            result.push(tmp);
          });
          return [this.createTreeNode('historyRoot', result)];
        });
      } else {
        return Promise.resolve(this.getSecCommitCheckData(this.commitList)).then((children) => {
          const type = this.commitList[0]?.type;
          if (type || (children && children[0]?.children?.length === 0)) {
            // 初始化init、正在检查checking、空empty
            return this.getCustomTreeItem(type || 'empty');
          }
          return children;
        });
      }
    }
  }

  // issues树数据更新
  refresh(): void {
    this._onDidChangeTreeData.fire(undefined);
  }
  private async processInBatches<T, R>(
    items: T[],
    batchSize: number,
    fn: (item: T, index: number) => Promise<R>
  ): Promise<R[]> {
    // 初始化一个与输入数组相同长度的结果数组，先填充undefined
    const result: R[] = new Array(items.length);

    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      // 使用Promise.all处理当前批次的所有项目
      const batchResults = await Promise.all(batch.map((item, batchIndex) => fn(item, i + batchIndex)));

      // 将当前批次的结果按照原始索引填充到结果数组中
      batchResults.forEach((batchResult, batchIndex) => {
        result[i + batchIndex] = batchResult;
      });
    }
    return result;
  }

  private async getSecCommitCheckData(params: SecCommitCheckParams[]): Promise<SecTreeNode[]> {
    // 初始化面板树不走安全检查
    const startTime = Date.now();
    if (params?.length == 1 && params[0]?.type == 'init') {
      return [this.createTreeNode('root', [])];
    } else {
      const resultArrCheck = await this.processInBatches(params, batchSize, async (item, index) => {
        // console.log('check---' + index + '---' + new Date());
        if (item['fileType']) {
          return secBatchCheck(item.code, item);
        }
        return secCommitCheck(item.code, item, index);
      });
      if (resultArrCheck) {
        const sec_result: SecTreeNode[] = [];
        const result: SecTreeNode[] = [];
        let resultArrFix: SecCommitCheckParams[] = [];
        resultArrCheck?.forEach((item, index) => {
          // 生成待修复数据
          if (item?.cwe) {
            const tmp = params[index];
            resultArrFix.push({
              fileName: tmp.fileName,
              filePath: tmp.filePath,
              fileLanguage: tmp.fileLanguage,
              code: tmp.code,
              id: item.id,
              cwe: item.cwe,
              start_line: item.start_line,
              end_line: item.end_line,
              vulnerability: item.vulnerability,
              comment: item.comment,
              vul_display: item.vul_display,
              display: item.display,
              checkIndex: index,
              crArray: item.crArray || [],
              optimizedCode: item.optimizedCode ?? '',
            });
          }
        });
        resetSecDeepFix();
        if (resultArrFix.length > 0 && resultArrFix.some((element) => element?.cwe?.startsWith('CWE-'))) {
          reportAction({
            actionCate: 'sec',
            actionType: ActionType.commitFind,
            extendMsg: {
              vulnId: 0,
            },
          });
        }
        let resultArr = await this.processInBatches(resultArrFix, batchSize, async (item, index) => {
          // console.log('fix----' + index + '---' + new Date());
          if (item?.cwe && item.cwe.startsWith('CR-MULTI')) {
            // 代码评审数据获取
            return reviewCommitFix(item, index, deepFixResult);
          }
          return secCommitFix(item, index);
        });
        // 处理深度修复结果超时或fullText不完整
        resultArrFix = resultArrFix.filter((_item: SecCommitCheckParams, index) => {
          const reg = secCodeReg;
          if (resultArr) {
            const codeArr = resultArr[index].fullText?.match(reg);
            // 神医安全检查返回第一段代码为替换代码
            return codeArr && codeArr[0];
          }
        });
        resultArr = resultArr?.filter((item) => {
          const reg = secCodeReg;
          const codeArr = item.fullText?.match(reg);
          // 神医安全检查返回第一段代码为替换代码
          return codeArr && codeArr[0];
        });
        resultArrFix?.forEach((item, index) => {
          // 生成树形结构数据
          if (item.checkIndex === 0 || item.checkIndex) {
            const tmp = params[item.checkIndex];
            if (!item.cwe?.startsWith('CR-MULTI')) {
              const sec_tmp1 = this.createTreeNode(
                'file',
                [this.createSecTreeNode(tmp, item, resultArr ? resultArr[index].fullText : undefined)],
                tmp
              );
              sec_result.push(sec_tmp1);
            }
            const crList = (item?.crArray || []).filter((crItem) => !!crItem.vulnerability);
            let crChildren: SecTreeNode[] = [];
            if (crList.length > 0) {
              crChildren = crList.map((child) => {
                return this.createSecTreeNode(tmp, child);
              });
            } else if (item.cwe?.startsWith('CR-MULTI')) {
              crChildren = [this.createSecTreeNode(tmp, item)];
            }
            const tmp1 = this.createTreeNode('file', [...crChildren], tmp);
            crChildren.length && result.push(tmp1);
          }
        });
        const crFileList = [...sec_result, ...result];
        if (crFileList.length > 0) {
          let crResultSize = sec_result.length ?? 0;
          if (result.length) {
            result.forEach((tree: SecTreeNode) => {
              crResultSize += tree.children?.length ?? 0;
            });
          }
          Logger.showInformationMessage(`代码审核: 检测发现${crResultSize}处问题代码，点击查看详细内容`, '查看').then(
            (result: string) => {
              if (result === '查看') {
                vscode.commands.executeCommand('JoyCoderFE.sec.showSecIssues');
              }
            }
          );
          // 存三天内的commit增量检查结果
          storeAndCleanupFixResults(resultArrFix, resultArr ? resultArr?.map((item) => item?.fullText) : []);
          try {
            // 上报所有补全文件日志
            crFileList.forEach((file) => {
              const fileInfo = this.commitList.find(
                (commitFile) => commitFile.fileName == file.fileName && commitFile.filePath == file.filePath
              );
              const resultFix = resultArrFix.find(
                (resFile) => resFile.fileName == file.fileName && resFile.filePath == file.filePath
              );
              reportAction({
                actionCate: 'ai',
                actionType: fileInfo?.fileSource as ActionType,
                conversationId: resultFix?.id,
                result: resultFix?.optimizedCode,
                consumeTime: Date.now() - startTime,
                extendMsg: {
                  // 代码评审Diff（本次新增字段）
                  diffContent: fileInfo?.diffContent,
                  // 代码评审 源文件（本次新增字段）
                  originContent: fileInfo?.code,
                },
              });
            });
          } catch (error) {}
        }
        // console.log('--------end: ' + new Date());
        // return result;
        if (result.length > 0) {
          const sec_results: SecTreeNode[] = [];
          if (sec_result.length) {
            sec_results.push(this.createTreeNode('root', sec_result));
          }
          return [...sec_results, this.createTreeNode('cr_root', result)];
        }
        return [this.createTreeNode('root', sec_result)];
      } else {
        return [];
      }
    }
  }
  private createTreeNode(type: string, sec_result: SecTreeNode[], tmp?: SecCommitCheckParams): SecTreeNode {
    return new SecTreeNode(
      type,
      undefined,
      tmp?.fileName,
      tmp?.filePath,
      tmp?.id,
      tmp?.fileSource,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      sec_result
    );
  }
  private createSecTreeNode(tmp: SecCommitCheckParams, item: SecCommitCheckParams, fullText?: string): SecTreeNode {
    const isCr = item.cwe?.startsWith('CR-MULTI');
    return new SecTreeNode(
      'issue',
      undefined,
      tmp.fileName,
      tmp.filePath,
      item?.id,
      item?.fileSource,
      tmp.fileLanguage,
      tmp.code,
      undefined,
      item.cwe,
      isCr ? item.vulnerability : item.vul_display || item.vulnerability,
      isCr ? item.comment : item.display || item.comment,
      item.start_line,
      item.end_line,
      fullText ?? item?.optimizedCode,
      undefined
    );
  }
  private getIconPath(name: string) {
    return {
      light: vscode.Uri.file(getAssetsFilePath('sec/' + name + '.svg').fsPath), // 亮色主题下的图标路径
      dark: vscode.Uri.file(getAssetsFilePath('sec/' + name + '.svg').fsPath), // 暗色主题下的图标路径
    };
  }
}

// commit增量问题检查接口调用
async function secCommitCheck(param: string, node?: SecCommitCheckParams, index?: number) {
  try {
    const isChecked =
      getVscodeConfig('JoyCode.config.codeReview.commit') && getVscodeConfig('JoyCode.config.shenYi.commit');
    const fileNumber = getVscodeConfig('JoyCode.config.codeReview.fileNumber');
    const erpSuffix = (getJdhLoginInfo()?.erp || '') + '_v';
    const deepVulnUrl = `${secBaseUrl}/find_vuln_deep`;

    const performDeepVulnCheck = async () => {
      const params = {
        code: '```' + getCurrentFileType() + '\n' + param + '\n```',
        erp: erpSuffix,
      };
      const response = await axios.post(deepVulnUrl, params);
      if (response?.data?.cwe) {
        reportAction({
          actionCate: 'sec',
          actionType: ActionType.commitFindOne,
          extendMsg: {
            vulnId: response?.data?.id,
          },
        });
      }
      return response;
    };
    const performCodeReview = () => commitCodeReview({ ...node, lang: node?.fileLanguage, code: param });

    if (isChecked && fileNumber > (index ?? 0)) {
      const [res, crData] = await Promise.all([performDeepVulnCheck(), performCodeReview()]);
      return processResults(res?.data, crData);
    } else if (getVscodeConfig('JoyCode.config.shenYi.commit')) {
      const res = await performDeepVulnCheck();
      return res.data;
    } else if (getVscodeConfig('JoyCode.config.codeReview.commit') && fileNumber > (index ?? 0)) {
      const crData = await performCodeReview();
      return processCodeReviewData(crData);
    }
  } catch (error) {
    console.warn('神医安全助手-find_vuln_deep-增量检查接口-error：' + error?.message);
  }
}
/**
 * 批量代码审核-用户文件区域右键主动触发
 * @param {string} param - 代码参数
 * @param {SecCommitCheckParams} [node] - 安全提交检查参数
 * @param {number} [index] - 索引
 * @returns {Promise<any>} - 处理结果
 * @throws {Error} - 捕获并警告错误信息
 */
async function secBatchCheck(param: string, node?: SecCommitCheckParams) {
  try {
    const erpSuffix = (getJdhLoginInfo()?.erp || '') + '_v';
    const deepVulnUrl = `${secBaseUrl}/find_vuln_deep`;

    const performDeepVulnCheck = async () => {
      const params = {
        code: '```' + getCurrentFileType() + '\n' + param + '\n```',
        erp: erpSuffix,
      };
      const response = await axios.post(deepVulnUrl, params);
      if (response?.data?.cwe) {
        reportAction({
          actionCate: 'sec',
          actionType: ActionType.commitFindOne,
          extendMsg: {
            vulnId: response?.data?.id,
          },
        });
      }
      return response;
    };
    const performCodeReview = () => commitCodeReview({ ...node, lang: node?.fileLanguage, code: param });
    const [res, crData] = await Promise.all([performDeepVulnCheck(), performCodeReview()]);
    return processResults(res?.data, crData);
  } catch (error) {
    console.warn('神医安全助手-find_vuln_deep-增量检查接口-error：' + error?.message);
  }
}

function processResults(resData: any, crData: any) {
  if (crData) {
    if (resData?.cwe) {
      return { ...resData, crArray: crData?.crArray || [] };
    } else if (crData?.crArray?.[0]) {
      return {
        ...crData.crArray[0],
        crArray: crData.crArray || [],
      };
    }
  }
  return resData;
}

function processCodeReviewData(crData: any) {
  return {
    ...crData?.crArray?.[0],
    crArray: crData?.crArray || [],
  };
}

// commit增量问题修复接口调用，默认超时时间200s
function secCommitFix(param: SecCommitCheckParams, index: number, timeout = 200000) {
  const body = {
    code: '```' + param?.fileLanguage + '\n' + param?.code + '\n```',
    vulnerability: param?.vulnerability,
    cwe: param?.cwe,
    comment: param?.comment,
    erp: (getJdhLoginInfo()?.erp || '') + '_v',
  };
  return new Promise<SecAskResponseData>((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`sec fix_vuln_deep timed out`));
    }, timeout);

    fetchSSE(`${secBaseUrl}/fix_vuln_deep`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      onMessage: async (res: string) => {
        try {
          // console.warn('神医安全助手-fix_vuln_deep-增量安全修复接口返回内容：' + res);
          // 返回数据结束
          if (res == '[DONE]') {
            clearTimeout(timeoutId); // 清除超时定时器
            deepFixResult[index].text = deepFixResult[index].text.trim();
            deepFixResult[index].fullText = deepFixResult[index].fullText.trim();
            console.warn(
              '\n神医安全助手-fix_vuln_deep-增量安全修复接口返回完整内容：' + new Date() + '\n',
              deepFixResult[index].fullText
            );
            return resolve(deepFixResult[index]);
          }
          const response: SecAskResponse = JSON.parse(res);
          const text = response?.answer || '';
          // 返回数据中
          if (text) {
            if (!deepFixResult[index]) {
              deepFixResult[index] = {
                text: '',
                fullText: '',
              };
            }
            deepFixResult[index].text = text;
            deepFixResult[index].fullText = deepFixResult[index].fullText + text;
          }
        } catch (err) {
          clearTimeout(timeoutId); // 清除超时定时器
          deepFixResult[index].fullText = '';
          console.warn('神医安全助手-fix_vuln_deep-增量安全修复-error：' + err);
          return reject(err);
        }
      },
    }).catch((error: Error) => {
      console.warn('神医安全助手-fix_vuln_deep-增量安全修复-error：' + error?.message);
      // return reject(error);
    });
  });
}

export function resetSecDeepFix() {
  deepFixResult = [];
}
// 神医安全增量检查历史记录
// 获取n天前零时的时间戳
function getNthDaysAgoTimestamp(nth: number) {
  const now = new Date();
  now.setDate(now.getDate() - nth); // 设置为三天前的日期
  now.setHours(0, 0, 0, 0); // 设置时间为零时
  return now.getTime();
}

// 存储修复结果到一个文件，并清理超过nth天的结果
function storeAndCleanupFixResults(resultArrFix: SecCommitCheckParams[], fullTextList: string[]) {
  const tempDir = os.tmpdir();
  const tempFileName = historyTmpFileName;
  const tempFilePath = path.join(tempDir, tempFileName);
  const threeDaysAgoTimestamp = getNthDaysAgoTimestamp(nth);
  let fixResults: HistoryResult[] = [];

  // 尝试读取现有的修复结果文件
  if (fs.existsSync(tempFilePath)) {
    try {
      fixResults = JSON.parse(fs.readFileSync(tempFilePath));
      // 清理超过nth天的结果
      fixResults = fixResults.filter(
        (result: HistoryResult) => result.timestamp && result.timestamp >= threeDaysAgoTimestamp
      );
    } catch (err) {
      console.error('Failed to read or parse existing sec fix results:', err);
      // 如果文件损坏，重新开始一个新的数组
      fixResults = [];
    }
  }

  // 添加新的修复结果
  const timestamp = Date.now();
  fixResults.unshift({
    timestamp: timestamp,
    commitList: resultArrFix,
    fullTextList,
  });

  // 写入更新后的修复结果到文件
  fs.writeFileSync(tempFilePath, JSON.stringify(fixResults));
}

//检测信息状态
export function saveCheckRecord(resultArrFix: commitCheckRecord) {
  const tempDir = os.tmpdir();
  const tempFileName = inspectionRecordFileName;
  const tempFilePath = path.join(tempDir, tempFileName);
  const threeDaysAgoTimestamp = getNthDaysAgoTimestamp(nth);
  let checkResults: commitCheckRecord[] = [];

  // 尝试读取现有的修复结果文件
  if (fs.existsSync(tempFilePath)) {
    try {
      checkResults = JSON.parse(fs.readFileSync(tempFilePath, 'utf-8'));
      // 清理超过 nth 天的结果
      checkResults = checkResults.filter(
        (result: commitCheckRecord) => result.timestamp && result.timestamp >= threeDaysAgoTimestamp
      );
    } catch (err) {
      console.error('Failed to read or parse existing sec fix results:', err);
      // 如果文件损坏，重新开始一个新的数组
      checkResults = [];
    }
  }

  // 添加新的修复结果
  checkResults.unshift(resultArrFix);

  // 写入更新后的修复结果到文件
  fs.writeFileSync(tempFilePath, JSON.stringify(checkResults));
}
