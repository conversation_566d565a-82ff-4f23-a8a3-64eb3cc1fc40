// commit增量检测结果树形节点
export default class SecTreeNode {
  public type: string;
  public fileIconPath?: string;
  public fileName?: string;
  public filePath?: string;
  public id?: string;
  public fileSource?: string;
  public fileLanguage?: string;
  public code?: string;
  public errIconPath?: string;
  public cwe?: string;
  public vulnerability?: string;
  public comment?: string;
  public start_line?: number;
  public end_line?: number;
  public fixResult?: string;
  public children?: SecTreeNode[];

  constructor(
    type: string,
    fileIconPath?: string,
    fileName?: string,
    filePath?: string,
    id?: string,
    fileSource?: string,
    fileLanguage?: string,
    code?: string,
    errIconPath?: string,
    cwe?: string,
    vulnerability?: string,
    comment?: string,
    start_line?: number,
    end_line?: number,
    fixResult?: string,
    children?: SecTreeNode[]
  ) {
    this.type = type;
    this.fileIconPath = fileIconPath;
    this.fileName = fileName;
    this.filePath = filePath;
    this.id = id;
    this.fileSource = fileSource;
    this.fileLanguage = fileLanguage;
    this.code = code;
    this.errIconPath = errIconPath;
    this.cwe = cwe;
    this.vulnerability = vulnerability;
    this.comment = comment;
    this.start_line = start_line;
    this.end_line = end_line;
    this.fixResult = fixResult;
    this.children = children;
  }
}
