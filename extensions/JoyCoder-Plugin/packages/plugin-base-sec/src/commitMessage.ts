import { Logger } from '@joycoder/shared';

export class CommitMessage {
  commitMessage: string;
  constructor(repo) {
    // 监听提交消息输入框的变化
    try {
      repo.inputBox._inputBox.onDidChange((message) => {
        const commitMessage = message.trim();
        commitMessage && this.setCommitMessage(commitMessage);
      });
    } catch (error) {
      Logger.error('%c [ error ]-1029', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
  setCommitMessage(commitMessage: string) {
    this.commitMessage = commitMessage;
  }
  getCommitMessage() {
    return this.commitMessage;
  }
}
