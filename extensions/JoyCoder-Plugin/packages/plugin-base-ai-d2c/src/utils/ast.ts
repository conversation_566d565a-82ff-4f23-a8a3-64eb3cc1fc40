import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import generator from '@babel/generator';
import {
  ImportDeclaration,
  JSXAttribute,
  identifier,
  importDeclaration,
  importDefaultSpecifier,
  isImportDeclaration,
  jsxAttribute,
  jsxElement,
  jsxIdentifier,
  jsxOpeningElement,
  stringLiteral,
} from '@babel/types';
import { findIndex, findLastIndex } from 'lodash-es';

const generateCode = (astNode) => generator(astNode).code;
const parseCode = (code, { language = '' } = {}) =>
  parse(code, {
    sourceType: 'module',
    plugins: language === 'typescript' || language === 'typescriptreact' ? ['typescript', 'jsx'] : ['jsx'],
  });

const jsxElementIdPrefix = 'r-';
export function addJSXElementId(code) {
  const ast = parseCode(code);

  let i = 0;
  traverse(ast, {
    JSXElement(path) {
      const openingElement = path.node.openingElement;
      // 判断id是否存在
      const idAttr = openingElement.attributes.find((attr: JSXAttribute) => {
        const name: any = attr.name || {};
        return name.name === 'id';
      });
      if (idAttr) {
        return;
      }
      const id = jsxAttribute(jsxIdentifier('id'), stringLiteral(jsxElementIdPrefix + i++));
      openingElement.attributes.push(id);
    },
  });

  const output = generateCode(ast);

  return output;
}

export function removeJSXElementId(code) {
  code = code.replace(/ id="r-\d+"/g, '');
  return code;
}

interface CompDesc {
  id: string;
  name: string;
}

export function componentByComps(code, comps: CompDesc[]) {
  const ast = parseCode(code);
  const programBody = ast.program.body;
  const originImports = programBody.filter((node) => isImportDeclaration(node));
  const generateImportsCode = (imports) => imports.map(generateCode).join('\n');
  const originImportsCode = generateImportsCode(originImports);
  const astList = splitASTByComps(ast, comps) as any[];

  const codes = astList.map((item) => {
    if (item.isProgram) {
      // 插入组件声明
      const body = item.astNode.program.body;
      const lastImportIndex = findLastIndex(body, (node: object) => isImportDeclaration(node)) + 1;
      item.astNode.program.body = body
        .slice(0, lastImportIndex)
        .concat(item.importNodes)
        .concat(body.slice(lastImportIndex));
      return { name: 'Index', code: generateCode(item.astNode) };
    } else {
      const componentCode = `
${originImportsCode}
${generateImportsCode(item.importNodes)}
function ${item.name}() {
  return (
    ${generateCode(item.astNode)}
  );
}
export default ${item.name};`;
      return { name: item.name, code: componentCode };
    }
  });

  // console.log(codes);
  return codes;
}

function splitASTByComps(astNode, comps, astList: any[] = []) {
  const isProgram = !!astNode.program;
  const newImportNodes: ImportDeclaration[] = [];

  traverse(astNode, {
    noScope: true,
    JSXElement(path) {
      const openingElement = path.node.openingElement;
      const idAttr = openingElement.attributes.find((attr: JSXAttribute) => {
        const name: any = attr.name || {};
        return name.name === 'id';
      }) as any;
      if (idAttr) {
        const id = idAttr.value.value;
        const compIndex = findIndex(comps, (comp: CompDesc) => comp.id === id);
        const comp = comps[compIndex];
        if (comp) {
          // 删除已经匹配的组件
          comps.splice(compIndex, 1);

          const componentName = comp.name;
          const astListItem = { name: componentName, astNode: path.node, importNodes: [] as ImportDeclaration[] };
          if (comps.length) {
            const res = splitASTByComps(astListItem.astNode, comps, astList) as { importNodes: ImportDeclaration[] };
            astListItem.importNodes = res.importNodes;
          }
          // 通过 path.node 创建组件
          astList.push(astListItem);
          // 用组件替换原来的jsxElement
          const newNode = jsxElement(jsxOpeningElement(jsxIdentifier(componentName), [], true), null, []);
          path.replaceWith(newNode);
          // 增加组件声明
          newImportNodes.push(
            importDeclaration([importDefaultSpecifier(identifier(componentName))], stringLiteral(`./${componentName}`))
          );
        }
      }
    },
  });

  if (isProgram) {
    astList.unshift({ isProgram, astNode, importNodes: newImportNodes });
    return astList;
  } else {
    return { importNodes: newImportNodes };
  }
}

// export function execDemo() {
//   const demoCode = `
//   import React from 'react';

//   function App() {
//     return (
//       <div>
//         <header id="123">
//           <h1>Welcome to my App</h1>
//         </header>
//         <main>
//           <p>This is a paragraph in the main section.</p>
//         </main>
//         <footer>
//           <p>This is the footer.</p>
//           <div id="456">
//             <div>hahha</div>
//             <div id="789">sssssss</div>
//           </div>
//         </footer>
//       </div>
//     );
//   }

//   export default App;
//   `;

//   const outputCode = addJSXElementId(demoCode);
//   console.log(outputCode);

//   const comps = [
//     {
//       id: '123',
//       name: 'Header',
//     },
//     {
//       id: 'r-2',
//       name: 'Main',
//     },
//     {
//       id: 'r-4',
//       name: 'Footer',
//     },
//     {
//       id: '456',
//       name: 'Footer456',
//     },
//     {
//       id: '789',
//       name: 'Footer789',
//     },
//   ];
//   componentByComps(outputCode, comps);
// }

// execDemo();
