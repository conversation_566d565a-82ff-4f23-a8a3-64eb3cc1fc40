import * as fs from 'fs';
import * as path from 'path';

const logsDirectory = 'logs';
const logsExtension = '.txt';
let logsFilename = '';

function newLog(): void {
  const date = new Date();
  logsFilename = `${date.toLocaleDateString()}_${date.toLocaleTimeString()}`.replace(/\/|:/g, '-');
}

/**
 * 返回日志文件路径
 * @returns {string}
 */
function logPath(): string {
  if (!logsFilename) {
    newLog();
  }

  const logsDir = path.join(__dirname, '..', logsDirectory);
  const fileName = `${logsFilename}${logsExtension}`;
  return path.join(logsDir, fileName);
}

/**
 * 将日志保存到日志文件夹
 * @param {string} text
 */
export async function saveLog(text: string): Promise<void> {
  const isDebugger = process.env.NODE_ENV == 'development';
  if (!isDebugger) {
    return;
  }
  const filePath = logPath();
  const dirName = path.dirname(filePath);
  const append = () => fs.appendFileSync(filePath, `${text} \n\n*******\n\n`);
  try {
    await fs.promises.access(dirName, fs.constants.F_OK);
    append();
  } catch (error) {
    await fs.promises.mkdir(dirName, { recursive: true });
    append();
  }
}
