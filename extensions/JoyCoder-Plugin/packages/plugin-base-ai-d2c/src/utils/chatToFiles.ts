import { IFile } from '../interface';

export function parseChat(chat) {
  const regex = /(\S+)\n\s*```[^\n]*\n(.+?)```/gs;
  const matches = chat.matchAll(regex);

  const files: IFile[] = [];
  for (const match of matches) {
    let fileName = match[1];

    fileName = fileName.replace(/[<>"|?*]/g, '');
    fileName = fileName.replace(/^\[(.*)\]$/, '$1');
    fileName = fileName.replace(/^`(.*)`$/, '$1');
    fileName = fileName.replace(/\]$/, '');

    const code = match[2];

    const isValidFile = /\.[a-zA-Z0-9]+$/.test(fileName);
    // 过滤掉没有文件后缀的文件
    if (isValidFile) {
      files.push({ fileName, code });
    } else {
      // console.log('文件名不合法', fileName)
    }
  }

  return files;
}
