import { ChatCompletionRequestMessageFunctionCall, ChatCompletionResponseMessage } from 'openai';

import { BaseFunction } from '../functions/base';
// import { get_encoding } from '@dqbd/tiktoken';
import { saveLog } from '../utils/logs';
import { BaseAiLLM } from './baseAiLLM';
import { IBaseLLM } from './baseLLM';
import { ChatGPTAskResponseData } from '@joycoder/plugin-base-ai/src/proxy';

export type Model = {
  id: string;
  maxTokens: number;
};
export type Config = {
  openAIKey: string;
  openAIModel: Model;
  basePath?: string;
};

export class Ai {
  private llm: IBaseLLM;
  private messages: ChatCompletionResponseMessage[] = [];
  private functions: BaseFunction[] = [];

  constructor(systemMessage: string) {
    this.llm = new BaseAiLLM();
    this.messages.push({ role: 'system', content: systemMessage });
  }

  addFunction(func: BaseFunction) {
    this.functions.push(func);
  }

  getFunction(name: string): BaseFunction {
    const func = this.functions.find((f) => f.definition.name === name);
    if (!func) {
      throw new Error(`Function ${name} not found`);
    }
    return func;
  }

  executeFunction(functionCall: ChatCompletionRequestMessageFunctionCall) {
    if (!functionCall.name) {
      throw new Error('Function name not provided');
    }

    const func = this.getFunction(functionCall.name);
    return func.execute(functionCall);
  }

  private calculateTokenLength(message: ChatCompletionResponseMessage) {
    // const encoding = get_encoding('cl100k_base');
    // let tokenLength = 0;
    // if (message.content) {
    //   tokenLength += encoding.encode(message.content).length;
    // }
    // if (message.function_call) {
    //   tokenLength += encoding.encode(JSON.stringify(message.function_call)).length;
    // }
    // return tokenLength;
  }

  /**
   * 该函数确保所有消息的总令牌长度不超过OpenAI设置的最大限制。
   * 如果总令牌长度超过限制，它将删除第二旧的消息，直到总长度在限制内。
   * 最旧的消息始终被保留，因为它是系统消息。
   */
  private capMessageList() {
    // let totalTokenLength = this.messages.reduce((acc, m) => {
    //   return acc + this.calculateTokenLength(m);
    // }, 0);
    // // Remove
    // while (totalTokenLength > this.config.openAIModel.maxTokens) {
    //   totalTokenLength -= this.calculateTokenLength(this.messages[1]);
    //   this.messages.splice(1, 1);
    // }
  }

  async userRequest(message: string): Promise<ChatGPTAskResponseData> {
    // 增加用户消息记录
    this.messages.push({ role: 'user', content: message });

    // 确保消息的令牌长度小于最大长度
    this.capMessageList();

    saveLog('[input]:' + JSON.stringify(this.messages, null, 2));

    const result = await this.llm.request({
      // model: 'GPT-3.5-TURBO-16K',
      model: 'GPT-4-1106-Preview',
      messages: this.messages,
      // functions: this.functions.map((f) => f.definition),
      temperature: 0,
    });

    saveLog('[output]:' + JSON.stringify(result, null, 2));

    const fullText = result.fullText;
    // const choice = result.data.choices[0] || {};
    if (fullText) {
      // 增加返回ai消息记录
      this.messages.push({ role: 'assistant', content: message });

      // // 处理函数调用
      // const functionCall = choice.message?.function_call;
      // const funcName = functionCall?.name;
      // if (funcName) {
      //   const commandOutput = await this.executeFunction(functionCall);
      //   if ((commandOutput as unknown) !== false) {
      //     this.userRequest(commandOutput || `Function ${funcName} executed successfully`);
      //   }
      // }
    }

    return result;
  }
}
