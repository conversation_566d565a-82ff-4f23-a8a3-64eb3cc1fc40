import { ChatGPTAskResponseData, Scene, ChatProxyService } from '@joycoder/plugin-base-ai/src/proxy/index';
import { IBaseLLM, IBaseLLMRequestOpt } from './baseLLM';

export class BaseAiLLM implements IBaseLLM {
  constructor() {}

  async request(opt: IBaseLLMRequestOpt): Promise<ChatGPTAskResponseData> {
    const abortController = new AbortController();
    const extraContent = (messages) => messages.map((item) => item.content).join('\n') || '';
    const systemMessage = extraContent(opt.messages.filter((item) => item.role === 'system'));
    const messages = opt.messages.filter((item) => item.role !== 'system');
    console.log('[D2C] BaseAiLLM request start');

    const chatCompletion = await new ChatProxyService().invoke(
      {
        systemMessage,
        model: opt.model, // 触发 maxTokens 匹配
        temperature: opt.temperature as number,
        message: extraContent(messages),
        scene: Scene.Cursor,
        respMaxTokens: 4096,
        maxTotalTokens: 10000,
      },
      {
        abortController,
        onProgress: function (response: ChatGPTAskResponseData): void {
          process.stdout.write(`.. \r`);
        },
      }
    );
    return chatCompletion;
  }
}
