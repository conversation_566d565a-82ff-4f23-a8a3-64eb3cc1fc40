import { Configuration, OpenAIApi } from 'openai';
import { IBaseLLM, IBaseLLMRequestOpt } from './baseLLM';

export class OpenAiLLM implements IBaseLLM {
  private llm: OpenAIApi;
  constructor() {
    // Set up OpenAI configuration
    const openAiConfiguration = new Configuration({
      apiKey: 'sk-xxxxx',
      basePath: 'https://gofrontend.cn/v0',
    });

    // Initialize OpenAI client
    this.llm = new OpenAIApi(openAiConfiguration);
  }

  async request(opt: IBaseLLMRequestOpt) {
    const chatCompletion = await this.llm.createChatCompletion(opt);
    return chatCompletion as any;
  }
}
