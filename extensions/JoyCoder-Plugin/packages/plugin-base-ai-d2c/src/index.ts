import * as vscode from 'vscode';
// import D2C from './agents/d2c';
import { addRouterIn } from './bridge';

const demoCodeShort = `import React from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Image } from '@tarojs/components'
import './Mod.scss'
function Mod() {
  return (
    <View className='mod'>
      <View className='cnt_col'>
        <View className='cnt_col1'>
          <View className='cnt_col2'>
            <View className='wrapper'>
              <View className='color_view'></View>
            </View>
            <View className='cnt_row1'>
              <Image
                className='icon2'
                src='//img20.360buyimg.com/img/jfs/t1/99124/35/40466/580/64ad1859F5e6cd042/99b4cc659b7c36bc.png'
              ></Image>
              <Image
                className='icon3'
                src='//img10.360buyimg.com/img/jfs/t1/117859/37/40992/335/64ad1859Faf37a584/05080dda8cda6f92.png'
              ></Image>
            </View>
            <View className='split_ver'></View>
          </View>
          <Text className='txt_common'>京东开店助你成为大学生创业明星</Text>
        </View>
      </View>
      <View className='wrapper10'>
        <View className='wrapper11'>
          <View className='cnt_row14'>
            <Text className='txt31'>立即</Text>
            <Text className='number3'>0</Text>
            <Text className='txt31'>元开店</Text>
          </View>
        </View>
      </View>
    </View>
  )
}
export default Mod`;
const demoCode = `import React from 'react'
import Taro from '@tarojs/taro'
import { View, Image, Text } from '@tarojs/components'
import './Mod.scss'
function Mod() {
  return (
    <View className='mod'>
      <View className='cnt_col'>
        <View className='cnt_row'>
          <Image
            className='icon'
            src='//img20.360buyimg.com/img/jfs/t1/220792/14/33595/499/64c6531eFe7adbd86/305f7a77258eecf5.png'
          ></Image>
          <Text className='line1 txt'>开店流程指导</Text>
          <Image
            className='icon1'
            src='//img14.360buyimg.com/img/jfs/t1/215921/13/34982/491/64c6531fF98389b5d/4b972b20a64dc99f.png'
          ></Image>
          <Text className='line1 instruction'>保存指引图片</Text>
        </View>
        <View className='cnt_row1'>
          <View className='wrapper'>
            <View className='cnt_col1'>
              <View className='color_view'></View>
              <View className='color_view1'></View>
              <View className='color_view2'></View>
            </View>
          </View>
          <View className='cnt_col2'>
            <Text className='line1 txt1'>开店申请</Text>
            <Text className='line1 txt2'>
              进入主题选择页，选择“个人”类型，点击我要开店
            </Text>
          </View>
        </View>
        <View className='cnt_row2'>
          <View className='wrapper1'>
            <View className='wrapper2'>
              <View className='icon_wrap'>
                <Image
                  className='icon2'
                  src='//img10.360buyimg.com/img/jfs/t1/160735/32/37835/375/64ad1856F94d57932/7f250e1c78844f68.png'
                ></Image>
              </View>
            </View>
          </View>
          <View className='cnt_col3'>
            <Text className='line1 txt1'>实名认证</Text>
            <Text className='line1 txt2'>人脸识别及身份验证证</Text>
          </View>
        </View>
        <View className='cnt_row2'>
          <View className='wrapper1'>
            <View className='icon_wrap1'>
              <Image
                className='icon3'
                src='//img11.360buyimg.com/img/jfs/t1/149787/2/36955/640/64ad1857F0f34a9ef/be81850a4b61e6d8.png'
              ></Image>
            </View>
          </View>
          <View className='cnt_col4'>
            <Text className='line1 txt1'>下载京麦并完成资质认证</Text>
            <Text className='line1 txt2'>
              进入应用市场，下载京麦APP并注册，进行资质认证
            </Text>
          </View>
        </View>
        <View className='cnt_row2'>
          <View className='icon_wrap2'>
            <Image
              className='icon4'
              src='//img20.360buyimg.com/img/jfs/t1/126421/10/36564/1092/64ad1857F0890aa14/44db4bd1baa6376b.png'
            ></Image>
          </View>
          <View className='cnt_col5'>
            <Text className='line1 txt1'>店铺启动</Text>
            <Text className='line1 txt2'>
              资质审核通过后，绑定结算账户，店铺启动
            </Text>
          </View>
        </View>
      </View>
      <View className='wrapper3'>
        <View className='wrapper4'>
          <View className='view'>
            <Text className="txt31">立即</Text>
            <Text className="number3">0</Text>
            <Text className="txt31">元开店</Text>
          </View>
        </View>
      </View>
    </View>
  )
}
export default Mod
`;
const demoCodeLong = `import React from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image } from "@tarojs/components";
import "./Mod.scss";
function Mod() {
  return (
    <View className="mod">
      <View className="cnt_col">
        <View className="cnt_col1">
          <View className="cnt_row_common">
            <Text className="tit_common">9:41</Text>
            <Image
              className="icon"
              src="//img30.360buyimg.com/img/jfs/t1/176870/23/34928/435/64ad185aF8950e166/e14d5e92a3429ccd.png"
            ></Image>
            <Image
              className="icon1"
              src="//img10.360buyimg.com/img/jfs/t1/223681/35/25766/518/64ad185aFfc1846ad/6b9723a8760e5c47.png"
            ></Image>
            <Image
              className="img"
              src="//img12.360buyimg.com/img/jfs/t1/142540/33/35509/775/64ad1859Fd03a3e5a/dc05b8720960e994.png"
            ></Image>
          </View>
          <View className="cnt_col2">
            <View className="wrapper">
              <View className="color_view"></View>
            </View>
            <View className="cnt_row1">
              <Image
                className="icon2"
                src="//img20.360buyimg.com/img/jfs/t1/99124/35/40466/580/64ad1859F5e6cd042/99b4cc659b7c36bc.png"
              ></Image>
              <Image
                className="icon3"
                src="//img10.360buyimg.com/img/jfs/t1/117859/37/40992/335/64ad1859Faf37a584/05080dda8cda6f92.png"
              ></Image>
            </View>
            <View className="split_ver"></View>
          </View>
          <Text className="txt_common">京东开店助你成为大学生创业明星</Text>
        </View>
        <View className="cnt_row2">
          <View className="bg_view"></View>
          <View className="cnt_row3">
            <View className="bg_view1"></View>
            <View className="cnt_col3">
              <View className="cnt_row4">
                <Image
                  className="main_img"
                  src="//img30.360buyimg.com/img/jfs/t1/114663/1/35548/267341/64ad1855Fbf21e23e/e05d8260c88ac09a.png"
                ></Image>
                <View className="section_wrap">
                  <Text className="section">京东开店</Text>
                </View>
                <Text className="line2 txt1">college</Text>
              </View>
              <Image
                className="banner"
                src="//img13.360buyimg.com/img/jfs/t1/156639/7/37284/1054143/64ad1855F8806746a/7105f6c674b6ccc3.png"
              ></Image>
              <View className="bg_view2"></View>
              <Image
                className="bg"
                src="//img14.360buyimg.com/img/jfs/t1/183468/28/34154/433574/64ad1856F0153e1c6/171046d0b427d8f6.png"
              ></Image>
              <View className="cnt_col4">
                <Text className="txt2">student</Text>
                <View className="cnt_row5">
                  <Text className="tit1">赚人生</Text>
                  <Text className="line2 txt3">第一桶金</Text>
                </View>
                <Text className="line2 txt4">college</Text>
              </View>
              <View className="cnt_row6">
                <Text className="line3 txt5">
                  0元创业 | 定向辅导 | 收益可观
                </Text>
                <Text className="txt6">student</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      {/*以下是一个列表*/}
      <View className="cnt_col_list">
        {/*列表项*/}
        <View className="cnt_col_item_common">
          <View className="cnt_row7_common cnt_row7">
            <Image
              className="icon4_common"
              src="//img12.360buyimg.com/img/jfs/t1/93454/33/42033/429/649be154F7065bcb6/1e01e72c54e4b42f.png"
            ></Image>
            <Text className="txt7_common txt7">大学生开店特权</Text>
            <Image
              className="icon4_common icon5"
              src="//img13.360buyimg.com/img/jfs/t1/103720/31/29971/432/649be154Fb99e6b22/37cfa18ab1ec1b88.png"
            ></Image>
          </View>
          <View className="subtit_wrap_common subtit_wrap">
            <Text className="subtit_common">专属定制，0基础也能快速上手</Text>
          </View>
          <View className="cnt_col5_common">
            <Image
              className="main_img1"
              src="//img11.360buyimg.com/img/jfs/t1/176942/2/35287/124176/64ad1858F383153f4/7967fdf6a3922c85.png"
            ></Image>
            <View className="cnt_col6">
              <Text className="txt8">极低门槛</Text>
              <View className="wrapper1">
                <View className="wrapper2">
                  <View className="color_view1"></View>
                </View>
              </View>
              <Text className="txt9">10分钟开店成功</Text>
              <Text className="txt10">扣点低至0%</Text>
              <Text className="txt11">1台手机+1张身份证</Text>
              <Text className="txt10">0元开店试运营</Text>
            </View>
            <Text className="number_common">1</Text>
            {/*以下是一个列表*/}
            <View className="cnt_row_list_common">
              {/*列表项*/}
              <View className="cnt_col_item1_common cnt_col_item1">
                <View className="txt_wrap_common txt_wrap">
                  <Text className="txt12_common">定向创业辅导</Text>
                </View>
                <View className="wrapper3">
                  <View className="wrapper4">
                    <View className="color_view2"></View>
                  </View>
                </View>
                <View className="txt_wrap_common txt_wrap1">
                  <Text className="txt13_common txt13">免费电商知识培训</Text>
                </View>
                <View className="txt_wrap_common txt_wrap2">
                  <Text className="txt13_common txt14">开店技巧指引</Text>
                </View>
              </View>
              {/*列表项*/}
              <View className="cnt_col_item1_common cnt_col_item2">
                <View className="txt_wrap_common txt_wrap3">
                  <Text className="txt12_common">精准流量免费</Text>
                </View>
                <View className="wrapper3">
                  <View className="wrapper4">
                    <View className="color_view2"></View>
                  </View>
                </View>
                <View className="txt_wrap_common txt_wrap1">
                  <Text className="txt13_common txt13">超100项流量权益</Text>
                </View>
              </View>
              {/*列表项*/}
              <View className="cnt_col_item3_common">
                <View className="txt_wrap_common txt_wrap">
                  <Text className="txt12_common">无货源开店</Text>
                </View>
                <View className="wrapper3">
                  <View className="wrapper5">
                    <View className="color_view3"></View>
                  </View>
                </View>
                <View className="txt_wrap_common txt_wrap1">
                  <Text className="txt13_common txt13">海量货源挑选</Text>
                </View>
                <View className="txt_wrap_common txt_wrap2">
                  <Text className="txt13_common txt14">支持一件代发</Text>
                </View>
              </View>
              {/*列表项*/}
              <View className="cnt_col_item4_common">
                <View className="cnt_row8_common">
                  <Text className="txt15_common">首次成交仅需3天</Text>
                  <Text className="txt16_common">（平均）</Text>
                </View>
                <View className="wrapper6">
                  <View className="wrapper7">
                    <View className="color_view4"></View>
                  </View>
                </View>
                <View className="txt_wrap_common txt_wrap4">
                  <Text className="txt13_common txt17">课余经营</Text>
                </View>
                <View className="txt_wrap_common txt_wrap5">
                  <Text className="txt13_common txt18">收益赚不停</Text>
                </View>
              </View>
              <Text className="countdown_hour_common countdown_hour">2</Text>
              <Text className="txt19_common">5000元开店广告奖励金</Text>
              <Text className="countdown_hour_common countdown_sec">3</Text>
            </View>
            <Text className="number1">4</Text>
            <Text className="number2">5</Text>
          </View>
        </View>
        {/*列表项*/}
        <View className="cnt_col_item5_common cnt_col_item5">
          <View className="cnt_row7_common cnt_row9">
            <Image
              className="icon4_common"
              src="//img30.360buyimg.com/img/jfs/t1/143546/7/35698/499/649be37dF87a33a16/55b0b1784ca02476.png"
            ></Image>
            <Text className="txt7_common txt20">优秀大学生创业案列</Text>
            <Image
              className="icon4_common icon5"
              src="//img20.360buyimg.com/img/jfs/t1/111993/20/38131/491/649be37dF97c059fc/e0763888b547dbd5.png"
            ></Image>
          </View>
          <View className="banner_wrap_common banner_wrap">
            <Image
              className="banner1"
              src="//img13.360buyimg.com/img/jfs/t1/113883/8/40317/1178977/64ad1857Fa956e6ba/911add752b1367c4.png"
            ></Image>
          </View>
          <View className="subtit_wrap_common txt_wrap6">
            <Text className="txt21_common">大四学生开店创业卖潮牌</Text>
          </View>
          <View className="cnt_col7_common">
            <View className="txt_wrap7">
              <Text className="txt22">开店9个月</Text>
            </View>
            <View className="cnt_row10_common">
              <View className="txt_wrap8_common">
                <Text className="txt23_common">大四 </Text>
              </View>
              <Text className="txt24_common"> 618当天突破500万流水</Text>
            </View>
            <Text className="txt25_common">
              小马是即将毕业的大四学生，面对就业环境激烈，他一心只想创业，去年无意间看到京东0元开店，想着自己家里也有衣服的货源，就开始倒腾起来，经过半年多的摸索，最近一次618当天店铺流水突破500万，收益非常可观，现在打算毕业后全心投入这份事业中。
            </Text>
          </View>
          <View className="banner_wrap_common img_wrap">
            <Image
              className="img1"
              src="//img10.360buyimg.com/img/jfs/t1/58406/3/22503/1261/649be37dFcfbdeaba/472234d01251619e.png"
            ></Image>
          </View>
        </View>
        {/*列表项*/}
        <View className="cnt_col_item5_common cnt_col_item6">
          <View className="cnt_row11_common">
            <Image
              className="icon4_common icon6"
              src="//img30.360buyimg.com/img/jfs/t1/143546/7/35698/499/649be37dF87a33a16/55b0b1784ca02476.png"
            ></Image>
            <Text className="txt26_common">开店流程指导</Text>
            <Image
              className="icon4_common icon7"
              src="//img20.360buyimg.com/img/jfs/t1/111993/20/38131/491/649be37dF97c059fc/e0763888b547dbd5.png"
            ></Image>
            <Text className="instruction_common">保存指引图片</Text>
          </View>
          <View className="cnt_row12_common cnt_row12">
            <Image
              className="img2_common"
              src="//img30.360buyimg.com/img/jfs/t1/204818/28/38688/2480/64ad1856Fe493e491/b9fa77cc82cdfef5.png"
            ></Image>
            <View className="cnt_col8_common cnt_col8">
              <Text className="txt27_common">开店申请</Text>
              <Text className="txt28_common txt28">
                进入主题选择页，选择“个人”类型，点击我要开店
              </Text>
            </View>
          </View>
          <View className="cnt_row12_common cnt_row13">
            <View className="wrapper8_common">
              <View className="wrapper9_common">
                <View className="icon_wrap_common">
                  <Image
                    className="icon8_common"
                    src="//img10.360buyimg.com/img/jfs/t1/160735/32/37835/375/64ad1856F94d57932/7f250e1c78844f68.png"
                  ></Image>
                </View>
              </View>
            </View>
            <View className="cnt_col8_common cnt_col9">
              <Text className="txt27_common txt29">实名认证</Text>
              <Text className="txt28_common txt30">人脸识别及身份验证证</Text>
            </View>
          </View>
          <View className="cnt_row12_common cnt_row13">
            <View className="wrapper8_common">
              <View className="icon_wrap1_common">
                <Image
                  className="icon9_common"
                  src="//img11.360buyimg.com/img/jfs/t1/149787/2/36955/640/64ad1857F0f34a9ef/be81850a4b61e6d8.png"
                ></Image>
              </View>
            </View>
            <View className="cnt_col8_common cnt_col10">
              <Text className="txt27_common">下载京麦并完成资质认证</Text>
              <Text className="txt28_common txt28">
                进入应用市场，下载京麦APP并注册，进行资质认证
              </Text>
            </View>
          </View>
          <View className="cnt_row12_common cnt_row13">
            <View className="wrapper8_common">
              <Image
                className="icon10_common"
                src="//img20.360buyimg.com/img/jfs/t1/126421/10/36564/1092/64ad1857F0890aa14/44db4bd1baa6376b.png"
              ></Image>
            </View>
            <View className="cnt_col8_common cnt_col11">
              <Text className="txt27_common txt29">店铺启动</Text>
              <Text className="txt28_common txt30">
                资质审核通过后，绑定结算账户，店铺启动
              </Text>
            </View>
          </View>
        </View>
      </View>
      <View className="wrapper10">
        <View className="wrapper11">
          <View className="cnt_row14">
            <Text className="txt31">立即</Text>
            <Text className="number3">0</Text>
            <Text className="txt31">元开店</Text>{" "}
          </View>
        </View>
      </View>
    </View>
  );
}
export default Mod;
`;

export default function (context: vscode.ExtensionContext) {
  addRouterIn();

  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.ai.d2c', async () => {
      // 原AI生产应用示例代码，暂不对外
      // const d2c = new D2C();
      // await d2c.input(demoCode);
    })
  );
}
