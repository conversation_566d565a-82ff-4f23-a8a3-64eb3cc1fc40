// import { Ai } from '../llm/ai';

import EventAgent from './event';
import ComponentByASTAgent from './componentByAST';
import { saveLog } from '../utils/logs';

export default class D2C {
  // private ai: Ai;

  constructor() {
    console.log('D2C');
    // this.ai = new Ai(`#01 你充当前端开发专家。
    // #02 user 将提供一些关于前端代码问题的具体信息，而你的工作就是想出为 user 解决问题的策略。这可能包括建议代码、代码逻辑思路策略。
    // #03 你的代码应该遵循 SOLID and KISS and DRY principles
    // #04 你应该一步步思考完再作答
    // #05 你应该非常细心，不要遗漏 user 提问的任何信息或需求，回答要完整`);
  }

  async input(code) {
    // const agentList = [new ComponentAgent(), new EventAgent()];
    // const agentList = [new EventAgent()];
    const agentList = [new ComponentByASTAgent()];
    const execAgent = async (agent, file) => {
      try {
        let resultFileOrFiles = await agent.input(file);
        if (!Array.isArray(resultFileOrFiles)) resultFileOrFiles = [resultFileOrFiles];
        // 确保文件输入后，必定有文件输出
        return resultFileOrFiles.length > 0 ? resultFileOrFiles : [file];
      } catch (e) {
        console.error('[D2C]: execAgent error', e);
        return [file];
      }
    };
    const execAgentList = async (curIndex, files) => {
      const agent = agentList[curIndex];
      if (!agent) {
        return files;
      }
      const allResultFiles: any[] = [];
      for (const filesItem of files) {
        const resultFiles = await execAgent(agent, filesItem);
        allResultFiles.push(...resultFiles);
      }
      return await execAgentList(curIndex + 1, allResultFiles);
    };

    const res = await execAgentList(0, [{ code }]);
    saveLog('[D2C] D2C coding result:');
    res.forEach((item) => {
      saveLog(item.fileName + ':\n' + item.code);
    });
    console.log('[D2C] D2C coding result', res);
  }
}
