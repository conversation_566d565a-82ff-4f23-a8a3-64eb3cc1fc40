import { Ai } from '../llm/ai';
import { IBaseAgent } from '../interface';
import { parseChat } from '../utils/chatToFiles';

/**
 * 组件化 功能 Agent
 */
export default class ComponentAgent implements IBaseAgent {
  private ai: Ai;

  constructor() {
    this.ai =
      //   new <PERSON>(`You are an AI that outputs code. You will write a very long answer. Make sure that every detail of the instructions is, in the end, implemented as code.
      // No matter what you are asked, only output code (HTML or JSX/TSX) use the code's format.

      // CODE'S FORMAT:
      // Each code's file must strictly follow a markdown code block format, where the following tokens must be replaced such that
      // FILENAME is the lowercase file name including the file extension,
      // LANG is the markup code block language for the code's language, and CODE is the code:

      // FILENAME
      // \`\`\`LANG
      // CODE
      // \`\`\`

      // for examples:

      // index.js
      // \`\`\`javascript
      // const a = 1
      // const b = 1
      // console.log(a + b)
      // \`\`\`

      // Do not comment on what every file does.

      // Please note that the code should be fully functional. No placeholders.`);
      new <PERSON>(`
      As a senior frontend developer, you will be given a lengthy JS code and some instructions to refactor.
You will write a very long answer. Make sure that every detail of the instructions is, in the end, implemented as code.

Each code's file must strictly follow a markdown code block format, where the following tokens must be replaced such that
FILENAME is the lowercase file name including the file extension,
LANG is the markup code block language for the code's language, and CODE is the code:

FILENAME
\`\`\`LANG
CODE
\`\`\`

for examples:

index.js
\`\`\`javascript
const a = 1
const b = 1
console.log(a + b)
\`\`\`

Do not comment on what every file does.

Please note that the code should be fully functional. No placeholders.
Please note that you only output the implemented code by the user's instruction.
Please do not explain the result code.
Please do not retell the user input.
Please do not handle the style e.g. css, scss, less, etc.`);
  }

  async input(file) {
    const message = `
## THE CODE
\`\`\`JavaScript
${file.code}
\`\`\`

## INSTRUCTIONS
我希望你根据以下方式将 \`THE CODE\` 的代码进行更新:
1. 拆分组件分析：依据代码中的 DOM 结构进行分析，确认可以拆分成哪些组件（必须确保分割后的组件组合后能够完全覆盖到原有的DOM节点）。
2. 组件化拆分：将提供的代码进行组件化分割，具体来说按照分析对代码针对DOM结构进行分割，目标是将长代码组件化后提高代码的可读性（必须确保分割后的组件组合后能够完全覆盖到原有的DOM节点）。

请注意：
- 将DOM节点进行组件化分割后，所有组件组合后应该可以100%覆盖到原有的 DOM 结构。
- 组件化后可归为容器组件和展示组件，而展示组件的Dom节点数应优先控制在10到50个节点之间（极端情况可在2到60个节点），不应该过多或过少，这个很重要。
- 组件化后，不应该存在只有一个DOM节点的组件，即组件内包含的DOM节点应该大于一个，这个很重要。
- 组件化后，组件的名称应该具有语义化，即具有明确含义，如：不要使用类似 \`cnt\` 这样的名称。
- 分割后的所有组件组合后能够完全覆盖到原有的DOM节点。
- 在建立父子组件关系时，请确保子组件的DOM节点包含在父组件的DOM节点内。
- 组件化后，组件的层级结构应该与DOM结构保持一致。
- 组件化后，组件的样式应该与DOM结构保持一致。
- 请一个组件一个组件的输出，确保每个组件都是完整且合理的，组件间不存在耦合。
    `;
    // TODO：需要先判断是否需要组件化，如果不需要组件化，直接返回原代码
    const result = await this.ai.userRequest(message);
    const fullText = result.fullText;
    console.log('[D2C] ComponentAgent coding result', fullText);
    const files = parseChat(fullText);
    return files;
  }
}
