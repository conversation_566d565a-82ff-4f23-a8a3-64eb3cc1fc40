import { Ai } from '../llm/ai';
import { IBaseAgent } from '../interface';
import { parseChat } from '../utils/chatToFiles';

/**
 * 事件绑定 功能 Agent
 */
export default class EventAgent implements IBaseAgent {
  private ai: Ai;

  constructor() {
    this.ai =
      new <PERSON>(`You are a senior frontend engineer and now you are required to add dynamic logic to the given code.
      You need to implement the corresponding tasks according to the steps.

      You will write a very long answer. Make sure that every detail of the architecture is, in the end, implemented as code.

      Each code's file must strictly follow a markdown code block format, where the following tokens must be replaced such that
      FILEPATH is the file path including the file name and the file extension,
      LANG is the markup code block language for the code's language, and CODE is the code:

      FILEPATH
      \`\`\`LANG
      CODE
      \`\`\`

      for examples:

      index.js
      \`\`\`javascript
      const a = 1
      const b = 1
      console.log(a + b)
      \`\`\`

Please do not comment on what every file does
Please note that the code should be fully functional. No placeholders.
Please note that you only output the implemented code by the user's instruction.
Please do not explain the result code.
Please do not retell the user input.
Please do not handle the style e.g. css, scss, less, etc.

      Your answer always use Chinese.

      Let's work this out in a step by step way to be sure we have the right answer.
      `);
  }

  async input(file) {
    //     const message = `
    // ## THE CODE
    // \`\`\`JavaScript
    // ${file.code}
    // \`\`\`

    // ## INSTRUCTIONS
    // 我希望你根据以下方式将 \`THE CODE\` 的代码进行更新:
    // 1、列出dom节点的所有文案（同一个节点的文案应该是一个整体）。
    // 2、根据以上列出的文案逐一找到 \`THE CODE\` 对应的dom节点，根据其自身节点结合相邻兄弟文本节点、父节点，推测当前是一个什么模块或是跟相邻兄弟或父节点能组成一个什么模块。如：相同父节点下的文本节点拼接后可能是一个操作或者行为模块的描述，故应该按照父节点将相应子节点组合成一个模块（模块应该颗粒度化，并按钮模块应优先文本）。
    // 3、根据1中的文案筛选出具有动作导向的文案，结合2中的模块描述，推测哪些节点需要绑定点击事件，若需要则简要列出：具备语义化的事件函数名称、事件函数功能描述、事件应该绑定的节点。
    // 4、生成代码：根据以上步骤生成代码文件（绑定事件、生成事件对应方法）。

    // 请注意：
    // 1：所有文件输出都应该有完整的实现，不应该存在占位符。
    // 2：文件路径应该与原来保持一致。
    // 3：除了文件内容，其他描述应该尽可能精简表达，不存在冗赘话术。`;
    const message = `
## THE CODE
\`\`\`JavaScript
${file.code}
\`\`\`

## INSTRUCTIONS
我希望你根据以下方式将 \`THE CODE\` 的代码进行更新，目的是推测事件并绑定:
1、仔细查看以上代码，找到需要绑定事件的元素。
2、寻找具有交互性质的元素，例如按钮、输入框等（不需要为图片绑定事件）。
3、注意查找具有事件属性的元素，例如onClick、onSubmit等。
4、可以根据元素的类名、ID或其他属性来定位需要绑定事件的元素。
5、如果有嵌套的组件结构，需要逐层查找，找到最终需要绑定事件的元素。
6、确保找到的元素是用户可以与之交互的，并且需要绑定事件。
7、如果存在需要绑定的事件则简要列出：具备语义化的事件函数名称、事件函数功能描述、事件应该绑定的节点。
8、生成代码：根据以上步骤判断是否存在可以绑定的事件，不存在，则返回"没有需要绑定的事件"；存在，则根据以上步骤生成更新后的代码（完成绑定事件、生成事件对应方法）。

请注意：
1：所有文件输出都应该有完整的实现，不应该存在占位符。
2：文件路径应该与原来保持一致。
3：除了文件内容，其他描述应该尽可能精简表达，不存在冗赘话术。
4、请在生成更新代码之前不要输出其他代码。以免重复输出。
5、不要复述输入，不要生成样式类。`;
    const result = await this.ai.userRequest(message);
    const fullText = result.fullText;
    console.log('[D2C] EventAgent coding result', fullText);
    return parseChat(fullText);
  }
}
