import { Ai } from '../llm/ai';
import { IBaseAgent } from '../interface';
import { parseChat } from '../utils/chatToFiles';
import { addJSXElementId, componentByComps, removeJSXElementId } from '../utils/ast';

/**
 * 组件化 功能 Agent
 */
export default class ComponentByASTAgent implements IBaseAgent {
  private ai: Ai;

  constructor() {
    this.ai = new Ai(`
      As a senior frontend developer, you will be given a lengthy JS code and some instructions to refactor.
You will write a very long answer. Make sure that every detail of the instructions is, in the end, implemented as code.

Each code's file must strictly follow a markdown code block format, where the following tokens must be replaced such that
FILENAME is the lowercase file name including the file extension,
LANG is the markup code block language for the code's language, and CODE is the code:

FILENAME
\`\`\`LANG
CODE
\`\`\`

for examples:

index.js
\`\`\`javascript
const a = 1
const b = 1
console.log(a + b)
\`\`\`

Do not comment on what every file does.

Please note that the code should be fully functional. No placeholders.
Please note that you only output the implemented code by the user's instruction.
Please do not explain the result code.
Please do not retell the user input.
Please do not handle the style e.g. css, scss, less, etc.`);
  }

  async input(file) {
    const inputCode = addJSXElementId(file.code);
    const componentJSONName = 'components.json';
    const message = `
## THE CODE
\`\`\`JavaScript
${inputCode}
\`\`\`

## INSTRUCTIONS
我希望你根据以下方式将 \`THE CODE\` 的代码进行组件化拆分:
1. 拆分组件分析：依据代码中的 DOM 结构进行分析，简要列出可以拆分成哪些组件，目标是将长代码组件化后拆成多个功能组件，以提高代码的可读性（必须确保分割后的组件组合后能够完全覆盖到原有的DOM节点）。
2. 生成拆分后的组件化元文件（${componentJSONName}）：将组件对应的组件名和起始Dom节点id记录成JSON结构数组源文件。

JSON结构数组：
- 组件名（name）：通过上下文结构、当前组件内功能及文本推测出具有语义化的名称且必须唯一，不跟其他组件重名。
- 节点id（id）：当前组件起始Dom节点的对应的id值。

### 示例
输入：
\`\`\`JavaScript
function Mod() {
  return (
    <View id='1' className='mod'>
      <View id='2' className='cnt_col'>
          <View id='3' className='cnt_col2'>
              <Image id='4'
                className='icon2'
                src='//img20.360buyimg.com/img/jfs/t1/99124/35/40466/580/64ad1859F5e6cd042/99b4cc659b7c36bc.png'
              ></Image>
          </View>
          <Text id='5' className='txt_common'>京东开店助你成为大学生创业明星</Text>
      </View>
      <View id='6' className='wrapper10'>
          <View id='7' className='cnt_row14'>
            <Text id='8' className='txt31'>立即</Text>
            <Text id='9' className='number3'>0</Text>
            <Text id='10' className='txt31'>元开店</Text>
          </View>
      </View>
    </View>
  )
}
\`\`\`

输出：
${componentJSONName}
\`\`\`json
{
  components: [
    {name: 'Header', id: '2'},
    {name: 'OpenShopBtn', id: '7'}
  ]
}
\`\`\`

### 请注意：
- 组件化后可归为容器组件和展示组件，而展示组件的Dom节点数应优先控制在10到50个节点之间（极端情况可在2到60个节点），不应该过多或过少，这个很重要。
- 组件化后，不应该存在只有一个DOM节点的组件，即组件内包含的DOM节点应该大于一个，这个很重要。
- 组件化后，组件的名称应该具有语义化，即具有明确含义，如：不要使用类似 \`cnt\` 这样的名称。
- 组件名称不能重名，这个很重要。
- 不能将每个DOM节点都拆成一个单独的组件，这个很重要。
- 请仅严格按照输出格式，输出 ${componentJSONName} 及其内容即可，不需要其他代码或说明。
    `;
    // TODO：需要先判断是否需要组件化，如果不需要组件化，直接返回原代码
    const result = await this.ai.userRequest(message);
    const fullText = result.fullText;
    console.log('[D2C] ComponentAgent coding result', fullText);
    const files = parseChat(fullText);
    const componentJSON = files.find((file) => file.fileName === componentJSONName);
    if (componentJSON) {
      const componentJSONContent = componentJSON.code as string;
      const componentJSONObj = JSON.parse(componentJSONContent);
      const res = componentByComps(inputCode, componentJSONObj.components);
      // res.forEach((item) => {
      //   console.log('[D2C] ComponentAgent componentByComps result', item);
      // });
      const originFileName = file.fileName || '';
      const originFileExt = /\./.test(originFileName) ? originFileName.split('.').pop() : 'jsx';
      return res.map((item) => ({
        fileName: `${item.name}.${originFileExt}`,
        code: removeJSXElementId(item.code),
      }));
    }
    return [file];
  }
}
