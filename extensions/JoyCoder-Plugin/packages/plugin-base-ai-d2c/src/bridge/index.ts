/**
 * 通过vscode跳转协议打通Relay系统
 */
import * as vscode from 'vscode';
import { addRouter, getAssetsFilePath } from '@joycoder/shared';
import path from 'path';
// import { getChatGptWebviewLeftLoaded, postMessageToWebview } from '@joycoder/plugin-base-ai/src/dialog';
// import { AgentSceneType } from '@joycoder/plugin-base-ai/src/langchain/tools/shared/toolType';

interface ICode {
  extname: string;
  code: string;
  schema?: any;
}

interface ID2CInfo {
  from: string;
  projectName: string;
  code: ICode[];
}

export async function addRouterIn() {
  return new Promise((resolve) => {
    addRouter({
      path: '/d2c',
      callback: async (uri) => {
        const info: RegExpMatchArray | null = uri.query.match(/info=([^$&]+)/);
        if (!info) return resolve(info);

        const d2cInfo = JSON.parse(info[1]);
        resolve(d2cInfo);

        const selectedAction = await quickPick();
        if (!selectedAction) return;

        switch (selectedAction.actionType) {
          case 'ai':
            insertToAI(d2cInfo);
            break;
          case 'save':
            saveToFile(d2cInfo);
            break;
          case 'ai&save':
            await insertToAI(d2cInfo);
            await saveToFile(d2cInfo);
            break;
          default:
            break;
        }
      },
    });
  });
}

async function insertToAI(d2cInfo: ID2CInfo) {
  await vscode.commands.executeCommand('workbench.view.extension.JoyCode');

  // const insert = () => {
  //   const code = d2cInfo.code
  //     .map((item) => {
  //       return `// 文件名：index.${item.extname}\n${item.code}`;
  //     })
  //     .join('\n');
  //   postMessageToWebview({
  //     type: 'COMMON',
  //     payload: {
  //       type: 'updateContextPanel',
  //       data: { content: code, isFixed: true, noLineInfo: true },
  //       isLeft: true,
  //     },
  //   });

  //   postMessageToWebview({
  //     type: 'COMMON',
  //     payload: {
  //       type: 'updateSelectTool',
  //       data: { scene: AgentSceneType.D2C },
  //       isLeft: true,
  //     },
  //   });
  // };

  // 聊天窗已打开
  // const webviewLoaded = getChatGptWebviewLeftLoaded();
  // if (webviewLoaded) {
  //   insert();
  //   return;
  // }

  // // 聊天窗未打开
  // const loaded = await new Promise<boolean>((resolve) => {
  //   eventEmitter.on('webviewLoaded', function (loaded) {
  //     resolve(loaded);
  //   });
  // });
  // loaded && insert();
}

async function saveToFile(d2cInfo: ID2CInfo) {
  const folderUris = await vscode.window.showOpenDialog({
    canSelectMany: false,
    canSelectFiles: false,
    canSelectFolders: true,
    openLabel: '选择',
    defaultUri: vscode.workspace.workspaceFolders?.[0].uri,
  });
  if (!folderUris || !folderUris[0]) return;

  const folderUri = folderUris[0];
  const fileInfo = d2cInfo.code.map((item) => {
    return {
      uri: vscode.Uri.joinPath(folderUri, `indexPage.${item.extname}`),
      content: Buffer.from(item.code),
    };
  });

  for (let i = 0; i < fileInfo.length; i++) {
    const file = fileInfo[i];
    try {
      await vscode.workspace.fs.stat(file.uri);
      const overwrite = await vscode.window.showWarningMessage(
        `当前目录下已存在文件 ${path.basename(file.uri.path)}，是否覆盖?`,
        '是',
        '否'
      );
      if (overwrite === '是') {
        await vscode.workspace.fs.writeFile(file.uri, file.content);
      }
    } catch (error) {
      // 不存在覆盖文件的情况
      await vscode.workspace.fs.writeFile(file.uri, file.content);
    }
  }
}

async function quickPick() {
  const quickPick = vscode.window.createQuickPick();
  const pickList = [
    // {
    //   label: '插入到聊天窗',
    //   detail: '将代码插入聊天窗，便于AI进一步处理，如语义化、组件化等',
    //   iconPath: getAssetsFilePath('activitybar.svg'),
    //   actionType: 'ai',
    // },
    {
      label: '保存到项目',
      detail: '将代码保存到指定文件夹',
      iconPath: getAssetsFilePath('d2c/save.png'),
      actionType: 'save',
    },
    // {
    //   label: '插入聊天窗并保存',
    //   detail: '同时插入代码至聊天窗并保存',
    //   iconPath: getAssetsFilePath('d2c/all.png'),
    //   actionType: 'ai&save',
    // },
    {
      label: '暂不使用',
      detail: '关闭此选择器',
      iconPath: getAssetsFilePath('d2c/close.png'),
      actionType: 'close',
    },
  ];
  quickPick.items = pickList;
  quickPick.ignoreFocusOut = true;
  quickPick.placeholder = '您准备如何使用D2C工具生成的代码';
  quickPick.show();

  const target: any = await new Promise((resolve) => {
    quickPick.onDidAccept(() => resolve(quickPick.selectedItems[0]));
    quickPick.onDidHide(() => resolve(undefined));
  });

  quickPick.dispose();

  if (target?.actionType !== 'close') {
    return target;
  }
}
