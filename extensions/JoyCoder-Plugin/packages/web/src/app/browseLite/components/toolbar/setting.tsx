import React, { memo } from 'react';
import { Modal, Form, Input, ConfigProvider } from 'antd';
import { Theme, useTheme } from '../../../../hooks/useTheme';
import './setting.scss';

interface SettingModalProps {
  visible: boolean;
  onClose: any;
  browseConfig: any;
}

const SettingModal = ({ visible, browseConfig, onClose }: SettingModalProps) => {
  const [form] = Form.useForm();
  const { themeClassName } = useTheme({ forceTheme: Theme.Light });

  const handleSave = () => {
    form.validateFields().then((values) => {
      onClose(values || {});
    });
  };

  const handleCancel = () => {
    onClose();
    form.setFieldsValue(browseConfig);
  };

  return (
    <ConfigProvider prefixCls={themeClassName}>
      <Modal
        className="settingmodal"
        width="80%"
        open={visible}
        title="设置"
        onCancel={handleCancel}
        onOk={handleSave}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} initialValues={browseConfig} className="setting">
          <Form.Item
            name="proxy"
            label="代理"
            tooltip="只需IP+端口，如：127.0.0.1:8899"
            getValueFromEvent={(event) => event.target.value.replace(/\s+/g, '')}
          >
            <Input allowClear placeholder="示例: 127.0.0.1:8899" />
          </Form.Item>
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

export default memo(SettingModal);
