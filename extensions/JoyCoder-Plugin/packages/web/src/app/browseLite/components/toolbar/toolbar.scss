.toolbar {
    background: var(--vscode-tab-activeBackground);
    padding: 6px 2px 5px;
    border-bottom: 1px solid var(--vscode-tab-border);
    box-shadow: -1px 1px 2px rgba(0, 0, 0, 0.1);

    &-inner {
        display: flex;
    }

    button {
        color: var(--vscode-tab-activeForeground);
        width: 26px;
        height: 30px;
        cursor: pointer !important;
        border: none;
        outline: none;
        font-size: 22px;
        background: transparent;
        transition: background-color 0.2s ease-in-out;
        border-radius: 2px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0 2px;

        svg {
            display: block;
            font-size: 0.85em;
        }

        &:disabled {
            opacity: 0.1;
        }

        &.active {
            background-color: var(--vscode-button-secondaryBackground);
        }

        &:not([disabled]):hover,
        &:not([disabled]):active {
            background-color: var(--vscode-button-secondaryBackground);
        }

        &:not([disabled]):active:hover {
            background-color: var(--vscode-button-secondaryBackground);
        }
    }
}
