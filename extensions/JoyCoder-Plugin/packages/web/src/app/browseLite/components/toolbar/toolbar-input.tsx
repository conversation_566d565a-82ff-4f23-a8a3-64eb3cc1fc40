// 库
import React, { Component, RefObject } from 'react';
// 样式
import './toolbar-input.scss';
// 本地定义
type IToolbarInputProps = {
  url: string;
  onUrlChanged: (url: string) => void;
};
type IToolbarInputState = {
  url: string;
  hasChanged: boolean;
};

/**
 * url输入框
 */
export default class ToolbarInput extends Component<IToolbarInputProps, IToolbarInputState> {
  private inputRef: RefObject<HTMLInputElement> = React.createRef();

  constructor(props: IToolbarInputProps) {
    super(props);
    this.state = {
      url: this.props.url,
      hasChanged: false,
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps: IToolbarInputProps) {
    if (nextProps.url !== this.state.url && !this.state.hasChanged) {
      this.setState({
        url: nextProps.url,
      });
    }
  }

  public render() {
    return (
      <input
        className="toolbar-input"
        ref={this.inputRef}
        type="text"
        value={this.state.url}
        onFocus={this.handleFocus}
        onChange={this.handleChange}
        onKeyDown={this.handleKeyDown}
      />
    );
  }

  // --------------------事件函数--------------------
  /**
   * 输入框修改事件
   */
  private handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({
      url: event.target.value,
      hasChanged: true,
    });
  };

  /**
   * 输入框键盘按下事件
   */
  private handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    // 回车按键
    if (event.keyCode === 13) {
      let url = this.state.url.trim();
      const schemeRegex = /^(https?|about|chrome|file):/;
      if (!url.match(schemeRegex)) {
        url = `http://${this.state.url}`;
      }
      // 还原标记
      this.setState({
        hasChanged: false,
      });
      // 触发链接变化事件
      this.props.onUrlChanged(url);
    }
  };

  /**
   * 输入框点击事件
   */
  private handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    if (!event.target && !this.inputRef) {
      return;
    }
    // 延迟一点全选
    setTimeout(() => {
      ((event.target || this.inputRef) as HTMLInputElement).select();
    }, 100);
  };
}
