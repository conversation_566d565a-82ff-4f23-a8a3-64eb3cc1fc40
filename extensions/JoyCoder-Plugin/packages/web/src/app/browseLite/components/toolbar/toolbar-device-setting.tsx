// 库
import React, { Component } from 'react';
// 常量
import { EMULATED_DEVICE_LIST } from '../../shared/constant';
// 业务定义
import { IEmulatedDeviceData, IViewportMetadata } from '../../shared/interface';
import CommonTool from '../../shared/utils/CommonTool';
// 样式
import './toolbar-device-setting.scss';
// 本地定义
interface IToolbarDeviceSettingProps {
  viewportMetadata: IViewportMetadata;
  onSizeChange: (size: Record<string, number>) => void;
  onDeviceChange: (device: IEmulatedDeviceData) => void;
}
interface IToolbarDeviceSettingState {
  emulatedDeviceList: IEmulatedDeviceData[];
}

/**
 * 模拟设备设置栏
 */
export default class ToolbarDeviceSetting extends Component<IToolbarDeviceSettingProps, IToolbarDeviceSettingState> {
  constructor(props: IToolbarDeviceSettingProps) {
    super(props);
    this.state = {
      emulatedDeviceList: EMULATED_DEVICE_LIST,
    };
  }

  public render() {
    const {
      viewportMetadata: { width: realWidth, height: realHeight, deviceName, deviceScale, isEnabledDevice },
    } = this.props;
    const { emulatedDeviceList } = this.state;

    const {
      viewport: { width, height },
    } = CommonTool.getDeviceByDeviceName(deviceName);
    // 如果未启用模拟设备功能则直接返回
    if (!isEnabledDevice) {
      return null;
    }
    return (
      <div className="toolbar-device-setting">
        <select value={deviceName} className="toolbar-device-setting-selector" onChange={this.handleDeviceChange}>
          {emulatedDeviceList.map((item: IEmulatedDeviceData) => (
            <option key={item.name} value={item.name}>
              {item.name}
            </option>
          ))}
        </select>
        <input
          min="0"
          type="number"
          value={deviceName == 'Responsive' ? realWidth : width}
          disabled={deviceName !== 'Responsive'}
          className="toolbar-device-setting-input"
          onChange={this.handleWidthChange}
        />
        <span className="toolbar-device-setting-spacer">x</span>
        <input
          min="0"
          type="number"
          value={deviceName == 'Responsive' ? realHeight : height}
          disabled={deviceName !== 'Responsive'}
          className="toolbar-device-setting-input"
          onChange={this.handleHeightChange}
        />
        {deviceName !== 'Responsive' && <span className="toolbar-device-setting-scale">缩放（{deviceScale}%）</span>}
        {/* TODO: 增加UA设置逻辑 */}
      </div>
    );
  }

  // --------------------私有函数--------------------
  /**
   * 模拟设备变化事件
   */
  private handleDeviceChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    let device: IEmulatedDeviceData = this.state.emulatedDeviceList[0];
    // 遍历找到选择的设备信息
    this.state.emulatedDeviceList.some((item: IEmulatedDeviceData) => {
      if (item.name === event.target.value) {
        device = item;
        return true;
      }
      return false;
    });
    // 响应父级事件
    this.props.onDeviceChange(device);
  };

  /**
   * 尺寸变化事件
   */
  private handleWidthChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.props.onSizeChange({
      width: parseInt(event.target.value, 10),
      height: this.props.viewportMetadata.height,
    });
  };

  /**
   * 尺寸变化事件
   */
  private handleHeightChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.props.onSizeChange({
      width: this.props.viewportMetadata.width,
      height: parseInt(event.target.value, 10),
    });
  };
}
