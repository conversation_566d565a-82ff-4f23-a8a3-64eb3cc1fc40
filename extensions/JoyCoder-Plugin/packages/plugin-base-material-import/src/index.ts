import * as vscode from 'vscode';
import { groupBy } from 'lodash-es';
import * as fs from 'fs';
import * as path from 'path';
import {
  openInBrowser,
  openInVscode,
  reportRd,
  Logger,
  getProjectRootDirByFilePath,
  getLastAcitveTextEditor,
} from '@joycoder/shared';
import { getComLinkByName, getComCodingUrlByName } from '@joycoder/plugin-custom-quick-jump/src/getCompDocLinks';

const command = vscode.commands.registerCommand('JoyCode.materialImport.start', () => {
  const { visibleTextEditors } = vscode.window;

  if (visibleTextEditors.length) {
    openInVscode({
      pageName: 'materialImport',
      pageTitle: 'JoyCode:物料导入',
      // 存在打开的文件时做分隔，不存在是全屏打开
      layout: vscode.window.activeTextEditor
        ? { orientation: 0, groups: [{ size: 0.65 }, { size: 0.35 }] }
        : { orientation: 0, groups: [{}] },
      async onDidReceiveMessage({ type, content }) {
        if (type == 'insertSnippet') {
          const editor = vscode.window.activeTextEditor || getLastAcitveTextEditor();
          if (!editor) return Logger.showErrorMessage('请先在编辑器中选中要插入的位置~');

          const snippetList = content.code.split('\n');
          const listGroup = groupBy(snippetList, (item) => {
            return Number(item.trim().startsWith('import '));
          });

          const selection: vscode.Selection = editor.selection;

          // 插入import语句
          if (listGroup[1]) {
            await editor.insertSnippet(
              new vscode.SnippetString(listGroup[1].join('\n') + '\n'),
              new vscode.Position(0, 0)
            );
          }
          // 插入非import语句
          const codePosition = new vscode.Position(
            selection.start.line + (listGroup[1]?.length ?? 0),
            selection.start.character
          );
          await editor.insertSnippet(new vscode.SnippetString(listGroup[0].join('\n') + '\n'), codePosition);

          // 未安装时自动安装npm包
          if (content.npmName) {
            const rootDir = getProjectRootDirByFilePath(editor.document.fileName);
            const pckJsonPath = path.join(rootDir, 'package.json');
            const pckJson = JSON.parse(fs.readFileSync(pckJsonPath, 'utf8'));
            const hasNpm = pckJson?.dependencies[content.npmName] || pckJson?.devDependencies[content.npmName];
            if (!hasNpm) {
              const terminal = vscode.window.activeTerminal || vscode.window.createTerminal({ name: 'JoyCode' });
              terminal.show(true);
              terminal.sendText(`cd "${rootDir}"`);
              terminal.sendText(`npm install ${content.npmName} --save`);
            }
          }
          reportRd(7);
        } else if (type == 'jumpDocs') {
          const url = getComLinkByName(content);

          if (!url) {
            return Logger.showErrorMessage('未找到该组件文档');
          }
          if (/^https:\/\/coding.jd.com|http:\/\/legos.jd.com|https:\/\/github.com/.test(url)) {
            return openInBrowser(url);
          }

          openInVscode({
            pageTitle: `文档:${content.npmName}`,
            url,
            viewColumn: vscode.ViewColumn.Beside,
          });
        } else if (type == 'jumpCode') {
          const url = getComCodingUrlByName(content);
          if (!url) {
            Logger.showErrorMessage('未找到该组件文档');
            return;
          }

          openInBrowser(url);
        } else {
          vscode.window.showInformationMessage(content);
        }
      },
      onDidDispose() {},
    });
  } else {
    Logger.showErrorMessage('请先在编辑器打开一个文件再操作哦~');
  }
});

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(command);
}
