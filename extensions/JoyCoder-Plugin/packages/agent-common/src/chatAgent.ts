import 'dotenv/config';
import { ChatOpenAI } from '@langchain/openai';
import { StateGraphArgs } from '@langchain/langgraph';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableConfig } from '@langchain/core/runnables';
import { END, START, StateGraph } from '@langchain/langgraph';
import { MemorySaver } from '@langchain/langgraph';
import { DevConsole } from './utils';
import baiduSearch from './tools/baidu';
import { v4 as uuidv4 } from 'uuid';

const math = require('mathjs');

interface GraphState {
  task: string;
  planString: string;
  steps: string[][];
  results: Record<string, any>;
  result: string;
  conversationId: string;
  sessionId: string;
}

export default async function chatAgent(
  task: string,
  model: ChatOpenAI,
  options?: Record<string, any>,
  onProgress?: (msg: string) => void
) {
  if (typeof onProgress !== 'function') {
    onProgress = (msg: string) => {
      console.log(msg);
    };
  }
  const optionOnlineSearch = options?.onlineSearch;
  DevConsole.log('🚀 ~ optionOnlineSearch:', optionOnlineSearch);
  //options?.systemMessage 会影响效果
  const systemMessage =
    'You are a helpful assistant. When your task is to write code, you must review its execution and optimize it, You need to check JSON or Object structure carefully and avoid errors.';

  const abortController = options?.abortController || {};
  const { signal } = abortController;
  let isAborted = false;
  let llmPlanString = '';
  model.bind({ signal: signal });
  if (signal) {
    const handleAbort = () => {
      isAborted = true;
      DevConsole.log('isAborted', isAborted);
      onProgress && onProgress('[DONE]');
    };
    signal.addEventListener('abort', handleAbort);
  }

  //状态
  const graphState: StateGraphArgs<GraphState>['channels'] = {
    task: {
      value: (x: string, y?: string) => (y ? y : x),
      default: () => '',
    },
    planString: {
      value: (x: string, y?: string) => (y ? y : x),
      default: () => '',
    },
    steps: {
      value: (a: string[][], b: string[][]) => a.concat(b),
      default: () => [],
    },
    results: {
      value: (a: Record<string, any>, b: Record<string, any>) => ({ ...a, ...b }),
      default: () => ({}),
    },
    result: {
      value: (x: string, y?: string) => (y ? y : x),
      default: () => '',
    },
    conversationId: {
      value: (x: string, y?: string) => (y ? y : x),
      default: () => options?.conversationId || '',
    },
    sessionId: {
      value: (x: string, y?: string) => (y ? y : x),
      default: () => options?.sessionId || '',
    },
  };

  function getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要加1，并且补零
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  async function getPlan(state: GraphState, config?: RunnableConfig) {
    //用户取消
    if (isAborted) {
      return {
        steps: 0,
        planString: '已中止回答',
      };
    }
    onProgress && onProgress('规划任务');
    const currentDate = getCurrentDate() || '';
    const regexPattern = new RegExp('Plan\\s*\\d*:\\s*([^#]+)\\s*(#E\\d+)\\s*=\\s*(\\w+)\\s*\\[([^\\]]+)\\]', 'g');
    const task = state.task;
    const template = `For the following task. For each plan, indicate
  which external tool together with tool input to retrieve evidence. When the task involves generating code You should calling LLM tool.You can store the evidence into a 
  variable #E that can be called by later tools. Solve the problem step by step (Plan, #E1, Plan, #E2, Plan, #E3, ...). When calculations or number compare you should trust Calculator. If you don't know something, first Plan to understand what it is, then proceed to deal with the problem step by step.At the end you should summary the result.
  

  Tools can ONLY be one of the following:
  ${
    optionOnlineSearch
      ? `- Baidu[input]: Worker that searches results from Baidu search engine. Useful when you need to find short
  and succinct answers about a specific topic. You need to think more and filter out the most accurate information. The input should be a search query. When searching new info,add keywords like \`最新\` to find the latest news.`
      : ''
  }
  - LLM[input]: A pre-trained LLM like yourself. Useful when you need to act with general 
  world knowledge and common sense. Prioritize it when you are confident in solving the problem
  yourself. Input can be any instruction.When generating code, please check carefully to ensure the code can run directly. If generating HTML, please create the complete content within a single HTML file.
  - Calculator[input]: A calculator capable of processing math expressions. Suitable for tasks requiring calculations or number compare, The INPUT MUST SHOULD BE math expressions like \`2 * (3 + 4)\`2  and outputs the result as a number to string. Ensure inputs are valid math expressions.


  For example,
  Task: Thomas, Toby, and Rebecca worked a total of 157 hours in one week. Thomas worked x 
  hours. Toby worked 10 hours less than twice what Thomas worked, and Rebecca worked 8 hours 
  less than Toby. How many hours did Rebecca work? 
  Plan: Given Thomas worked x hours, translate the problem into algebraic expressions and solve with Wolfram Alpha.
  #E1 = WolframAlpha[Solve x + (2x - 10) + ((2x - 10) - 8) = 157]
  Plan: Find out the number of hours Thomas worked.
  #E2 = LLM[What is x, given #E1]
  Plan: Calculate the number of hours Rebecca worked.
  #E3 = Calculator[(2 * #E2 - 10) - 8]


  Important!
  Variables/results MUST be referenced using the # symbol!
  The Plan: will be executed as a program, so no coreference resolution apart from naive variable replacement is allowed.NEVER translate "Plan:" to  "计划："! You should create three Plan.Plan describe not including #E<step>.
  The ONLY way for steps to share context is by including #E<step> within the arguments of the tool.
  You should always speak in English.
  The current date is ${currentDate}.
  The results should be summarized and analyzed based on the tool's output.
    

  Begin! 
  Describe your plans with rich details. Create THREE plans (Plan, #E1, Plan, #E2, Plan, #E3, ...). Each Plan should be followed by only one #E.


  Task: {task}`;

    const promptTemplate = ChatPromptTemplate.fromMessages([
      ['system', systemMessage],
      ['human', template],
    ]);
    //@ts-ignore
    const planner = promptTemplate.pipe(model);
    DevConsole.log('task', task);
    const result = await planner.invoke({ task }, config);

    DevConsole.log('result', result);
    llmPlanString = result.content.toString();
    const planList = llmPlanString.split('\n').filter((planItem) => planItem.startsWith('Plan:'));

    if (planList.length > 0) {
      const ChinesePlan = await model.invoke(
        `翻译为中文一段话，50字内，不要代码，不要换行：${
          planList.join('').replace(/Plan:/g, '').replace(/#E\d+/g, '').trim() || ''
        }`
      );
      const ChinesePlanString = ChinesePlan.content.toString() || '';
      onProgress && ChinesePlanString && onProgress(ChinesePlanString);
    }

    // Find all matches in the sample text.
    const matches = result.content.toString().matchAll(regexPattern);
    DevConsole.log('matches', matches);

    const steps: string[][] = [];
    for (const match of matches) {
      const item = [match[1], match[2], match[3], match[4], match[0]];
      if (item.some((i) => i === undefined)) {
        onProgress && onProgress(`[DONE]`);
        throw new Error('定制计划异常，请稍后重试');
      }
      steps.push(item as string[]);
    }
    DevConsole.log('_plan_', steps, result, result.content.toString());

    return {
      steps,
      planString: result.content.toString(),
    };
  }

  const _getCurrentTask = (state: GraphState) => {
    DevConsole.log('_getCurrentTask', state.results, state);
    if (!state.results) {
      return 1;
    }
    if (Object.entries(state.results).length === state.steps.length) {
      return null;
    }
    return Object.entries(state.results).length + 1;
  };

  const _parseResult = (input: unknown) => {
    if (typeof input === 'string') {
      const parsedInput = JSON.parse(input);
      if (Array.isArray(parsedInput) && 'content' in parsedInput[0]) {
        // This means it is a tool result.
        return parsedInput.map(({ content }) => content).join('\n\n');
      }
    } else if (Array.isArray(input) && 'content' in input[0]) {
      return input.map(({ content }) => content).join('\n\n');
    }
    if (input && typeof input === 'object' && 'content' in input) {
      // If it's not a tool, we know it's an LLM result.
      const { content } = input;
      return content;
    }
    onProgress && onProgress(`[DONE]`);
    throw new Error('搜索解析异常，请稍后重试');
  };

  function calculator(expression: string) {
    DevConsole.log('toolsCalculate1 ', expression);

    try {
      const result = math.evaluate(expression);
      DevConsole.log('toolsCalculate2 ', expression, result);
      return `计算器结果: ${expression} 是 ${result} `
        .replace('>', '大于')
        .replace('<', '小于')
        .replace('true', '对的')
        .replace('false', '错的');
    } catch (error) {
      onProgress && onProgress(`[DONE]`);
      return 'Error: Invalid expression';
    }
  }

  // // 示例
  // const hoursWorked = calculate('(2 * 9 - 10) - 8'); // 假设 #E2 是 9
  // console.log(`Rebecca worked ${hoursWorked} hours.`); // 应该输出 Rebecca worked 0 hours.

  async function toolExecution(state: GraphState, config?: RunnableConfig) {
    //用户取消
    if (isAborted) {
      return {
        results: {},
      };
    }

    const _step = _getCurrentTask(state);
    DevConsole.log('_step', _step);

    if (_step === null) {
      onProgress && onProgress(`[DONE]`);
      throw new Error('任务处理异常，请稍后重试');
    }
    const [, stepName, tool, inputString, toolInputTemplate] = state.steps[_step - 1];
    DevConsole.log('---EXECUTE TOOL---state.steps---', state.steps[_step - 1]);
    DevConsole.log('---EXECUTE TOOL---stepName---', stepName);
    DevConsole.log('---EXECUTE TOOL---tool---', tool);
    DevConsole.log('---EXECUTE TOOL---inputString---', inputString);

    let toolInput = toolInputTemplate;
    const _results = state.results || {};
    for (const [k, v] of Object.entries(_results)) {
      toolInput = toolInput.replace(k, v);
    }
    DevConsole.log('toolInput', toolInput, tool, inputString, _results);

    let result: any = null;
    if (tool === 'Baidu') {
      if (optionOnlineSearch) {
        result = await baiduSearch(inputString, 18);
        DevConsole.log('---TOOL-result', result);
        onProgress && onProgress(`步骤${_step}，搜索找到${result && result.length}篇资料..`);
      } else {
        //默认关闭
        result = {
          content: '您关闭了联网搜索',
        };
      }
    } else if (tool === 'LLM') {
      const inputStringFilter = inputString.replace(/#E(\d+)/g, (match, number) => {
        return `步骤${number}`;
      });

      const ChineseLLM = await model.invoke(
        `翻译为中文一段话，50字内，不要代码，不要换行，要翻译内容：${inputStringFilter}`
      );
      const ChineseLLMString = ChineseLLM.content.toString() || '';
      onProgress && onProgress(`步骤${_step}：` + ChineseLLMString);
      //@ts-ignore
      result = await model.invoke(toolInput, config);
    } else if (tool === 'Calculator') {
      onProgress && onProgress(`步骤${_step}，计算处理`);
      const calRes = calculator(inputString);
      result = {
        content: calRes,
      };
    } else {
      onProgress && onProgress(`[DONE]`);
      throw new Error('无法找到任务 ' + String(tool) + ' ，请稍后重试');
    }
    DevConsole.log('_parseResult(result)', _parseResult(result));
    _results[stepName] = JSON.stringify(_parseResult(result), null, 2);
    return { results: _results };
  }

  const solvePromptUser = ChatPromptTemplate.fromTemplate(
    `Solve the following task or problem. To solve the problem, we have made step-by-step Plan and
  retrieved corresponding Evidence to each Plan. Use them with caution since long evidence might
  contain irrelevant information.
  
  {plan}
  
  Now solve the question or task according to provided Evidence above. Respond with the answer
  directly with no extra words.
  
  Task: {task}
  Response:`
  );

  const solvePrompt = ChatPromptTemplate.fromMessages([['system', systemMessage], solvePromptUser]);

  async function solve(state: GraphState, config?: RunnableConfig) {
    //用户取消
    if (isAborted) {
      return {
        result: '已中止回答',
      };
    }
    DevConsole.log('---SOLVE---');
    let plan = '';
    const _results = state.results || {};
    DevConsole.log('_results', _results);

    for (let [_plan, stepName, tool, toolInput] of state.steps) {
      for (const [k, v] of Object.entries(_results)) {
        toolInput = toolInput.replace(k, v);
      }
      _plan = _plan ? _plan : '';
      stepName = stepName ? stepName : '';
      tool = tool ? tool : '';
      plan += `Plan: ${_plan}\n${stepName} = ${tool}[${toolInput}]\n`;
    }
    onProgress && onProgress(`总结结果`);
    DevConsole.log('planplan', plan, state.task, state);
    //@ts-ignore
    const result = await solvePrompt.pipe(model).invoke({ plan, task: state.task }, config);
    DevConsole.log('---SOLVE-result--', result);
    return {
      result: result.content.toString(),
    };
  }

  const _route = (state: GraphState) => {
    const _step = _getCurrentTask(state);
    if (_step === null || isAborted === true) {
      // We have executed all tasks
      return 'solve';
    }
    // We are still executing tasks, loop back to the "tool" node
    return 'tool';
  };

  const workflow = new StateGraph<GraphState>({
    channels: graphState,
  })
    .addNode('plan', getPlan as any)
    .addNode('tool', toolExecution as any)
    .addNode('solve', solve as any)
    .addEdge('plan', 'tool')
    .addEdge('solve', END)
    .addConditionalEdges('tool', _route)
    .addEdge(START, 'plan');

  // Compile
  const app = workflow.compile({ checkpointer: new MemorySaver() });
  const uuid = uuidv4();
  const threadConfig = { configurable: { thread_id: String(uuid) } };
  let finalResult: any = null;
  const stream = await app.stream(
    {
      task: task,
    },
    threadConfig
  );
  for await (const item of stream) {
    finalResult = item;
  }
  onProgress && onProgress(`[DONE]`);
  return finalResult;
}
