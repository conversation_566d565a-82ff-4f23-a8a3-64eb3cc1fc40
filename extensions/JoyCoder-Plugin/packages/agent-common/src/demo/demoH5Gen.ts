import h5GenAgent from '../h5GenAgent';
import { ChatOpenAI } from '@langchain/openai';
const fs = require('fs');

// 运行demo需要设置好环境变量
// export OPENAI_API_KEY=xxx
//https://js.langchain.com/v0.2/docs/integrations/chat/openai/
const model = new ChatOpenAI({
  model: 'gpt-4o', //process.env.OPENAI_API_MODEL
  temperature: 0.1,
  apiKey: process.env.OPENAI_API_KEY || 'f7d94988-5d8a-4ce6-aac5-58ff0b680aac',
  configuration: {
    baseURL: 'http://gpt-proxy.jd.com/v1/', //大模型网关
  },
});

const html = `// 文件名：index.jsx
import React from 'react'
import Taro from '@tarojs/taro'
import { Text } from '@tarojs/components'
import './Line1Number.scss'
function Line1Number() {
  return <Text className="line1 number">58</Text>
}
export default Line1Number

// 文件名：index.scss
.number {
	flex-shrink:0;
	align-self:flex-start;
	font-size:180px;
	font-family:Gotham-Bold;
	font-weight:700;
	line-height:1;
	color:rgb(251,253,224);
	text-align:right;
	text-shadow:0px 1px 5px rgb(237,0,11);
	letter-spacing:-10px;
}
.line1 {
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
}
`;
// const html = `

// <template>
// <div class="mod">
//   <div class="cnt_col">
//     <div class="txt_wrap">
//       <span class="line1 txt">切图示意</span>
//     </div>
//     <img src="//img20.360buyimg.com/ling/jfs/t1/75069/37/27542/106997/66b0736eFd44dcc52/6e2d66c23a623055.png" class="act" />
//     <div class="cnt_row">
//       <img src="//img11.360buyimg.com/ling/jfs/t1/40218/6/22245/29032/66b0736eFdbb17216/5d9d6a0e1f4af8d3.png" class="img" />
//       <div class="cnt_col1">
//         <span class="line1 txt1">副标题文案副标题文案副</span>
//         <div class="cnt_row1">
//           <img src="//img12.360buyimg.com/ling/jfs/t1/236529/39/23124/17245/66b0736eFbf9abe98/ade13a79e78c50c3.png" class="img1" />
//           <img src="//img12.360buyimg.com/ling/jfs/t1/236529/39/23124/17245/66b0736eFbf9abe98/ade13a79e78c50c3.png" class="img1" />
//           <img src="//img20.360buyimg.com/ling/jfs/t1/90374/28/44737/17263/66b0736eF54427b01/9bd3ded0b559718f.png" class="img1" />
//           <img src="//img20.360buyimg.com/ling/jfs/t1/90374/28/44737/17263/66b0736eF54427b01/9bd3ded0b559718f.png" class="img1" />
//           <img src="//img12.360buyimg.com/ling/jfs/t1/18064/22/22500/17264/66b0736eF5ed643c9/5e41574b3b33bed7.png" class="img1" />
//         </div>
//       </div>
//       <img src="//img14.360buyimg.com/ling/jfs/t1/81381/10/27230/17241/66b0736eFbc913d74/2688fc8f8dcb7503.png" class="img" />
//     </div>
//     <img src="//img13.360buyimg.com/ling/jfs/t1/230205/14/25155/456/66b0736eF976adab0/15aa2249a22c6a30.png" class="icon" />
//     <div class="cnt_row2">
//       <div class="cnt_row3">
//         <div class="cnt_row4">
//           <span class="txt2">Da</span>
//           <span class="txt3">y</span>
//           <span class="number">1</span>
//         </div>
//         <span class="section">今天</span>
//       </div>
//       <div class="color_view"></div>
//       <div class="cnt_row5">
//         <span class="txt4">Da</span>
//         <span class="txt3">y</span>
//         <span class="number">3</span>
//       </div>
//       <div class="cnt_row5">
//         <span class="txt4">Da</span>
//         <span class="txt3">y</span>
//         <span class="number">4</span>
//       </div>
//       <div class="cnt_row5">
//         <span class="txt5">Da</span>
//         <span class="txt3">y</span>
//         <span class="number">5</span>
//       </div>
//       <div class="cnt_row5">
//         <span class="txt4">Da</span>
//         <span class="txt3">y</span>
//         <span class="number">6</span>
//       </div>
//       <div class="cnt_row5">
//         <span class="txt6">Da</span>
//         <span class="txt3">y</span>
//         <span class="number">7</span>
//       </div>
//     </div>
//     <span class="line1 txt7">切图示意</span>
//     <div class="cnt_col2">
//       <span class="line1 txt8">礼盒</span>
//       <span class="line1 txt9">切图示意</span>
//     </div>
//     <img src="//img14.360buyimg.com/ling/jfs/t1/67185/20/27479/648228/66b0736eFb6a132e7/891237cbba334f44.png" class="banner" />
//     <div class="wrapper">
//       <div class="cnt_row6">
//         <img src="//img14.360buyimg.com/ling/jfs/t1/77777/29/28438/109602/66b0736eF109e450e/39c3a49f6e32b066.png" class="main_img" />
//         <img src="//img10.360buyimg.com/ling/jfs/t1/80014/20/26966/15334/66b0736eF8f5d6044/d81b01321a4afd1c.png" class="img2" />
//         <img src="//img30.360buyimg.com/ling/jfs/t1/66962/29/28106/65908/66b0736eFabde71e0/434eb3daa1d911ba.png" class="img3" />
//         <div class="cnt_row7">
//           <img src="//img12.360buyimg.com/ling/jfs/t1/80407/35/27260/10023/66b0736eFd6df9dba/4fdce65518196f8e.png" class="img4" />
//           <img src="//img14.360buyimg.com/ling/jfs/t1/243884/30/10642/5161/66b0736eF13c83ada/bfe49cf994f21dd6.png" class="img5" />
//         </div>
//         <img src="//img20.360buyimg.com/ling/jfs/t1/17690/39/21627/27134/66b0736eF5fe1bfab/0ad4792e5882a8f2.png" class="img6" />
//         <span class="line1 tit">马上拆</span>
//       </div>
//     </div>
//     <div class="cnt_col3">
//       <div class="cnt_row8">
//         <span class="line1 txt10">今日奖品</span>
//         <div class="section_wrap">
//           <span class="line1 section1">我的奖品</span>
//         </div>
//       </div>
//       <!--以下是一个列表-->
//       <div class="cnt_row_list">
//         <!--列表项-->
//         <div class="cnt_col_item_common cnt_col_item">
//           <div class="cnt_col4_common cnt_col4">
//             <!--商品价格-->
//             <div class="price_wrap">
//               <span class="yuan_common">¥</span>
//               <span class="price_common">22.22</span>
//             </div>
//             <span class="line1 txt11_common">现金红包</span>
//           </div>
//           <div class="section_wrap1_common section_wrap1">
//             <span class="line1 section2_common">去提现</span>
//           </div>
//         </div>
//         <!--列表项-->
//         <div class="cnt_col_item_common cnt_col_item">
//           <div class="cnt_col4_common cnt_col4">
//             <!--商品价格-->
//             <div class="price_wrap1">
//               <span class="yuan_common yuan1">¥</span>
//               <span class="price_common">2.22</span>
//             </div>
//             <span class="line1 txt11_common">现金红包</span>
//           </div>
//           <div class="section_wrap1_common section_wrap1">
//             <span class="line1 section2_common">去提现</span>
//           </div>
//         </div>
//         <!--列表项-->
//         <div class="cnt_col_item_common cnt_col_item">
//           <div class="cnt_col4_common cnt_col5">
//             <!--商品价格-->
//             <div class="price_wrap2">
//               <span class="yuan2">¥</span>
//               <span class="price_common price1">3.24</span>
//             </div>
//             <span class="line1 txt11_common">优惠券</span>
//           </div>
//           <div class="section_wrap1_common section_wrap1">
//             <span class="line1 section2_common">去使用</span>
//           </div>
//         </div>
//         <!--列表项-->
//         <div class="cnt_col_item_common">
//           <div class="section_wrap1_common section_wrap2">
//             <span class="line1 section3">实物图</span>
//           </div>
//           <div class="section_wrap1_common section_wrap1">
//             <span class="line1 section2_common section4">按钮文案</span>
//           </div>
//         </div>
//       </div>
//     </div>
//   </div>
// </div>
// </template>
// <script>
// export default {}
// </script>
// <style>
// .mod {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-start;
// overflow: hidden;
// width: 750px;
// height: 1624px;
// background-color: rgb(255, 255, 255);
// font-family: JDZhengHT;
// color: rgb(255, 255, 255);
// }
// .cnt_col {
// display: flex;
// flex-direction: column;
// justify-content: flex-start;
// align-items: flex-end;
// position: relative;
// width: 750px;
// height: 1624px;
// background: url(//img13.360buyimg.com/ling/jfs/t1/84444/29/26686/24956/66b0736fF1702f8a8/99e6b6c129de6b3e.jpg) no-repeat center center;
// background-size: 100% auto;
// }
// .cnt_col3 {
// display: flex;
// flex-direction: column;
// justify-content: center;
// align-items: flex-end;
// align-self: center;
// width: 686px;
// height: 350px;
// margin-top: 702px;
// background: url(//img30.360buyimg.com/ling/jfs/t1/242129/4/15349/22504/66b0736eFbf44d215/eb2e47e9c15a1cc9.png) no-repeat center center;
// background-size: 100% auto;
// font-family: JDZhengHT-EN;
// }
// .cnt_row_list {
// display: flex;
// flex-direction: row;
// flex-wrap: wrap;
// justify-content: flex-start;
// align-items: flex-start;
// align-self: center;
// overflow: hidden;
// width: 628px;
// height: 215px;
// font-weight: 500;
// }
// .section4 {
// font-weight: 500;
// color: rgb(255, 255, 255);
// }
// .section_wrap2 {
// height: 160px;
// background: url(//img13.360buyimg.com/ling/jfs/t1/8982/26/25585/18521/66b0736eF1ff30a8c/2c70916f9d63ec38.png) no-repeat center center;
// }
// .section3 {
// flex-shrink: 0;
// margin-left: 2px;
// font-size: 28px;
// font-family: PingFangSC-Regular;
// font-weight: 400;
// line-height: 1;
// color: rgb(255, 6, 111);
// text-align: center;
// letter-spacing: -1px;
// }
// .cnt_col5 {
// background: url(//img20.360buyimg.com/ling/jfs/t1/233116/4/23005/20125/66b0736eF11557456/8b88029133dcdbd0.png) no-repeat center center;
// }
// .price_wrap2 {
// display: flex;
// flex-direction: row;
// align-items: baseline;
// width: 104px;
// height: 58px;
// margin-top: 24px;
// font-weight: 700;
// color: rgb(255, 6, 111);
// }
// .price1 {
// margin-left: 2px;
// text-align: center;
// }
// .yuan2 {
// flex-shrink: 0;
// font-size: 28px;
// line-height: 1.21;
// text-align: center;
// letter-spacing: -1px;
// }
// .price_wrap1 {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: baseline;
// width: 102px;
// height: 58px;
// margin-top: 24px;
// margin-left: -14px;
// font-weight: 700;
// color: rgb(255, 6, 111);
// }
// .yuan1 {
// margin-left: -2px;
// }
// .cnt_col_item_common {
// display: flex;
// flex-direction: column;
// justify-content: flex-start;
// align-items: flex-start;
// align-self: flex-start;
// width: 148px;
// height: 215px;
// margin-bottom: 0px;
// }
// .cnt_col_item {
// margin-right: 12px;
// }
// .section_wrap1_common {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: center;
// width: 148px;
// background-size: 100% auto;
// }
// .section_wrap1 {
// height: 55px;
// background: url(//img10.360buyimg.com/ling/jfs/t1/58307/34/25970/8986/66b0736eF05ab865e/00015a447a349793.png) no-repeat center center;
// }
// .section2_common {
// flex-shrink: 0;
// margin-top: 3px;
// font-size: 22px;
// font-family: PingFangSC-Medium;
// line-height: 1;
// text-align: center;
// }
// .cnt_col4_common {
// display: flex;
// flex-direction: column;
// justify-content: flex-start;
// align-items: center;
// width: 148px;
// height: 160px;
// background-size: 100% auto;
// }
// .cnt_col4 {
// background: url(//img14.360buyimg.com/ling/jfs/t1/10725/6/25200/23141/66b0736eF3debe35a/85e46238a205ba54.png) no-repeat center center;
// }
// .txt11_common {
// flex-shrink: 0;
// opacity: 0.8;
// margin-top: 6px;
// font-size: 22px;
// font-family: PingFangSC-Regular;
// font-weight: 400;
// line-height: 1;
// color: rgb(255, 137, 159);
// text-align: center;
// }
// .price_wrap {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: baseline;
// align-self: flex-start;
// width: 123.7px;
// height: 58px;
// margin-top: 24px;
// margin-left: 3px;
// font-weight: 700;
// color: rgb(255, 6, 111);
// }
// .price_common {
// flex-shrink: 0;
// overflow: hidden;
// font-size: 48px;
// line-height: 1.21;
// letter-spacing: -2px;
// }
// .yuan_common {
// flex-shrink: 0;
// font-size: 28px;
// line-height: 2.07;
// letter-spacing: -2px;
// }
// .cnt_row8 {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-end;
// align-self: flex-end;
// width: 415px;
// height: 43px;
// margin-top: -2px;
// margin-bottom: 24px;
// font-weight: 400;
// }
// .section_wrap {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: center;
// align-self: flex-start;
// width: 128px;
// height: 40px;
// margin-left: 143px;
// background-color: rgb(255, 255, 255);
// border-top-left-radius: 20px;
// border-top-right-radius: 0px;
// border-bottom-right-radius: 0px;
// border-bottom-left-radius: 20px;
// }
// .section1 {
// flex-shrink: 0;
// opacity: 0.49;
// margin-left: -4px;
// font-size: 24px;
// font-family: PingFangSC-Regular;
// line-height: 1;
// color: rgb(255, 6, 111);
// text-align: center;
// }
// .txt10 {
// flex-shrink: 0;
// align-self: flex-end;
// font-size: 36px;
// font-family: MFYuanHei-Regular;
// line-height: 1;
// color: rgb(51, 51, 51);
// text-align: center;
// }
// .wrapper {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: center;
// position: absolute;
// z-index: 15;
// top: 1000px;
// left: 109px;
// width: 532px;
// height: 154px;
// background: url(//img10.360buyimg.com/ling/jfs/t1/83384/28/26117/29067/66b0736eFec73ecc9/fc33a321ea168428.png) no-repeat center center;
// background-size: 100% auto;
// }
// .cnt_row6 {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: center;
// position: relative;
// overflow: hidden;
// width: 522px;
// height: 144px;
// background: url(//img13.360buyimg.com/ling/jfs/t1/21088/9/22253/75681/66b0736eFeece7595/4afa51e32f42c25d.png) no-repeat center center;
// background-size: 100% auto;
// }
// .tit {
// flex-shrink: 0;
// position: relative;
// z-index: 15;
// margin-top: 2px;
// margin-left: 2px;
// font-size: 60px;
// font-family: MFYuanHei-Regular;
// font-weight: 400;
// line-height: 1;
// text-align: center;
// text-shadow: 0px 2px 8px rgba(236, 0, 116, 0.42);
// }
// .img6 {
// position: absolute;
// z-index: 14;
// top: 99px;
// left: 123px;
// width: 278px;
// height: 71px;
// }
// .cnt_row7 {
// display: flex;
// flex-direction: row;
// align-items: flex-start;
// position: absolute;
// z-index: 13;
// top: -5px;
// left: -5px;
// width: 532px;
// height: 154px;
// background: url(//img10.360buyimg.com/ling/jfs/t1/247393/14/16644/32763/66b0736eFe58a41d3/9105dc548c9fa038.png) no-repeat center center;
// background-size: 100% auto;
// }
// .img5 {
// align-self: flex-start;
// position: relative;
// z-index: 11;
// width: 430px;
// height: 49px;
// margin-top: 15px;
// margin-left: 54px;
// }
// .img4 {
// position: absolute;
// z-index: 10;
// top: 47px;
// left: 50px;
// width: 419px;
// height: 41px;
// }
// .img3 {
// position: absolute;
// z-index: 12;
// top: 45px;
// left: -12px;
// width: 547px;
// height: 117px;
// }
// .img2 {
// position: absolute;
// z-index: 11;
// top: 55px;
// left: 412px;
// width: 74px;
// height: 73px;
// }
// .main_img {
// position: absolute;
// z-index: 10;
// top: -80px;
// left: -103px;
// width: 247px;
// height: 162px;
// }
// .banner {
// position: absolute;
// z-index: 13;
// top: 650px;
// left: 33px;
// width: 697px;
// height: 384px;
// }
// .cnt_col2 {
// display: flex;
// flex-direction: column;
// justify-content: flex-end;
// align-items: center;
// position: absolute;
// z-index: 16;
// top: 526px;
// left: 206px;
// width: 340px;
// height: 294px;
// background-color: rgb(216, 216, 216);
// border-width: 8px;
// border-style: solid;
// border-color: rgb(151, 151, 151);
// border-radius: 20px;
// font-family: PingFangSC-Medium;
// font-weight: 500;
// }
// .txt9 {
// flex-shrink: 0;
// align-self: flex-start;
// opacity: 0.45;
// margin-bottom: 24px;
// margin-left: 31px;
// font-size: 33px;
// line-height: 1;
// text-align: center;
// }
// .txt8 {
// flex-shrink: 0;
// margin-bottom: 64px;
// margin-left: -2px;
// font-size: 49px;
// line-height: 1;
// color: rgb(0, 0, 0);
// text-align: center;
// }
// .txt7 {
// flex-shrink: 0;
// align-self: flex-end;
// opacity: 0.45;
// margin-top: 35px;
// margin-right: 82px;
// font-size: 33px;
// font-family: PingFangSC-Medium;
// font-weight: 500;
// line-height: 1;
// text-align: center;
// }
// .cnt_row2 {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-start;
// align-self: center;
// width: 600px;
// height: 32px;
// margin-top: 4px;
// background: url(//img20.360buyimg.com/ling/jfs/t1/40096/9/23138/3913/66b0736eF74b34afc/acd5f8b46b8b2efd.png) no-repeat center center;
// background-size: 100% auto;
// font-weight: 400;
// }
// .txt6 {
// flex-shrink: 0;
// margin-left: -22px;
// font-size: 22px;
// line-height: 1.18;
// }
// .txt5 {
// flex-shrink: 0;
// margin-left: -20px;
// font-size: 22px;
// line-height: 1.18;
// }
// .cnt_row5 {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: baseline;
// align-self: flex-start;
// width: 72px;
// height: 26px;
// margin-top: 1px;
// margin-left: 16px;
// }
// .txt4 {
// flex-shrink: 0;
// margin-left: -21px;
// font-size: 22px;
// line-height: 1.18;
// }
// .color_view {
// width: 72px;
// height: 32px;
// margin-left: 16px;
// background-color: rgb(255, 255, 255);
// border-radius: 16px;
// }
// .cnt_row3 {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-start;
// width: 72px;
// height: 32px;
// background-color: rgb(255, 255, 255);
// border-radius: 16px;
// }
// .section {
// flex-shrink: 0;
// align-self: center;
// width: 72px;
// margin-left: -72px;
// font-size: 22px;
// font-family: PingFangSC-Medium;
// font-weight: 500;
// line-height: 1;
// color: rgb(255, 0, 147);
// text-align: center;
// }
// .cnt_row4 {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: baseline;
// align-self: flex-start;
// width: 72px;
// height: 26px;
// margin-top: 1px;
// font-weight: 400;
// color: rgb(255, 255, 255);
// }
// .number {
// flex-shrink: 0;
// font-size: 22px;
// line-height: 1.18;
// }
// .txt3 {
// flex-shrink: 0;
// font-size: 22px;
// line-height: 1.18;
// letter-spacing: 2px;
// }
// .txt2 {
// flex-shrink: 0;
// margin-left: -23px;
// font-size: 22px;
// line-height: 1.18;
// }
// .icon {
// position: absolute;
// z-index: 18;
// top: 386px;
// left: 184px;
// width: 25px;
// height: 16px;
// }
// .cnt_row {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-end;
// align-self: center;
// position: relative;
// z-index: 12;
// width: 616px;
// height: 123px;
// margin-top: 112px;
// }
// .cnt_col1 {
// display: flex;
// flex-direction: column;
// justify-content: flex-start;
// align-items: center;
// width: 440px;
// height: 123px;
// }
// .cnt_row1 {
// display: flex;
// flex-direction: row;
// justify-content: flex-start;
// align-items: flex-start;
// width: 440px;
// height: 84px;
// margin-top: -3px;
// }
// .img1 {
// width: 88px;
// height: 84px;
// }
// .txt1 {
// flex-shrink: 0;
// margin-left: 4px;
// font-size: 30px;
// font-family: PingFangSC-Regular;
// font-weight: 400;
// line-height: 1;
// text-align: center;
// }
// .img {
// align-self: flex-end;
// width: 88px;
// height: 84px;
// }
// .act {
// position: absolute;
// z-index: 11;
// top: 166px;
// left: 0px;
// width: 750px;
// height: 102px;
// }
// .txt_wrap {
// display: flex;
// flex-direction: row;
// justify-content: center;
// align-items: flex-end;
// width: 750px;
// height: 150px;
// background: url(//img14.360buyimg.com/ling/jfs/t1/56407/26/25440/14132/66b07373Fb458dcb9/6141238522582eed.png) no-repeat center center;
// background-size: 100% auto;
// }
// .txt {
// flex-shrink: 0;
// align-self: flex-end;
// opacity: 0.45;
// margin-bottom: 6px;
// margin-left: 2px;
// font-size: 33px;
// font-family: PingFangSC-Medium;
// font-weight: 500;
// line-height: 1;
// text-align: center;
// }
// .line1 {
// overflow: hidden;
// text-overflow: ellipsis;
// white-space: nowrap;
// }
// </style>

// `;

function writeResultToFile(result: string, filename = 'output.html') {
  fs.writeFile(filename, result, 'utf8', (err: Error) => {
    if (err) {
      console.error('写入文件时发生错误:', err);
    } else {
      console.log(`结果已成功写入文件: ${filename}`);
    }
  });
}

h5GenAgent(html, model).then((result) => {
  // console.log('result', result);
  writeResultToFile(result); // 调用函数将结果写入文件
});
