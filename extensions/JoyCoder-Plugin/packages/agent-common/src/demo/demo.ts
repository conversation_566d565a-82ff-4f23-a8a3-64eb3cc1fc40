import chatAgent from '../chatAgent';
import { ChatOpenAI } from '@langchain/openai';

//测试
//https://js.langchain.com/v0.2/docs/integrations/chat/openai/
const model = new ChatOpenAI({
  model: process.env.OPENAI_API_MODEL || 'gpt-4o',
  temperature: 0.1,
  apiKey: process.env.OPENAI_API_KEY,
  configuration: {
    baseURL: process.env.OPENAI_API_BASE,
  },
});

const task = '小米SU7汽车不同车型售价是多少？';
chatAgent(task, model);
