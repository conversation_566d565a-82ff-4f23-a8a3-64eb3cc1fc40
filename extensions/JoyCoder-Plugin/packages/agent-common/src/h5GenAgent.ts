import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { EventEmitter } from 'events';
import { Logger } from '@joycoder/shared';

const systemMessage =
  'You are a helpful assistant. When your task is to write code, you must review its execution and optimize it, You need to check JSON or Object structure carefully and avoid errors.';

/**
 * 将HTML文本分割成多个文档片段。
 * @param text - 需要分割的HTML文本。
 * @returns 分割后的文档片段数组。
 */
export async function htmlSplite(text: string) {
  const splitter = RecursiveCharacterTextSplitter.fromLanguage('html', {
    chunkSize: 5000,
    chunkOverlap: 0,
    // separators: ['\n'],
  });
  const output = await splitter.createDocuments([text]);
  // console.log(output);
  return output;
}
function splitCodeByLanguage(code: string): { tsCode: string; cssCode: string } {
  const tsCodeLines: string[] = [];
  const cssCodeLines: string[] = [];
  let isCss = false;

  const lines = code.split('\n');
  for (const line of lines) {
    if (line.trim().startsWith('/*') && line.trim().endsWith('*/')) {
      // Skip comment lines
      continue;
    }
    if (line.trim().startsWith('import') || line.trim().startsWith('function') || line.trim().startsWith('export')) {
      isCss = false;
    }
    if (line.trim().startsWith('.')) {
      isCss = true;
    }
    if (isCss) {
      cssCodeLines.push(line);
    } else {
      tsCodeLines.push(line);
    }
  }

  return {
    tsCode: tsCodeLines.join('\n'),
    cssCode: cssCodeLines.join('\n'),
  };
}

function splitVueComponents(vueFileContent: string) {
  const templateMatch = vueFileContent.match(/(<template>[\s\S]*?<\/template>)/);
  const scriptMatch = vueFileContent.match(/(<script>[\s\S]*?<\/script>)/);
  const styleMatch = vueFileContent.match(/<style\b[^>]*>([\s\S]*?)<\/style>/);
  // const styleMatch = vueFileContent.match(/(<style>[\s\S]*?<\/style>)/);
  const template = templateMatch ? templateMatch[1].trim() : '';
  const script = scriptMatch ? scriptMatch[1].trim() : '';
  const style = styleMatch ? styleMatch[1].trim() : '';
  return {
    template,
    script,
    style,
  };
}

/**
 * 检测代码类型。
 * @param code - 待检测的代码字符串。
 * @returns 返回'Vue'、'React'、'HTML'或者不匹配时返回false。
 */
function detectCodeType(code: string) {
  // 检查Vue特有的模式
  if (
    (code.includes('<template>') && code.includes('<script>')) ||
    (code.includes('<template>') && code.includes('<style>'))
  ) {
    return 'Vue';
  }
  // 检查React特有的模式
  if (code.includes('import React') || (code.includes('class') && code.includes('extends Component'))) {
    return 'jsx';
  }
  // 检查HTML特有的模式
  if (code.trim().toLowerCase().startsWith('<!doctype html>') || code.includes('<html>')) {
    return 'HTML';
  } else {
    const languages = [
      { name: 'Java', regex: /class\s+\w+\s*{/ },
      { name: 'CSS', regex: /{[^{}]*}/ },
      { name: 'JavaScript', regex: /function\s+\w+\s*\(/ },
      { name: 'jsx', regex: /import\s+React\s+from\s+'react'/ },
      {
        name: 'tsx',
        regex: /import\s+React\s+from\s+'react'|import\s+\{\s*\w+\s*\}\s+from\s+'react'.*:\s*\w+\s*;/,
      },
      { name: 'TypeScript', regex: /:\s*\w+\s*;/ },
      { name: 'Python', regex: /def\s+\w+\s*\(/ },
      { name: 'C++', regex: /#include\s+<\w+\.h>/ },
      { name: 'SQL', regex: /SELECT\s+\*\s+FROM\s+\w+/i },
      { name: 'Go', regex: /package\s+\w+/ },
      { name: 'PHP', regex: /<\?php/ },
      { name: 'Ruby', regex: /def\s+\w+/ },
      { name: 'C', regex: /#include\s+<\w+\.h>/ },
      { name: 'C#', regex: /namespace\s+\w+|class\s+\w+\s*{|using\s+\w+/ },
    ];
    for (const lang of languages) {
      if (lang.regex.test(code)) {
        return lang.name;
      }
    }
    return 'plaintext';
  }
  // 如果都不匹配，返回其他
  // return false;
}

function matchMarkdownCodeBlocks(text: string): string[] {
  // 正则表达式匹配Markdown代码块内容，排除代码块语言标识
  const regex = /```(?:\w+\n)?([\s\S]*?)```/g;
  let matches: RegExpExecArray | null = null;
  const codeBlocks: string[] = [];
  while ((matches = regex.exec(text)) !== null) {
    // Push the content of the code block, excluding the language identifier
    codeBlocks.push(matches[1].trim());
  }
  return codeBlocks;
}

/**
 * 获取CSS模板的异步函数。
 *
 * @returns {Promise<string>} 返回包含模板字符串的Promise对象。
 */
function getCSSTemplate() {
  const template = `
  你是一个资深研发工程师，我这里有一个很大的设计稿生成的前端代码需要分段优化语义。这段代码渲染效果没有问题，但是写法不符合规范不好维护。我会把 需要优化的代码 和 已优化的代码 分别告诉你，你参考已优化的代码，确保拼接后上下文连贯，代码能够执行。


  ** 具体要求：**
  1. 结合上下文语义，将CSS代码转换为SASS语法的代码，** 修改优化SASS代码并确保代码优化且不重复已存在在已优化的代码中 **，确保SASS代码能够正常执行。
  2. 使用** BEM命名规范** 。
  3. 确保SASS代码的嵌套结构清晰。
  4. 保留原有CSS代码中的所有样式定义。
  5. 优化代码以提高可读性和可维护性。。
  6. 使用SASS的继承功能来减少冗余代码。
  7. 确保代码符合SASS的最佳实践。
  8. 确保代码在不同浏览器和设备上的兼容性。
  9. 确保代码结构和命名方式便于团队协作和后续维护
  10. 如果这些SASS代码已经存在已优化的代码中，** 请不要在输出中重复这些已经存在的代码 **


  ** 历史信息 **
  全部原始代码：
  {allHTML}


  已优化的代码：
  {history}


  ** 本次需要优化的代码： **
  {dom}
  `;
  return template;
}
/**
 * 生成HTML模板字符串
 * @param prompt - 额外的要求
 * @returns HTML模板字符串
 */
function getHtmlTemplate(prompt?: string) {
  const template = `
  你是一个资深研发工程师，我这里有一个很大的设计稿生成的前端代码需要分段优化语义。这段代码渲染效果没有问题，但是写法不符合规范不好维护。我会把 需要优化的代码 和 已优化的代码 分别告诉你，你参考已优化的代码，确保拼接后上下文连贯，代码能够执行。


  ** 具体要求：**
  1. 结合上下文语义，修改优化class名称，要** 符合BEM规范 **，DOM的class名称含义需要和DOM树节点内部内容含义一致，保持css名字和DOM的class名字一致，避免无法匹配，影响样式展示
  2. 需要保持原有样式不变，原有样式是设计稿生成，一般没有太大问题
  3. 优化后代码需要和优化前前后字符一致，可以用程序合并拼接在一起
  4. 仅提供修改后代码，无需额外说明和描述
  5. 请确保保留所有字符、标签和标识符，即使它们未完全闭合。如果这些字符、标签和标识符已经在已优化的代码中出现，请不要在输出中重复这些已经存在的代码
  6. 需要根据当前代码上下文重新优化HTML根节点DIV的CSS类名，子节点的类名应以优化后的根节点类名作为前缀


  ** 其他要求：**
  - ${prompt}


  ** 历史信息 **
  全部原始代码：
  {allHTML}


  已优化的代码：
  {history}


  ** 本次需要优化的代码： **
  {dom}
  `;
  return template;
}
/**
 * 异步处理代码函数
 *
 * @param model - 模型实例
 * @param dom - DOM字符串
 * @param allHTML - 完整HTML字符串
 * @param history - 历史记录字符串
 * @param signal - 中止信号
 * @param language - 语言类型
 * @param prompt - 可选的提示信息
 * @returns 包含内容和语言类型的对象
 */
async function llmHandleCode(
  model: any,
  dom: string,
  allHTML: string,
  history: string,
  signal: AbortSignal,
  language: string,
  prompt?: string
) {
  const template = language === 'css' ? getCSSTemplate() : getHtmlTemplate(prompt);
  const promptTemplate = ChatPromptTemplate.fromMessages([
    ['system', systemMessage],
    ['human', template],
  ]);
  const result: any = await promptTemplate
    .pipe(model)
    .invoke({ dom: dom, allHTML: allHTML, history: history }, { signal });
  if (result && result.content) {
    const matchs = result.content.match(/```(\w+)?\s*([\s\S]*?)\s*```/i);
    const content = matchMarkdownCodeBlocks(result.content).join('');
    return { content, language: matchs[1] };
  } else {
    return { content: '', language: '' };
  }
}

export default async function h5GenAgent(text: string, model: any, option?: Record<string, any>) {
  let files: any[] = [];
  let isAborted = false;
  const splitByLines = (code: string): string[] => {
    const lines = code.split('\n');
    const chunks: string[] = [];
    for (let i = 0; i < lines.length; i += 300) {
      chunks.push(lines.slice(i, i + 300).join('\n'));
    }
    return chunks;
  };
  option?.onProgress?.('思考中，预计需要几分钟..');
  let textArr: any = [];
  const inputLanguage = detectCodeType(text);
  switch (inputLanguage) {
    case 'Vue':
      const vueObj = splitVueComponents(text); // TODO: 每三百行代码做拆分
      const templates = splitByLines(vueObj.template);
      const scripts = splitByLines(vueObj.script);
      const styles = splitByLines(vueObj.style);
      textArr = [...templates, ...scripts, ...styles];
      break;
    case 'tsx':
    case 'jsx':
      const reactCode = splitCodeByLanguage(text);
      const cssCode = splitByLines(reactCode.cssCode);
      const tsCode = splitByLines(reactCode.tsCode);
      textArr = [...tsCode, ...cssCode];
      break;
    default: // TODO: 每三百行代码做拆分
      textArr = splitByLines(text);
      break;
  }
  option?.onProgress?.(`正在创建任务,共创建了${textArr.length}个任务`);
  // 定义一个函数来处理 abort 事件
  const handleAbort = () => {
    option?.onProgress?.(`[DONE]`);
    isAborted = true;
  };
  /**
   * 异步顺序处理文本数组中的每个块。
   * @param textArr - 文本块数组。
   * @param model - 处理文本的模型。
   * @returns 处理后的结果数组。
   */
  async function processChunksSequentially(textArr: any[], model: any) {
    const results: string[] = [];
    const cssCodes: string[] = [];
    const prompt = option?.prompt ?? '';
    const abortController = option?.abortController || new AbortController();
    EventEmitter.defaultMaxListeners = textArr.length * 2 + 15; // 设置全局最大监听器数量
    const { signal } = abortController;
    signal.addEventListener('abort', handleAbort);
    for (let index = 0; index < textArr.length; index++) {
      option?.onProgress?.(`正在处理${inputLanguage}优化任务,处理第${index + 1}/${textArr.length}个任务`);
      try {
        if (textArr[index].includes('<script') && inputLanguage === 'Vue') {
          results.push(textArr[index]);
          continue;
        }
        const tempChunk = await llmHandleCode(
          model,
          textArr[index],
          textArr.join(''),
          results.join(''),
          signal,
          inputLanguage,
          prompt
        );
        const styles = ['css', 'scss', 'sass', 'less'];
        const isStyle = tempChunk.language && styles.includes(tempChunk.language);
        const content = tempChunk.content;
        if (isStyle) {
          const cssStr = content.replace(/<style\b[^>]*>/, '').replace('</style>', '');
          cssCodes.push(cssStr);
        } else {
          results.push(content);
        }
      } catch (error) {
        if (signal.aborted) {
          isAborted = signal.aborted;
          break;
        }
      }
    }
    const generatorCode: string[] = [];
    for (let index = 0; index < cssCodes.length; index++) {
      option?.onProgress?.(`正在处理CSS优化任务,处理第${index + 1}/${cssCodes.length}个任务`);
      const cssChunk = await llmHandleCode(
        model,
        cssCodes[index],
        cssCodes.join(''),
        generatorCode.join(''),
        signal,
        'css',
        prompt
      );
      generatorCode.push(cssChunk.content);
    }
    const cssCode = generatorCode.map((css, i) => {
      return i === 0 && inputLanguage === 'Vue'
        ? `<style>\n${css}`
        : i === cssCodes.length - 1 && inputLanguage === 'Vue'
        ? `${css}\n</style>`
        : css;
    });
    const fileName = getFirstClassName(generatorCode.join(''));
    const filesInput =
      inputLanguage === 'Vue'
        ? [
            {
              fileName,
              fileContent: results.concat(cssCode).join(''),
              fileType: inputLanguage,
            },
          ]
        : [
            {
              fileName,
              fileContent: generatorCode.join(''),
              fileType: 'scss',
            },
            {
              fileName,
              fileContent: results.join(''),
              fileType: inputLanguage,
            },
          ];

    files = await option?.generateFiles?.(filesInput);
    return results.concat(cssCode);
  }
  // 使用新的按顺序处理的函数
  let optimizedChunks: string[] = [];
  try {
    optimizedChunks = await processChunksSequentially(textArr, model);
  } catch (error) {
    Logger.error('%c [ error ]-315', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    option?.onProgress?.(`[DONE]`);
    return '';
  }
  if (isAborted) {
    return '';
  }
  option?.onProgress?.(`任务处理完成，正在拼接代码`);
  const codeStr = optimizedChunks.join('\n');
  const language = detectCodeType(codeStr).toLowerCase() === 'vue' ? 'html' : detectCodeType(codeStr).toLowerCase();
  option?.onProgress?.(`代码拼接完成，任务结束`);
  const filesStr = files
    .map((file) => `<p title="${file.filePath}" class="joycoder-h5-gen-agent">${file.relativePath}</p>`)
    .join('');
  const result = `\`\`\`${language} \n ${codeStr} \n\`\`\`\n生成的代码已经保存在${filesStr}`;
  option?.onProgress?.(`[DONE]`);
  return result;
}

function getFirstClassName(cssString: string): string | null {
  const classNameRegex = /\.([a-zA-Z0-9_-]+)/;
  const match = cssString.match(classNameRegex);
  return match ? match[1] : null;
}
