//vscode入口
import { ChatOpenAI } from '@langchain/openai';
import { type ClientOptions } from 'openai';
import { type BaseChatModelParams } from '@langchain/core/language_models/chat_models';
import { OpenAIChatInput } from '@langchain/openai';
import { forceJdhLogin, getJdhLoginInfo, hasJdhLoginCookie, isIDE } from '@joycoder/shared';
import { type BaseMessage } from '@langchain/core/messages';
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager';
import { type ChatResult } from '@langchain/core/outputs';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { DevConsole } from '../utils';
import chatAgent from '../chatAgent';
// import to from 'await-to-js';

//JoyCoder服务端
const BASE_URL = 'http://chatgpt-relay.jd.com/v1/';

/**
 * https://joyspace.jd.com/pages/x5Z1ozZIzmjXMZWwh3v0
 */
export interface IExtParams {
  bizId: string;
  bizToken: string;
  ssoClientId: string;
  jdhLoginParams: {
    userToken?: string;
    userName?: string;
    erp?: string;
    sourceType: string;
  };
}

/**
 * 获取OpenAI定制接口的扩展参数
 * @returns IExtParams
 */
export function getExtParams(): IExtParams {
  return {
    bizId: 'joycoderfe',
    bizToken: 'd99fa9a5-3655-4251-8141-e0f84dccb099',
    ssoClientId: 'SSO1.0',
    jdhLoginParams: { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' },
  };
}

export function getExtHeaders() {
  const baseHeaders = {
    'Content-Type': 'application/json; charset=UTF-8',
  };
  const headers =
    isIDE() && getJdhLoginInfo()?.ptKey
      ? {
          ...baseHeaders,
          ptKey: getJdhLoginInfo()?.ptKey,
        }
      : baseHeaders;
  return {
    headers: headers,
  };
}

export type IChatModelOptions = Partial<OpenAIChatInput> &
  BaseChatModelParams & {
    configuration?: ClientOptions;
  };

/**
 * 检查用户登录状态，如未登录则强制登录
 * @returns Promise<void>
 */
export async function checkLogin() {
  const isLogin = !!hasJdhLoginCookie();
  if (!isLogin) {
    await forceJdhLogin();
  }
}

export class ChatJoyCoder extends ChatOpenAI {
  override _llmType(): string {
    return 'joycoder';
  }
  override modelKwargs: OpenAIChatInput['modelKwargs'];

  /**
   * 构造函数，初始化Chat模型配置。
   * @param fields 可选参数，包含模型配置和其他选项。
   * - 如果提供`fields`，将使用其值初始化新字段。
   * - 如果未提供`fields`，将使用默认值。
   * - `openAIApiKey`被设置为非空字符串以绕过内部校验。
   * - `streaming`默认为`true`，表示流式传输。
   * - `modelName`根据提供的名称使用相应的模型配置。
   * - `modelKwargs`中的`extParams`包含透传给接口的扩展参数。
   * - 如果模型配置中包含`chatApiUrl`，则会设置到`extParams.apiUrl`中。
   * - `configuration`中的`baseURL`被设置为服务的基础URL。
   * - `maxRetries`默认为0，禁用默认的重试次数。
   */
  constructor(fields?: IChatModelOptions) {
    const newFields = fields ? { ...fields } : {};

    // 绕过ChatOpenAI内部校验
    newFields.openAIApiKey = 'not-empty';

    // 默认为流式
    newFields.streaming = newFields.streaming ?? true;

    // 使用设置的模型
    const modelConfig = getChatModelAndConfig(newFields.modelName);
    DevConsole.log('modelConfig', modelConfig);
    newFields.modelName = modelConfig?.chatApiModel;

    //temperature获取配置
    newFields.temperature = modelConfig?.temperature === undefined ? 0.01 : modelConfig?.temperature;
    // 要透传给接口的扩展参数放在modelKwargs中
    newFields.modelKwargs = {
      ...newFields.modelKwargs,
      extParams: getExtParams(),
    };

    if (modelConfig.chatApiUrl) newFields.modelKwargs.extParams.apiUrl = modelConfig.chatApiUrl;

    // 设置服务URL
    newFields.configuration = {
      ...newFields.configuration,
      baseURL: BASE_URL,
    };

    // 禁用默认的重试次数，默认6次
    newFields.maxRetries = newFields.maxRetries ?? 0;
    //@ts-ignore
    super(newFields);

    this.modelKwargs = newFields.modelKwargs;
  }

  //@ts-ignore
  async _generate(
    messages: BaseMessage[],
    options: this['ParsedCallOptions'],
    runManager?: CallbackManagerForLLMRun
  ): Promise<ChatResult> {
    // 校验登录态，引导登录，更新登录参数
    const isLogin = !!hasJdhLoginCookie();
    if (!isLogin) {
      await forceJdhLogin();
      // @ts-ignore
      this.modelKwargs.extParams.jdhLoginParams = { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' };
    }
    // DevConsole.log('--messages--', messages);
    //@ts-ignore
    return await super._generate(messages, options, runManager);
  }
}

// const task = '小米SU7汽车不同车型售价是多少？';
export default function chatAgentVscode(
  task: string,
  options: Record<string, any>,
  onProgress?: (msg: string) => void
) {
  //https://js.langchain.com/v0.2/docs/integrations/chat/openai/
  const model: any = new ChatJoyCoder({
    temperature: 0.1,
    apiKey: 'empty',
    configuration: {
      baseURL: BASE_URL,
    },
  });

  return chatAgent(task, model, options, onProgress);
}
