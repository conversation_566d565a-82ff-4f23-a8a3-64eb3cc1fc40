import { workspace, ConfigurationChangeEvent, commands } from 'vscode';
import * as fse from 'fs-extra';
import * as path from 'path';
import * as walk from 'walkdir';
import { to } from 'await-to-js';
import * as jsonfile from 'jsonfile';
import { renameRepeat<PERSON>ey } from './tools/mergeTools';
import { Logger, getProjecctFramework, getRemoteConfigSync, getVscodeConfig } from '@joycoder/shared';

export default function () {
  const framework = getProjecctFramework();
  const wholeJsSnippets = {};
  const wholeCSSSnippets = {};
  const wholeHtmlSnippets = {};
  const wholePythonSnippets = {};
  const wholeGoSnippets = {};
  const wholeJavaSnippets = {};
  const wholeCppSnippets = {};

  const snippetTypes = [
    {
      src: path.join(__dirname, '../snippets/src/html'),
      dist: path.join(__dirname, '../snippets/snippets.html.json'),
      data: wholeHtmlSnippets,
      scope: 'html',
    },
    {
      src: path.join(__dirname, '../snippets/src/css'),
      dist: path.join(__dirname, '../snippets/snippets.css.json'),
      data: wholeCSSSnippets,
      scope: 'css,scss,less',
    },
    {
      src: path.join(__dirname, '../snippets/src/python'),
      dist: path.join(__dirname, '../snippets/snippets.python.json'),
      data: wholePythonSnippets,
      scope: 'python',
    },
    {
      src: path.join(__dirname, '../snippets/src/go'),
      dist: path.join(__dirname, '../snippets/snippets.go.json'),
      data: wholeGoSnippets,
      scope: 'go',
    },
    {
      src: path.join(__dirname, '../snippets/src/java'),
      dist: path.join(__dirname, '../snippets/snippets.java.json'),
      data: wholeJavaSnippets,
      scope: 'java',
    },
    {
      src: path.join(__dirname, '../snippets/src/cpp'),
      dist: path.join(__dirname, '../snippets/snippets.cpp.json'),
      data: wholeCppSnippets,
      scope: 'cpp',
    },
  ];

  // 根据当前项目的技术栈设置不同的snippets，避免一些无用的提示
  const reactSnippets = {
    src: path.join(__dirname, '../snippets/src/js/react'),
    dist: path.join(__dirname, '../snippets/snippets.js.json'),
    data: wholeJsSnippets,
    scope: 'javascript,typescript,javascriptreact,typescriptreact',
  };
  if (framework == 'taro') {
    snippetTypes.push(reactSnippets);
    snippetTypes.push({
      src: path.join(__dirname, '../snippets/src/js/taro'),
      dist: path.join(__dirname, '../snippets/snippets.js.json'),
      data: wholeJsSnippets,
      scope: 'javascript,typescript,javascriptreact,typescriptreact,vue',
    });
  } else if (framework == 'react') {
    snippetTypes.push(reactSnippets);
  } else if (framework == 'vue') {
    snippetTypes.push({
      src: path.join(__dirname, '../snippets/src/js/vue'),
      dist: path.join(__dirname, '../snippets/snippets.js.json'),
      data: wholeJsSnippets,
      scope: 'javascript,typescript,vue',
    });
  }

  const writeSnippets = () => {
    snippetTypes.map((item) => {
      fse.ensureFileSync(item.dist);
      jsonfile.writeFileSync(item.dist, item.data, { spaces: 2 });
    });
  };

  const snippetSwitch = getVscodeConfig('JoyCode.config.snippets.switch');
  if (!snippetSwitch) {
    writeSnippets();
    return;
  }

  // 1. 远程配置的工作区级别的snippets
  const snippetsConfigList = getRemoteConfigSync()?.snippetsConfig || [];
  const wholeSnippets = snippetsConfigList.reduce((result: any, item: any) => {
    try {
      const { snippets } = item;
      const snippetsObj = renameRepeatKey(JSON.parse(snippets), result);
      return Object.assign(result, snippetsObj);
    } catch (error) {
      console.log('remote snippets parse error:', error);
      return result;
    }
  }, {});

  // 2. 本地配置级别的snippets
  try {
    const personalSnippets = getVscodeConfig('JoyCode.config.personalSnippets');
    const personalSnippetsObj = renameRepeatKey(JSON.parse(personalSnippets), wholeSnippets);
    Object.assign(wholeSnippets, personalSnippetsObj);
  } catch (error) {
    console.log('config snippets parse error:', error);
  }

  // 3. 本地文件级别的snippets
  try {
    // 遍历目录读取json，修改scope，合并到wholeSnippets
    snippetTypes.map((item) => {
      const paths = walk.sync(item.src);
      paths.map((pathItem) => {
        if (path.extname(pathItem) !== '.json') return;
        const localFileSnippetsObj = jsonfile.readFileSync(pathItem);
        Object.keys(localFileSnippetsObj).map((snippet) => {
          localFileSnippetsObj[snippet].scope = item.scope;
        });
        Object.assign(wholeSnippets, renameRepeatKey(localFileSnippetsObj, wholeSnippets));
      });
    });
  } catch (error) {
    console.log('local file snippets parse error:', error);
  }

  // 4.1. snippet名称后追加 | JoyCode
  // 4.2. 未注明scope的snippet默认只针对几种js文件类型
  Object.keys(wholeSnippets).map((item) => {
    wholeSnippets[item].scope =
      wholeSnippets[item].scope || 'javascript,typescript,javascriptreact,typescriptreact,vue';

    // scope属性优先级低于package.json中的声明，所以需要从文件上区分，避免影响scss、css、html
    if (/\bscss\b|\bcss\b|\bless\b/.test(wholeSnippets[item].scope)) {
      wholeCSSSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else if (/\bhtml\b/.test(wholeSnippets[item].scope)) {
      wholeHtmlSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else if (/\bpython\b/.test(wholeSnippets[item].scope)) {
      wholePythonSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else if (/\bjava\b/.test(wholeSnippets[item].scope)) {
      wholeJavaSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else if (/\bgo\b/.test(wholeSnippets[item].scope)) {
      wholeGoSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else if (/\bcpp\b/.test(wholeSnippets[item].scope)) {
      wholeCppSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    } else {
      wholeJsSnippets[`${item} | JoyCode`] = wholeSnippets[item];
    }
  });

  // 5. 写入本地snippets文件
  writeSnippets();
}

// TODO snippet暂无数据上报，原因是未找到snippet被插入时的钩子

// snippet开关变更后需重载才能生效
workspace.onDidChangeConfiguration(async ({ affectsConfiguration }: ConfigurationChangeEvent) => {
  if (
    affectsConfiguration('JoyCode.config.snippets.switch') ||
    affectsConfiguration('JoyCode.config.personalSnippets')
  ) {
    const [, result] = await to(
      Logger.showWarningMessage('JoyCode: 请重载VSCode以使代码片段设置生效', '重载以生效', '暂时忽略')
    );
    if (result === '重载以生效') {
      commands.executeCommand('workbench.action.reloadWindow');
    }
  }
});
