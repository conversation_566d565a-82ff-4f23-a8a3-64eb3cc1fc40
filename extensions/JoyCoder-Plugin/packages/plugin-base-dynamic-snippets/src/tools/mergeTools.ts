/**
 * 移除重复的 key 并生成新 key
 */
export function renameRepeatKey(source: Record<string, any>, targetObj: Record<string, any>) {
  // 合法的唯一 key 数组
  const validKeys = Object.keys(targetObj);
  // 遍历目标对象的所有 key 依次进行校验
  Object.keys(source).forEach((key) => {
    // 如果未重复则追加到合法数组中
    if (!validKeys.includes(key)) {
      validKeys.push(key);
      return;
    }
    // 生成新 key 的后缀
    let index = 1;
    for (;;) {
      if (!validKeys.includes(`${key} ${index}`)) {
        break;
      }
      index += 1;
    }
    // 注册新 key 的数据
    source[`${key} ${index}`] = source[key];
    // 删除旧 key 的数据
    delete source[key];
  });
  // 返回最终结果
  return source;
}
