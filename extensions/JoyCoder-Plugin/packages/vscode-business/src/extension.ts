import * as vscode from 'vscode';
import { Logger, registerRouter, GlobalState, getRemoteConfigAsync, initLogOutCommand } from '@joycoder/shared';
import initBaseCodeCompletion from '@joycoder/plugin-base-code-completion';
import initBaseTreeView from '@joycoder/plugin-base-tree-view';
import initBaseCodeReview from '@joycoder/plugin-base-code-review';
import initBaseAI from '@joycoder/plugin-base-ai';
import to from 'await-to-js';
import { serializeError } from 'serialize-error';
import { initCheckUpdateCommand } from '@joycoder/version';

export async function activate(context: vscode.ExtensionContext) {
  // 注册监听路由事件
  registerRouter();

  // 全局登录
  initLogOutCommand(context);

  // 定义输出渠道，方便在vscode-输出中查看日志
  Logger.channel = vscode.window.createOutputChannel('JoyCode', { log: true });
  context.subscriptions.push(Logger.channel);

  // 请求工作区远程配置（模拟拉取）
  const [err] = await to(getRemoteConfigAsync());
  if (err) {
    Logger.showErrorMessage(`配置拉取失败，部分功能不可用，请稍后重新加载窗口~${JSON.stringify(serializeError(err))}`);
  }

  // 初始化使用globalState需要的context
  GlobalState.init(context);
  // 代码预测补全
  initBaseCodeCompletion(context);
  // 左侧webview面板
  initBaseTreeView(context);
  // 右键AI菜单注册
  initBaseAI(context);
  // 代码评审
  initBaseCodeReview(context);
  // 检查更新注册
  initCheckUpdateCommand(context);

  // Logger.log('JoyCoder插件激活成功啦~');
}

export function deactivate() {}
