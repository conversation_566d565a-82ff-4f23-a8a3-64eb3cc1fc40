const { merge } = require('webpack-merge');
const parentConfig = require('../../webpack.config');
const { DefinePlugin } = require('webpack');

/** @type WebpackConfig */
let extensionConfig = {
  plugins: [
    new DefinePlugin({
      'process.env': {
        PLUGIN_VER: JSON.stringify(process.env.PLUGIN_VER),
        BUSINESS_ENV: JSON.stringify(process.env.BUSINESS_ENV),
      },
    }),
  ],
};

module.exports = merge(parentConfig, extensionConfig);
