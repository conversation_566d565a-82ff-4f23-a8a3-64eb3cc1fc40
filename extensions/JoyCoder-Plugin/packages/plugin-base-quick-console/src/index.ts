import * as vscode from 'vscode';
import { FE_FILE_TYPES, Logger, getCurrentFileType, getSelectedText, reportRd } from '@joycoder/shared';

export default function (context: vscode.ExtensionContext) {
  const disposable = vscode.commands.registerCommand('JoyCode.quickConsole', function () {
    if (!FE_FILE_TYPES.includes(getCurrentFileType())) {
      return Logger.showInformationMessage('抱歉，快捷Console功能暂不支持当前语言~');
    }

    const activeTextEditor = vscode.window.activeTextEditor;
    if (!activeTextEditor) return;

    const curText = getSelectedText();
    const start = activeTextEditor.selection.start;

    const activeDocument = activeTextEditor.document;
    const curLineText = activeDocument.lineAt(start.line).text;
    const curLineStartCharacter = curLineText.search(/\S/i);
    let curBlankText = curLineText.substring(0, curLineStartCharacter);
    if (curLineText.endsWith('{')) curBlankText += '  ';

    const insertPositon = new vscode.Position(start.line + 1, 0);
    activeTextEditor.edit((TextEditorEdit) => {
      TextEditorEdit.insert(insertPositon, `${curBlankText}console.log('joycoder|${curText}:', ${curText});\n`);
      reportRd(20);
    });
  });

  context.subscriptions.push(disposable);
}
