import * as vscode from 'vscode';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { downloadFile } from '@joycoder/shared';
import { JoyCoderExtension } from '@joycoder/web';

/**
 * 判断插件是否安装
 *
 * @export
 * @param {string} name
 * @return {*}
 */
export function getAllVSCodeExtensionInstalled() {
  const allExtensions = vscode.extensions.all.filter((item) => {
    if (/(joycoder|jd)/i.test(item.id)) {
      console.log('[已安装]:', item.id);
    }
    return item.packageJSON.isBuiltin === false;
  });
  return allExtensions;
}

/**
 * 判断插件是否安装
 *
 * @export
 * @param {string} name
 * @return {*}
 */
export function isVSCodeExtensionInstalled(name: string) {
  return !!vscode.extensions.getExtension(name);
}

/**
 * 读取插件Package.json
 *
 * @export
 * @param {string} name
 * @return {*}
 */
export function getVSCodeExtensionPkgJSON(name: string) {
  const packageJSON: Record<string, any> = vscode.extensions.getExtension(name)?.packageJSON || {};
  return packageJSON;
}

/**
 * 卸载插件
 *
 * @export
 * @param {JoyCoderExtension} extension
 * @return {*}
 */
export async function uninstallExtension(extension: JoyCoderExtension) {
  try {
    // 等待卸载命令或者10s超时
    const timeout = await Promise.race([
      vscode.commands.executeCommand('workbench.extensions.uninstallExtension', extension.extensionIndentify),
      new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 10000);
      }),
    ]);
    return timeout;
  } catch (error) {
    console.error(error);
    return error;
  }
}

const CacheDir = path.resolve(os.homedir(), '.update-vscode-extension');

/**
 * 安装插件
 *
 * @export
 * @param {JoyCoderExtension} extension
 * @return {*}
 */
export async function installExtension(extension: JoyCoderExtension) {
  try {
    let uri: string | vscode.Uri = extension.extensionIndentify;
    if (extension.downloadUrl) {
      fs.ensureDirSync(CacheDir);
      const fileName = extension.downloadUrl.split('/').pop() || 'joycoder.vsix';
      await downloadFile(extension.downloadUrl, CacheDir, fileName);
      const localVSIXPath = path.resolve(CacheDir, fileName);
      uri = vscode.Uri.file(localVSIXPath);
    }
    // 等待安装命令或者10s超时
    const now = Date.now();
    const timeout = await Promise.race([
      vscode.commands.executeCommand('workbench.extensions.installExtension', uri),
      new Promise((resolve) => {
        setTimeout(() => {
          resolve(true);
        }, 10000);
      }),
    ]);
    console.log('[安装耗时]:', Date.now() - now);
    return timeout;
  } catch (error) {
    console.error(error);
    return error;
  }
}
