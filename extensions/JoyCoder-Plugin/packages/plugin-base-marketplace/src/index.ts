import { getRemoteConfigSync, openInVscode, isFirstUse, hasJdhLoginCookie } from '@joycoder/shared';
import { ViewLoader, THEME_HTML, MARKDOWN_HTML, MarketplaceEvent, JoyCoderExtension } from '@joycoder/web';
import * as vscode from 'vscode';
import {
  getAllVSCodeExtensionInstalled,
  installExtension,
  isVSCodeExtensionInstalled,
  uninstallExtension,
} from './utils';

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.marketplace.show', () => handleMarketplaceShow())
  );
  // 隐藏启动欢迎页[总开关]
  const hideStartup = getRemoteConfigSync().hideStartup;
  // 已登录用户不展示启动欢迎页[开关]
  const loginNotShowStartup = getRemoteConfigSync().loginNotShowStartup;
  // 是否展示启动欢迎页面
  if (!hideStartup && isFirstUse() && (!loginNotShowStartup || !hasJdhLoginCookie()))
    handleMarketplaceShow({ isStartup: true });
}

let marketplaceWebview: ViewLoader | null = null;

/**
 * 打开插件市场Webview
 *
 */
function handleMarketplaceShow(options?: { isStartup: boolean }) {
  if (marketplaceWebview) updateInstalledExtension();
  marketplaceWebview = openInVscode({
    pageName: 'marketplace',
    pageTitle: 'JoyCoder插件市场',
    viewColumn: vscode.ViewColumn.One,
    // css文件文件走cdn加载，减少插件大小
    htmlPrepend: `${THEME_HTML}${MARKDOWN_HTML}${
      options?.isStartup ? '<script>window.joyCoderStartup = true;</script>' : ''
    }`,
    async onDidReceiveMessage({ type, data }) {
      switch (type) {
        case MarketplaceEvent.WebviewLoaded:
          updateInstalledExtension();
          break;
        case MarketplaceEvent.Install:
          const extension = data as JoyCoderExtension;
          if (isVSCodeExtensionInstalled(extension.extensionIndentify)) {
            return updateInstallError(new Error('插件状态异常'));
          }
          const listener = vscode.extensions.onDidChange(() => {
            updateInstalledExtension();
            listener.dispose();
          });
          const error = await installExtension(extension);
          if (error) updateInstallError(error);
          break;
        case MarketplaceEvent.InstallRecommend:
          const extensionList = (data as JoyCoderExtension[]).filter(
            (extension) => !isVSCodeExtensionInstalled(extension.extensionIndentify)
          );
          let leftInstallCount = extensionList.length;
          if (leftInstallCount > 0) {
            const listener = vscode.extensions.onDidChange(() => {
              updateInstalledExtension();
              leftInstallCount--;
              leftInstallCount == 0 && listener.dispose();
            });
            extensionList.forEach(async (extension) => {
              installExtension(extension);
            });
          }
          break;
        case MarketplaceEvent.Uninstall:
          const unExtension = data as JoyCoderExtension;
          if (!isVSCodeExtensionInstalled(unExtension.extensionIndentify)) {
            return updateInstallError(new Error('插件状态异常'));
          }
          const unError = await uninstallExtension(unExtension);
          if (unError) {
            return updateInstallError(unError);
          }
          const choose = await vscode.window.showInformationMessage(
            `已卸载${unExtension.displayName} 重启VScode生效`,
            { modal: true },
            '重启'
          );
          if (choose === '重启') {
            vscode.commands.executeCommand('workbench.action.reloadWindow');
          } else {
            updateInstalledExtension();
          }
          break;
        case MarketplaceEvent.Search:
          const disableExtension = data as JoyCoderExtension;
          vscode.commands.executeCommand(
            'workbench.extensions.search',
            (disableExtension && disableExtension.displayName) || 'joycoder'
          );
          break;
        case MarketplaceEvent.Close:
          marketplaceWebview?.dispose();
          break;
        default:
          break;
      }
    },
    onDidDispose() {
      marketplaceWebview = null;
    },
  });
}

const updateInstalledExtension = () => {
  const extensions = getAllVSCodeExtensionInstalled();
  marketplaceWebview?.postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: MarketplaceEvent.InstalledUpdate,
      data: extensions,
    },
  });
};

const updateInstallError = (error: Error) => {
  const extensions = getAllVSCodeExtensionInstalled();
  marketplaceWebview?.postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: MarketplaceEvent.InstallError,
      data: extensions,
    },
  });
};
