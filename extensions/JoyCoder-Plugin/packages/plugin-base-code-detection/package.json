{"name": "@joycoder/plugin-base-code-detection", "version": "3.1.2", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "license": "MIT", "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "scripts": {}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@joycoder/shared": "workspace:*", "ts-unused-exports": "^9.0.4"}}