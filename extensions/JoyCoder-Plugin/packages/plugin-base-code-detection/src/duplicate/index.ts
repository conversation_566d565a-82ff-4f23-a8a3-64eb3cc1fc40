import * as vscode from 'vscode';
import { IClone } from '@jscpd/core';
import { detectClones } from 'jscpd';
import { to } from 'await-to-js';
import { getVscodeConfig, Logger, reportRd, sleep } from '@joycoder/shared';
import { cloneType } from './definition';

let workspaceRootPath = '';

function detection(file: vscode.Uri[]): void {
  if (file.length === 0) {
    Logger.showInformationMessage('JoyCode: 请先在资源管理器中选中文件或文件夹');
    return;
  }
  const paths = file.map((item: vscode.Uri) => item.fsPath.replace(/\\/g, '/'));
  workspaceRootPath = vscode.workspace.workspaceFolders?.find((workspaceFolder) =>
    paths[0].includes(workspaceFolder.uri.fsPath)
  )?.uri.fsPath as string;

  const options = getVscodeConfig('JoyCode.config.duplicateCodeDetection', {});

  vscode.window.withProgress(
    {
      cancellable: false,
      title: 'JoyCode: 代码重复检测正在进行中...',
      location: vscode.ProgressLocation.Notification,
    },
    async () => {
      // 延时500毫秒 避免进度提示被代码查重逻辑阻塞
      await sleep(500);

      // jscpd检测重复代码;
      const [err, clones] = await to(
        detectClones({
          path: paths,
          silent: true,
          ...options,
        })
      );
      if (err) {
        Logger.showInformationMessage('JoyCode: 代码检测失败，请重试');
        return Promise.reject();
      }

      codeDiagnose(clones || []);
      return Promise.resolve();
    }
  );
}

const collection = vscode.languages.createDiagnosticCollection('JoyCode');
async function codeDiagnose(clones: IClone[]): Promise<void> {
  reportRd(15);
  if (clones.length == 0) {
    Logger.showInformationMessage('JoyCode: 未检测到重复代码');
    return;
  }
  // clear the previous problems first
  collection.clear();

  const clonesMap: cloneType = {};
  clones.map((item: IClone) => {
    const {
      duplicationA: { sourceId: path },
    } = item;

    if (clonesMap[path]) {
      const group = clonesMap[path];
      group.push(item);
    } else {
      clonesMap[path] = [item];
    }
  });

  for (const path in clonesMap) {
    const uri = vscode.Uri.file(path);
    const group = clonesMap[path];
    const diagnostics = group.map((item: IClone) => {
      const { duplicationA, duplicationB } = item;
      const { sourceId: sourceIdA, start: startA, end: endA } = duplicationA;
      const { sourceId: sourceIdB, start: startB, end: endB } = duplicationB;

      const uriA = vscode.Uri.file(sourceIdA);
      const uriB = vscode.Uri.file(sourceIdB);

      const { line: startLineA } = startA;
      const { line: endLineA } = endA;
      const { line: startLineB } = startB;
      const { line: endLineB } = endB;

      // jscpd返回的duplicationA.column与vscode的column定义不一致 无法准确定位到具体的“列”
      // 故展示重复代码时，范围取起始行的下一行到终止行的上一行
      const rangeA = new vscode.Range(startLineA, 0, endLineA - 2, 0);
      const rangeB = new vscode.Range(startLineB, 0, endLineB - 2, 0);

      return {
        range: rangeA,
        message: `JoyCode:检测到重复代码 - ${endLineA - startLineA - 2} 行`,
        severity: vscode.DiagnosticSeverity.Information,
        relatedInformation: [
          {
            location: {
              uri: uriA,
              range: rangeA,
            },
            message: sourceIdA.replace(workspaceRootPath + '/', ''),
          },
          {
            location: {
              uri: uriB,
              range: rangeB,
            },
            message: sourceIdB.replace(workspaceRootPath + '/', ''),
          },
        ],
      };
    });
    collection.set(uri, diagnostics);
  }

  Logger.showInformationMessage(`JoyCode: 检测发现${clones.length}处重复代码，点击查看详细内容`, '查看').then(
    (result: string) => {
      if (result === '查看') {
        vscode.commands.executeCommand('workbench.actions.view.problems');
      }
    }
  );
}

export default vscode.commands.registerCommand('JoyCode.duplicateCodeDetection', async (...file) => {
  detection(file[1] || []);
});
