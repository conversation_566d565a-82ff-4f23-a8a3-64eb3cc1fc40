import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import * as fs from 'fs';
import { resolve, dirname, join, extname } from 'path';
import chalk from 'chalk';
import * as postcssLess from 'postcss-less';
import * as postcssScss from 'postcss-scss';
import * as vueCompiler from 'vue-template-compiler';

const JS_EXTS = ['.js', '.jsx', '.ts', '.tsx', '.d.ts'];
const CSS_EXTS = ['.css', '.less', '.scss'];
const JSON_EXTS = ['.json'];
const VUE_EXTS = ['.vue'];

let requirePathResolver: any = null;

const MODULE_TYPES = {
  JS: 0,
  CSS: 1,
  JSON: 2,
  VUE: 3,
};

function isDirectory(filePath) {
  try {
    return fs.statSync(filePath).isDirectory();
  } catch (e) {}
  return false;
}

const visitedModules = new Set();

function moduleResolver(curModulePath, requirePath) {
  if (typeof requirePathResolver === 'function') {
    const res = requirePathResolver(dirname(curModulePath), requirePath);
    if (typeof res === 'string') {
      requirePath = res;
    }
  }

  requirePath = resolve(dirname(curModulePath), requirePath);

  // 过滤掉第三方模块
  if (requirePath.includes('node_modules')) {
    return '';
  }

  requirePath = completeModulePath(requirePath);

  if (visitedModules.has(requirePath)) {
    return '';
  } else {
    visitedModules.add(requirePath);
  }
  return requirePath;
}

function completeModulePath(modulePath) {
  const EXTS = [...JSON_EXTS, ...JS_EXTS];
  if (modulePath.match(/\.[a-zA-Z]+$/)) {
    return modulePath;
  }

  function tryCompletePath(resolvePath) {
    for (let i = 0; i < EXTS.length; i++) {
      const tryPath = resolvePath(EXTS[i]);
      if (fs.existsSync(tryPath)) {
        return tryPath;
      }
    }
  }

  function reportModuleNotFoundError(modulePath) {
    throw chalk.red('module not found: ' + modulePath);
  }

  if (isDirectory(modulePath)) {
    const tryModulePath = tryCompletePath((ext) => join(modulePath, 'index' + ext));
    if (!tryModulePath) {
      reportModuleNotFoundError(modulePath);
    } else {
      return tryModulePath;
    }
  } else if (!EXTS.some((ext) => modulePath.endsWith(ext))) {
    const tryModulePath = tryCompletePath((ext) => modulePath + ext);
    if (!tryModulePath) {
      reportModuleNotFoundError(modulePath);
    } else {
      return tryModulePath;
    }
  }
  return modulePath;
}

function resolveBabelSyntaxtPlugins(modulePath) {
  const plugins: any[] = [];
  if (['.tsx', '.jsx', '.js'].some((ext) => modulePath.endsWith(ext))) {
    plugins.push('jsx');
  }
  if (['.ts', '.tsx'].some((ext) => modulePath.endsWith(ext))) {
    plugins.push('typescript');
  }
  return plugins;
}

function resolvePostcssSyntaxtPlugin(modulePath) {
  if (modulePath.endsWith('.less')) {
    return postcssLess;
  } else {
    return postcssScss;
  }
}

function getModuleType(modulePath) {
  const moduleExt = extname(modulePath);
  if (JS_EXTS.some((ext) => ext === moduleExt)) {
    return MODULE_TYPES.JS;
  } else if (CSS_EXTS.some((ext) => ext === moduleExt)) {
    return MODULE_TYPES.CSS;
  } else if (JSON_EXTS.some((ext) => ext === moduleExt)) {
    return MODULE_TYPES.JSON;
  } else if (VUE_EXTS.some((ext) => ext === moduleExt)) {
    return MODULE_TYPES.VUE;
  }
}

function traverseCssModule(curModulePath, callback) {
  const moduleFileConent = fs.readFileSync(curModulePath, {
    encoding: 'utf-8',
  });

  const ast = resolvePostcssSyntaxtPlugin(curModulePath).parse(moduleFileConent);
  ast.walkAtRules('import', (rule) => {
    const subModulePath = moduleResolver(curModulePath, rule.params.replace(/['"]/g, ''));
    if (!subModulePath) {
      return;
    }
    callback && callback(subModulePath);
    traverseModule(subModulePath, callback);
  });
  ast.walkDecls((decl) => {
    if (decl.value.includes('url(')) {
      const url = /.*url\((.+)\).*/.exec(decl.value)?.[1].replace(/['"]/g, '');
      const subModulePath = moduleResolver(curModulePath, url);
      if (!subModulePath) {
        return;
      }
      callback && callback(subModulePath);
    }
  });
}

function traverseJsModule(curModulePath, callback, fileContent?) {
  const moduleFileContent =
    fileContent ||
    fs.readFileSync(curModulePath, {
      encoding: 'utf-8',
    });

  const ast = parser.parse(moduleFileContent, {
    sourceType: 'unambiguous',
    plugins: resolveBabelSyntaxtPlugins(curModulePath),
  });

  const traverseCb = (path) => {
    const { node } = path;
    const moduleName = node.source?.value;
    if (!moduleName) return;
    const subModulePath = moduleResolver(curModulePath, moduleName);
    if (!subModulePath) return;
    callback && callback(subModulePath);
    traverseModule(subModulePath, callback);
  };

  traverse(ast, {
    ImportDeclaration: traverseCb,
    ExportAllDeclaration: traverseCb,
    ExportNamedDeclaration: traverseCb,
    ExportDefaultDeclaration: traverseCb,
    CallExpression(path) {
      if (path.get('callee').toString() === 'require') {
        const subModulePath = moduleResolver(curModulePath, path.get('arguments.0').toString().replace(/['"]/g, ''));
        if (!subModulePath) {
          return;
        }
        callback && callback(subModulePath);
        traverseModule(subModulePath, callback);
      }
    },
  });
}

function traverseVueModule(curModulePath, callback) {
  const moduleFileContent = fs.readFileSync(curModulePath, {
    encoding: 'utf-8',
  });

  const vueCom = vueCompiler.parseComponent(moduleFileContent);

  traverseJsModule(curModulePath, callback, vueCom?.script?.content);
}

function traverseModule(curModulePath, callback) {
  curModulePath = completeModulePath(curModulePath);

  const moduleType = getModuleType(curModulePath);

  if (moduleType == MODULE_TYPES.JS) {
    traverseJsModule(curModulePath, callback);
  } else if (moduleType == MODULE_TYPES.CSS) {
    traverseCssModule(curModulePath, callback);
  } else if (moduleType == MODULE_TYPES.VUE) {
    traverseVueModule(curModulePath, callback);
  }
}

const setRequirePathResolver = (resolver) => {
  requirePathResolver = resolver;
};

export { traverseModule, setRequirePathResolver };
