import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { Logger, reportRd, sleep, getProjectRootDirByFilePath } from '@joycoder/shared';
import { serializeError } from 'serialize-error';
import { getTsconfig } from 'get-tsconfig';
import findUnusedModule from './main';

function detection(file: vscode.Uri[]): void {
  if (file.length === 0) {
    Logger.showInformationMessage('JoyCode: 请先在资源管理器中选中入口文件');
    return;
  }
  const paths = file.map((item: vscode.Uri) => item.fsPath.replace(/\\/g, '/'));

  // 若多选文件或文件夹，只处理第一个
  const selPath = paths[0];
  const stat = fs.lstatSync(selPath);
  if (stat.isDirectory()) {
    return Logger.showErrorMessage('请选择项目入口文件～');
  }

  vscode.window.withProgress(
    {
      cancellable: false,
      title: 'JoyCode: 无用文件检测正在进行中...',
      location: vscode.ProgressLocation.Notification,
    },
    async () => {
      // 延时500毫秒 避免进度提示被代码查重逻辑阻塞
      await sleep(500);

      try {
        const { unused } = findUnusedModule({
          entries: [selPath],
          includes: [`${path.dirname(selPath)}/**/*`],
          resolveRequirePath(curDir, requirePath) {
            let res = '';
            if (requirePath.startsWith('/')) {
              res = requirePath;
            } else if (requirePath.startsWith('.')) {
              res = path.join(curDir, requirePath);
            } else {
              const tsconfig = getTsconfig(selPath);
              const alias = tsconfig?.config?.compilerOptions?.paths || {};
              const firstSeg = requirePath.match(/^\/?([^\/]+)/)?.[0] || '';
              const aliasKey = `${firstSeg}/*`;
              const rootDir = getProjectRootDirByFilePath(selPath);
              if (alias[aliasKey]) {
                const wholePath = path.join(rootDir, `${alias[aliasKey]}`).replace('/*', '');
                res = requirePath.replace(firstSeg, wholePath);
              } else {
                res = path.join(rootDir, './node_modules');
              }
            }
            return res;
          },
        });
        codeDiagnose(unused || {});
        return Promise.resolve();
      } catch (error) {
        Logger.showInformationMessage('JoyCode: 无用文件检测失败~' + JSON.stringify(serializeError(error)));
        return Promise.reject();
      }
    }
  );

  reportRd(29);
}

const collection = vscode.languages.createDiagnosticCollection('JoyCode');
function codeDiagnose(clones) {
  if (clones.length == 0) {
    Logger.showInformationMessage('JoyCode: 未检测到无用文件');
    return;
  }

  collection.clear();

  clones.forEach((item) => {
    const uri = vscode.Uri.file(item);
    const diagnostics = [
      {
        range: new vscode.Range(0, 0, 0, 0),
        message: `JoyCode:检测到该文件未被引用`,
        severity: vscode.DiagnosticSeverity.Information,
      },
    ];
    collection.set(uri, diagnostics);
  });

  Logger.showInformationMessage(`JoyCode: 检测发现${clones.length}个未被引用文件，点击查看详细内容`, '查看').then(
    (result: string) => {
      if (result === '查看') {
        vscode.commands.executeCommand('workbench.actions.view.problems');
      }
    }
  );
}

export default vscode.commands.registerCommand('JoyCode.unusedFileDetection', async (...file) => {
  detection(file[1] || []);
});
