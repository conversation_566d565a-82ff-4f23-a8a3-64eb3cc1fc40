import * as path from 'path';
import findUnusedModule from './main';

const { unused } = findUnusedModule({
  // cwd: process.cwd(),
  entries: ['/Users/<USER>/code/wechat_workspace/jxapp/src/pages/pingou/detail/index.tsx'],
  includes: ['/Users/<USER>/code/wechat_workspace/jxapp/src/pages/pingou/detail/**/*'],
  resolveRequirePath(curDir, requirePath) {
    let res = '';
    if (requirePath.startsWith('/')) {
      res = requirePath;
    } else if (requirePath.startsWith('.')) {
      res = path.join(curDir, requirePath);
    } else if (requirePath.startsWith('@pgdetail')) {
      res = requirePath.replace('@pgdetail', '/Users/<USER>/code/wechat_workspace/jxapp/src/pages/pingou/detail');
    } else {
      res = path.join(curDir, './node_modules');
    }
    // console.log(res)
    return res;
  },
});

console.log(unused);

// 测试命令： ts-node test.ts
