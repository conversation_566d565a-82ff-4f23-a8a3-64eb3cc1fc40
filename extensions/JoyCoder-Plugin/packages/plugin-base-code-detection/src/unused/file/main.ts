import { resolve, normalize } from 'path';
import * as fastGlob from 'fast-glob';
import { traverseModule, setRequirePathResolver } from './traverseModule';

const defaultOptions = {
  entries: [],
  includes: ['**/*', '!node_modules'],
  resolveRequirePath: () => {},
};

function findUnusedModule(options) {
  const { entries, includes, resolveRequirePath } = Object.assign(defaultOptions, options);

  const allFiles = fastGlob.sync(includes).map((item) => normalize(item));
  const entryModules: string[] = [];
  const usedModules: string[] = [];

  setRequirePathResolver(resolveRequirePath);

  entries.forEach((entry: string) => {
    entryModules.push(entry);
    traverseModule(entry, (modulePath) => {
      usedModules.push(modulePath);
    });
  });

  const unusedModules = allFiles.filter((filePath) => {
    const resolvedFilePath = resolve(filePath);
    return !entryModules.includes(resolvedFilePath) && !usedModules.includes(resolvedFilePath);
  });
  return {
    all: allFiles,
    used: usedModules,
    unused: unusedModules,
  };
}

export default findUnusedModule;
