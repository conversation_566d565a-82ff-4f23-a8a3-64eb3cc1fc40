import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { Logger, reportRd, sleep } from '@joycoder/shared';
import analyzeTsConfig from 'ts-unused-exports';
import { serializeError } from 'serialize-error';

function detection(file: vscode.Uri[]): void {
  if (file.length === 0) {
    Logger.showInformationMessage('JoyCode: 请先在资源管理器中选中文件或文件夹');
    return;
  }
  const paths = file.map((item: vscode.Uri) => item.fsPath.replace(/\\/g, '/'));

  // 若多选文件或文件夹，只处理第一个
  const selPath = paths[0];
  const stat = fs.lstatSync(selPath);
  const selDir = stat.isDirectory() ? selPath : path.dirname(selPath);
  const tsConfigPath = path.join(selDir, 'tsconfig.json');
  if (!fs.existsSync(tsConfigPath)) {
    return Logger.showErrorMessage('请确保目录下存在tsconfig.json～');
  }

  vscode.window.withProgress(
    {
      cancellable: false,
      title: 'JoyCode: 无用代码检测正在进行中...',
      location: vscode.ProgressLocation.Notification,
    },
    async () => {
      // 延时500毫秒 避免进度提示被代码查重逻辑阻塞
      await sleep(500);

      try {
        // 参数见：https://github.com/pzavolinsky/ts-unused-exports
        const result = analyzeTsConfig(tsConfigPath, ['--excludeDeclarationFiles']);
        codeDiagnose(result || {});
        return Promise.resolve();
      } catch (error) {
        Logger.showInformationMessage('JoyCode: 无用代码检测失败~' + JSON.stringify(serializeError(error)));
        return Promise.reject();
      }
    }
  );

  reportRd(28);
}

const collection = vscode.languages.createDiagnosticCollection('JoyCode');
function codeDiagnose(clonesMap) {
  if (Object.keys(clonesMap).length == 0) {
    Logger.showInformationMessage('JoyCode: 未检测到无用代码');
    return;
  }

  // clear the previous problems first
  collection.clear();

  let count = 0;
  for (const path in clonesMap) {
    const uri = vscode.Uri.file(path);
    const group = clonesMap[path];
    const diagnostics = group.map((item) => {
      const { exportName, location = { line: 1 } } = item;

      const range = new vscode.Range(location.line - 1, 0, location.line, 0);

      return {
        range: range,
        message: `JoyCode:检测到未被引用的导出 - ${exportName}`,
        severity: vscode.DiagnosticSeverity.Information,
      };
    });
    count = count + diagnostics.length;
    collection.set(uri, diagnostics);
  }

  Logger.showInformationMessage(`JoyCode: 检测发现${count}处未引用代码，点击查看详细内容`, '查看').then(
    (result: string) => {
      if (result === '查看') {
        vscode.commands.executeCommand('workbench.actions.view.problems');
      }
    }
  );
}

export default vscode.commands.registerCommand('JoyCode.unusedTsExportDetection', async (...file) => {
  detection(file[1] || []);
});
