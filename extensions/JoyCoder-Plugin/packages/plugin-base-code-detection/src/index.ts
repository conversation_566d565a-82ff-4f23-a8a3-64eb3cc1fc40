import * as vscode from 'vscode';
import duplicateCodeDetection from './duplicate';
import unusedTsExportDetection from './unused/tsexport';
import unusedFileDetection from './unused/file';

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(duplicateCodeDetection);
  context.subscriptions.push(unusedTsExportDetection);
  context.subscriptions.push(unusedFileDetection);
}
