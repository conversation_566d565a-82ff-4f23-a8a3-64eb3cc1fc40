import * as fs from 'fs/promises';
import * as os from 'os';
import * as path from 'path';
import { fileExistsAtPath } from '../../utils/fs';
import { GlobalFileNames } from '../storage/disk';
import { getWorkspacePath } from '../../utils/path';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { defaultUserPrompts } from './promptContent';

interface IUserPrompt {
  name: string;
  prompt: string;
  label: string;
  description?: string;
}

export class UserPromptManager {
  private userPrompts: Set<IUserPrompt> = new Set();
  private userPromptFilePath: string;

  constructor() {
    const cwd = getWorkspacePath();
    this.userPromptFilePath = FileSystemHelper.join(cwd, '.joycode', GlobalFileNames.userPrompts);
    this.initialize();
  }

  async getAndCreateUserPromptFilePath(): Promise<string> {
    const userPromptFilePath = this.userPromptFilePath;
    const fileExists = await fileExistsAtPath(userPromptFilePath);
    const defaultUserPrompt = JSON.stringify(defaultUserPrompts);
    if (!fileExists) {
      await fs.writeFile(userPromptFilePath, defaultUserPrompt);
    }
    return userPromptFilePath;
  }

  async readPromptFile() {
    try {
      const settingsPath = await this.getAndCreateUserPromptFilePath();
      const content = (await FileSystemHelper.readFile(settingsPath, 'utf-8')) ?? '[]';
      const promptList = JSON.parse(content);
      const osHomePromptFilePath = path.join(os.homedir(), '.joycode', GlobalFileNames.userPrompts);
      const fileExists = await fileExistsAtPath(osHomePromptFilePath);
      if (fileExists) {
        const promptContent = (await FileSystemHelper.readFile(osHomePromptFilePath, 'utf-8')) ?? '[]';
        const homePromptList = JSON.parse(promptContent);
        promptList.push(...homePromptList);
      }
      const userPrompts = new Set(promptList as IUserPrompt[]);
      return Array.from(userPrompts);
    } catch (error) {
      // console.error('读取UserPrompt文件错误:', error);
      return [];
    }
  }

  async getUserPromptByName(name: string): Promise<IUserPrompt | undefined> {
    const userPrompts = await this.readPromptFile();
    const userPrompt = userPrompts.find((item: IUserPrompt) => item.name === name);
    return userPrompt;
  }

  private async initialize(): Promise<void> {
    await this.getAndCreateUserPromptFilePath();
    const fileContent = await this.readPromptFile();
    this.userPrompts = new Set(fileContent as IUserPrompt[]);
  }

  addUserPrompt(promptData: IUserPrompt): void {
    if (!promptData) return;
    this.userPrompts.add(promptData);
    this.updatePromptFile();
  }

  async removeUserPrompt(promptData: IUserPrompt) {
    if (!promptData) return;
    const fileContent = await this.readPromptFile();
    this.userPrompts = new Set(fileContent as IUserPrompt[]);
    this.userPrompts.delete(promptData);
    this.updatePromptFile();
  }

  async updateUserPrompt(promptData: IUserPrompt) {
    if (!promptData) return;
    const fileContent = await this.readPromptFile();
    this.userPrompts = new Set(fileContent as IUserPrompt[]);
    this.userPrompts.forEach((item) => {
      if (item.name === promptData.name) {
        item.prompt = promptData.prompt;
      }
      if (item.prompt === promptData.prompt) {
        item.name = promptData.name;
      }
    });
    this.updatePromptFile();
  }

  /**
   * 获取用户提示数据
   * @param isString 是否返回字符串格式
   * @returns 如果isString为true，返回JSON字符串；否则返回提示数组
   */
  async getUserPrompt(isString?: boolean): Promise<string | IUserPrompt[]> {
    const userPrompts = Array.from(this.userPrompts);
    if (isString) {
      try {
        return JSON.stringify(userPrompts);
      } catch (error) {
        console.error('格式化UserPrompt文件错误:', error);
        return '';
      }
    }
    return userPrompts;
  }
  /**
   * 更新用户提示词文件
   * 获取用户提示词并写入文件
   * @returns Promise<void>
   */
  private async updatePromptFile(): Promise<void> {
    try {
      const fileContent = await this.getUserPrompt(true);
      await fs.writeFile(this.userPromptFilePath, `${fileContent}`);
    } catch (error) {
      console.error('%c [ 更新用户提示词失败 ]-63', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
}
