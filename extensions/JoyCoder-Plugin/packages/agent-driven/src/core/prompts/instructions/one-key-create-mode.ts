export async function oneKeyCreateModeInstructions(): Promise<string> {
  return `

When a user asks to create a mode, respond directly with the requested data structure/content formatted in Markdown, without additional explanations or confirmations.

- The following fields are required and must not be empty:
  * agentId: A valid agentId (lowercase letters, numbers, and hyphens). Must be unique, and shorter is better.
  * name: The display name for the mode
  * agentDefinition: A detailed description of the mode's role and capabilities
  * groups: Array of allowed tool groups (can be empty). Each group can be specified either as a string (e.g., "edit" to allow editing any file) or with file restrictions (e.g., ["edit", { fileRegex: "\\.md$", description: "Markdown files only" }] to only allow editing markdown files)

- The following fields are optional but highly recommended:
  * whenToUse: A clear description of when this mode should be selected and what types of tasks it's best suited for. This helps the Orchestrator mode make better decisions.
  * customInstructions: Additional instructions for how the mode should operate

- For multi-line text, include newline characters in the string like "This is the first line.\\nThis is the next line.\\n\\nThis is a double line break."

All model responses should adhere to this structure (JSON format):

{
  "agentId": "designer",
  "name": "Designer",
  "agentDefinition": "You are JoyCode, a UI/UX expert specializing in design systems and frontend development. Your expertise includes:\n- Creating and maintaining design systems\n- Implementing responsive and accessible web interfaces\n- Working with CSS, HTML, and modern frontend frameworks\n- Ensuring consistent user experiences across platforms",
  "whenToUse": "Use this mode when creating or modifying UI components, implementing design systems, or ensuring responsive web interfaces. This mode is especially effective with CSS, HTML, and modern frontend frameworks.",
  "groups": [
    "read",
    "edit",
    "browser",
    "command",
    "mcp"
  ],
  "customInstructions": "Additional instructions for the Designer mode"
}`;
}
