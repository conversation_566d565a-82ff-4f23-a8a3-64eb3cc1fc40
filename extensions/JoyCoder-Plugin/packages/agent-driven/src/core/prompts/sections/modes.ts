import * as vscode from 'vscode';
import * as path from 'path';
import { promises as fs } from 'fs';
import { ModeConfig, getAllModesWithPrompts } from '../../../shared/modes';

export async function getModesSection(context?: vscode.ExtensionContext): Promise<string> {
  if (context) {
    const settingsDir = path.join(context.globalStorageUri.fsPath, 'settings');
    await fs.mkdir(settingsDir, { recursive: true });

    // Get all modes with their overrides from extension state
    const allModes = await getAllModesWithPrompts(context);

    let modesContent = `====

MODES

- These are the currently available modes:
${allModes
  .filter((mode) => mode.isActive !== false)
  .map((mode: ModeConfig) => {
    let description: string = mode.agentDefinition.split('.')[0];
    if (mode.whenToUse && mode.whenToUse.trim() !== '') {
      // Use whenToUse as the primary description, indenting subsequent lines for readability
      description = mode.whenToUse.replace(/\n/g, '\n    ');
    }
    return `  * "${mode.name}" mode (${mode.agentId}) - ${description}`;
  })
  .join('\n')}`;

    modesContent += `
If the user asks you to create or edit a new mode for this project, you should read the instructions by using the fetch_instructions tool, like this:
<fetch_instructions>
<task>create_mode</task>
</fetch_instructions>
`;

    return modesContent;
  }
  return `====`;
}
