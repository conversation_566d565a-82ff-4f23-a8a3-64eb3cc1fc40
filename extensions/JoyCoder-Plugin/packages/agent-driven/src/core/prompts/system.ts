import * as vscode from 'vscode';
import * as os from 'os';
import * as path from 'path';
import { McpHub } from '../../services/mcp/McpHub';
import { BrowserSettings } from '../../shared/BrowserSettings';
import {
  getCapabilitiesSection,
  getMcpServersSection,
  getModesSection,
  getObjectiveSection,
  getRulesSection,
  getSystemInfoSection,
  getSharedToolUseSection,
  getToolUseGuidelinesSection,
  addCustomInstructions,
  markdownFormattingSection,
  PromptVariables,
  loadSystemPromptFile,
  getMemoryModuleInstructions,
} from './sections/index';
import { getToolDescriptions } from './tools/index';
import { getToolUseExamples } from './sections/tool-use-examples';
import { Mode } from '../../shared/modes';
import { formatLanguage } from '../../shared/language';
import {
  CustomModePrompts,
  getModeBySlug,
  ModeConfig,
  modes,
  PromptComponent,
} from '../../../web-agent/src/utils/modes';
import { DiffStrategy } from '../../shared/tools';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

// 新增：获取记忆内容的函数
async function getMemoryContent(): Promise<string> {
  const homeDir = os.homedir();
  const memoryDir = path.join(homeDir, '.joycode', 'memory');
  const memoryPath = path.join(memoryDir, 'memory.md');

  try {
    // 使用更可靠的方法检查文件是否存在
    const fileExists = await FileSystemHelper.access(memoryPath)
      .then(() => true)
      .catch(() => false);

    if (!fileExists) {
      // 文件不存在，创建目录和文件
      try {
        // 确保目录存在
        await FileSystemHelper.mkdir(memoryDir, { recursive: true });
        // 创建空文件
        await FileSystemHelper.writeFile(memoryPath, '');
        console.log('Created empty memory file:', memoryPath);
      } catch (createError) {
        console.error('Error creating memory file:', createError);
      }
    }

    // 读取文件内容
    const content = await FileSystemHelper.readFile(memoryPath, 'utf-8');
    return content.trim();
  } catch (error) {
    console.error('Error reading memory file:', error);
    return '';
  }
}

export const SYSTEM_PROMPT = async (
  context: vscode.ExtensionContext,
  cwd: string,
  supportsComputerUse: boolean,
  mode: Mode,
  mcpHub?: McpHub,
  supportsCodebase?: boolean,
  browserSettings?: BrowserSettings,
  enableMcpServerCreation?: boolean,
  language?: string,
  customModes?: ModeConfig[],
  customModePrompts?: CustomModePrompts,
  globalCustomInstructions?: string,
  diffStrategy?: DiffStrategy
): Promise<string> => {
  const getPromptComponent = (value: any) => {
    if (typeof value === 'object' && value !== null) {
      return value as PromptComponent;
    }
    return undefined;
  };

  // Try to load custom system prompt from file
  const variablesForPrompt: PromptVariables = {
    workspace: cwd,
    mode: mode,
    language: language ?? formatLanguage(vscode.env.language),
    shell: vscode.env.shell,
    operatingSystem: os.type(),
  };
  const fileCustomSystemPrompt = await loadSystemPromptFile(cwd, mode, variablesForPrompt);

  // Check if it's a custom mode
  const promptComponent = getPromptComponent(customModePrompts?.[mode]);

  // Get full mode config from custom modes or fall back to built-in modes
  const currentMode = getModeBySlug(mode, customModes) || modes.find((m) => m.agentId === mode) || modes[0];

  // If a file-based custom system prompt exists, use it
  if (fileCustomSystemPrompt) {
    const agentDefinition = promptComponent?.agentDefinition || currentMode.agentDefinition;
    const customInstructions = await addCustomInstructions(
      promptComponent?.customInstructions || currentMode.customInstructions || '',
      globalCustomInstructions || '',
      cwd,
      mode,
      { language: language ?? formatLanguage(vscode.env.language) }
    );

    // For file-based prompts, don't include the tool sections
    return `${agentDefinition}

${fileCustomSystemPrompt}

${customInstructions}`;
  }

  const modeConfig = getModeBySlug(mode, customModes) || modes.find((m) => m.agentId === mode) || modes[0];
  const agentDefinition = promptComponent?.agentDefinition || modeConfig.agentDefinition;
  // 获取各个部分的内容
  const [mcpServersSection, modesSection] = await Promise.all([
    getMcpServersSection(mcpHub, enableMcpServerCreation),
    getModesSection(context),
  ]);

  return `
  ${agentDefinition}

${markdownFormattingSection()}

${getSharedToolUseSection()}

${await getToolDescriptions(
  cwd,
  mode,
  supportsComputerUse,
  supportsCodebase,
  browserSettings,
  mcpHub,
  customModes,
  diffStrategy
)}

${getToolUseExamples(supportsCodebase ?? false, mode === 'ask' || mode === 'chat' || mode === 'orchestrator')}

${getToolUseGuidelinesSection()}

${getMemoryModuleInstructions()}

${mode === 'chat' ? '' : mcpServersSection}

${getCapabilitiesSection(cwd, mode, supportsComputerUse, mcpHub, supportsCodebase, diffStrategy)}

${modesSection}

${getRulesSection(cwd, supportsComputerUse, supportsCodebase, browserSettings, mode === 'chat', diffStrategy, mode)}

${getSystemInfoSection(cwd)}

${getObjectiveSection(!!mode)}


${await addCustomInstructions(
  promptComponent?.customInstructions || modeConfig.customInstructions || '',
  globalCustomInstructions || '',
  cwd,
  mode,
  { language: language ?? formatLanguage(vscode.env.language) }
)}

# MEMORY CONTEXT
Important reminders and frequently encountered issues:

${await getMemoryContent()}
`;
};

export async function addUserInstructions(
  settingsCustomInstructions?: string,
  joycoderRulesFileInstructions?: string,
  JoyCoderIgnoreInstructions?: string,
  contextMenuContent?: string
): Promise<string> {
  let customInstructions = '';

  // 合并用户自定义指令
  if (settingsCustomInstructions) {
    customInstructions += settingsCustomInstructions + '\n\n';
  }
  if (joycoderRulesFileInstructions) {
    customInstructions += joycoderRulesFileInstructions + '\n\n';
  }
  if (JoyCoderIgnoreInstructions) {
    customInstructions += JoyCoderIgnoreInstructions;
  }
  if (contextMenuContent) {
    customInstructions += contextMenuContent;
  }

  return `

${customInstructions.trim()}`;
}
