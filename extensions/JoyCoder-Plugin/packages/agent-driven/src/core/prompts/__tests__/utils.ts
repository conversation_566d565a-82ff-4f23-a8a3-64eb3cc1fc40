import * as fs from 'fs/promises';
import { PathLike } from 'fs';

/**
 * 将路径转换为 POSIX 格式，便于路径比较
 */
export function toPosix(filePath: PathLike | fs.FileHandle): string {
  return filePath.toString().replace(/\\/g, '/');
}

/**
 * 创建一个模拟的 McpHub 对象
 */
export function createMockMcpHub() {
  return {
    getServers: jest.fn().mockReturnValue([{ name: 'test-server', disabled: false }]),
  };
}

/**
 * 创建一个模拟的浏览器设置对象
 */
export function createMockBrowserSettings() {
  return {
    viewport: {
      width: 1280,
      height: 800,
    },
    headless: false,
  };
}
