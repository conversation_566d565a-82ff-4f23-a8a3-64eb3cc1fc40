import { getToolDescriptions } from '../tools';
import { BrowserSettings } from '../../../shared/types';

describe('Tool Descriptions', () => {
  const cwd = '/test/path';
  const mcpHub = undefined;

  describe('getToolDescriptions', () => {
    it('should include basic tools by default', () => {
      const result = getToolDescriptions(cwd, false, mcpHub);

      // 基本工具应该始终包含
      expect(result).toContain('use_command');
      expect(result).toContain('use_read_file');
      expect(result).toContain('use_write_file');
      expect(result).toContain('use_replace_file');
      expect(result).toContain('use_search_files');
      expect(result).toContain('use_list_files');
    });

    it('should include browser tools when supportsComputerUse is true', () => {
      const browserSettings: BrowserSettings = {
        headless: false,
        viewportSize: '1280x800',
      };

      const result = getToolDescriptions(cwd, true, mcpHub, undefined, browserSettings);

      // 应该包含浏览器相关工具
      expect(result).toContain('use_browser');
      expect(result).toContain('browser_goto');
      expect(result).toContain('browser_click');
      expect(result).toContain('browser_fill');
    });

    it('should not include browser tools when supportsComputerUse is false', () => {
      const result = getToolDescriptions(cwd, false, mcpHub);

      // 不应该包含浏览器相关工具
      expect(result).not.toContain('use_browser');
      expect(result).not.toContain('browser_goto');
      expect(result).not.toContain('browser_click');
      expect(result).not.toContain('browser_fill');
    });

    it('should include MCP tools when mcpHub is provided', () => {
      const mockMcpHub: any = {
        getServers: jest.fn().mockReturnValue([{ name: 'test-server', disabled: false }]),
      };

      const result = getToolDescriptions(cwd, false, mockMcpHub as any);

      // 应该包含 MCP 相关工具
      expect(result).toContain('use_mcp_tools');
      expect(result).toContain('get_mcp_resource');
    });

    it('should not include MCP tools when mcpHub is undefined', () => {
      const result = getToolDescriptions(cwd, false, undefined);

      // 不应该包含 MCP 相关工具描述
      expect(result).not.toContain('MCP SERVERS');
    });
  });

  describe('Individual Tool Descriptions', () => {
    it('should have correct description for use_command tool', () => {
      const result = getToolDescriptions(cwd, false, mcpHub);

      expect(result).toContain('Description: Request to execute a CLI command on the system');
      expect(result).toContain('command: (required)');
      expect(result).toContain('requires_approval: (required)');
    });

    it('should have correct description for use_read_file tool', () => {
      const result = getToolDescriptions(cwd, false, mcpHub);

      expect(result).toContain('Description: Request to read the contents of a file');
      expect(result).toContain('path: (required)');
    });

    it('should have correct description for use_write_file tool', () => {
      const result = getToolDescriptions(cwd, false, mcpHub);

      expect(result).toContain('Description: Request to write content to a file');
      expect(result).toContain('path: (required)');
      expect(result).toContain('content: (required)');
    });
  });
});
