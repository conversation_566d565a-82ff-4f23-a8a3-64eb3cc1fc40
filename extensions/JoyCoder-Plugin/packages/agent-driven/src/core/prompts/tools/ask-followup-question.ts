import { ToolArgs } from './types';

export function getGetPlanInfoDescription(args: ToolArgs): string {
  return `## ask_followup_question
Description: Respond to the user's inquiry in an effort to plan a solution to the user's task. This tool should be used when you need to provide a response to a question or statement from the user about how you plan to accomplish the task. This tool is only available in planning mode. The environment_details will specify the current mode, if it is not planning mode then you should not use this tool. Depending on the user's message, you may ask questions to get clarification about the user's request, architect a solution to the task, and to brainstorm ideas with the user. For example, if the user's task is to create a website, you may start by asking some clarifying questions, then present a detailed plan for how you will accomplish the task given the context, and perhaps engage in a back and forth to finalize the details before the user switches you to implementation mode to execute the solution.

Parameters:
- response: (REQUIRED) The response to provide to the user. Do not try to use tools in this parameter, this is simply a chat response.
- options: (optional) An array of 2-5 options for the user to choose from. Each option should be a string describing a possible choice or path forward in the planning process. This can help guide the discussion and make it easier for the user to provide input on key decisions. You may not always need to provide options, but it may be helpful in many cases where it can save the user from having to type out a response manually. Do NOT present an option to toggle to implementation mode, as this will be something you need to direct the user to do manually themselves.

Usage:
<ask_followup_question>
<response>Your response here</response>
<options>
Array of options here (optional), e.g. ["Option 1", "Option 2", "Option 3"]
</options>
</ask_followup_question>

Examples:

Example 1 - Initial planning with clarifying questions:
<ask_followup_question>
<response>I'd be happy to help you create a website! To provide you with the best plan, I need to understand your requirements better. Let me ask a few clarifying questions to tailor the approach to your needs.</response>
<options>
["E-commerce website with payment integration", "Portfolio/personal website", "Business landing page", "Blog or content-focused site", "Custom web application"]
</options>
</ask_followup_question>

Example 2 - Detailed planning response:
<ask_followup_question>
<response>Based on your requirements for an e-commerce website, here's my comprehensive plan:

1. **Frontend Development** (React.js)
   - Product catalog with search and filtering
   - Shopping cart functionality
   - User authentication and profiles
   - Responsive design for mobile/desktop

2. **Backend Development** (Node.js/Express)
   - RESTful API for product management
   - User authentication system
   - Order processing logic
   - Database integration

3. **Database Setup** (MongoDB)
   - Product schema design
   - User and order data models
   - Inventory management

4. **Payment Integration**
   - Stripe API implementation
   - Secure checkout process

5. **Deployment & Testing**
   - AWS/Vercel deployment
   - Security testing
   - Performance optimization

This approach will give you a fully functional e-commerce platform. Would you like me to adjust any part of this plan?</response>
<options>
["Proceed with this plan", "Modify the technology stack", "Add additional features", "Simplify the scope"]
</options>
</ask_followup_question>

Example 3 - Simple response without options:
<ask_followup_question>
<response>That's a great point about mobile responsiveness. I'll make sure to prioritize mobile-first design in the frontend development phase and include thorough testing across different device sizes. The plan has been updated to reflect this priority.</response>
</ask_followup_question>
`;
}
