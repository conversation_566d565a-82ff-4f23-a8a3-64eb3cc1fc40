import { ToolArgs } from './types';

export function getUseReadFileDescription(args: ToolArgs): string {
  return `## use_read_file
Description: Read file contents from the specified path. Use this to examine existing files when you need to analyze code, review documents, extract configuration data, or inspect any text-based content. Supports automatic text extraction from PDF and DOCX files. Not recommended for binary files.

Parameters:
- path: (required) File path relative to current working directory ${args.cwd}
- start_line: (optional) Starting line number (1-based indexing). Omit to start from beginning
- end_line: (optional) Ending line number (1-based, inclusive). Omit to read until end

Usage:
<use_read_file>
<path>File path here</path>
<start_line>Starting line number (optional)</start_line>
<end_line>Ending line number (optional)</end_line>
</use_read_file>

Examples:

1. Reading an entire file:
<use_read_file>
<path>frontend-config.json</path>
</use_read_file>

2. Reading the first 1000 lines of a large log file:
<use_read_file>
<path>logs/application.log</path>
<end_line>1000</end_line>
</use_read_file>

3. Reading lines 500-1000 of a CSV file:
<use_read_file>
<path>data/large-dataset.csv</path>
<start_line>500</start_line>
<end_line>1000</end_line>
</use_read_file>

4. Reading a specific function in a source file:
<use_read_file>
<path>src/app.ts</path>
<start_line>46</start_line>
<end_line>68</end_line>
</use_read_file>

Note: Line range parameters enable efficient streaming for large files, processing only requested sections without memory overhead. Ideal for logs, datasets, and large text files.`;
}
