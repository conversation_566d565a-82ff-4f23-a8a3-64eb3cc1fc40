import { ToolArgs } from './types';

export function getAttemptTaskDoneDescription(args: ToolArgs): string {
  return `
## attempt_task_done

**Purpose**: Use this tool to present the final result of your work to the user after confirming that all previous tool operations have completed successfully.

**When to Use**:
- Only after receiving confirmation from the user that all previous tool uses were successful
- When the task has been completed and you can present a final result
- To optionally provide a demonstration command to showcase your work

**Critical Safety Check**:
BEFORE using this tool, you MUST verify in <thinking></thinking> tags:
1. Have you used any tools in this conversation?
2. If yes, has the user confirmed that ALL previous tool uses were successful?
3. If you haven't received explicit confirmation of success, DO NOT use this tool

**Failure to follow this safety check will result in code corruption and system failure.**

**Parameters**:
- \`result\` (REQUIRED): A complete, final description of the task result that stands alone without requiring further user input. Do not include questions or offers for additional assistance.
- \`command\` (OPTIONAL): A CLI command to demonstrate the result live. Use commands that open, run, or display your work (e.g., \`open index.html\`, \`open localhost:3000\`). Do NOT use text-only commands like \`echo\` or \`cat\`. Ensure the command is valid for the current operating system and contains no harmful instructions.

**Usage Format**:
<attempt_task_done>
<result>
[Your complete final result description here - be specific and conclusive]
</result>
<command>[Optional CLI command to demonstrate the result]</command>
</attempt_task_done>


Examples:

1. Web development task completion:
<attempt_task_done>
<result>
Successfully created a responsive portfolio website featuring:
- Navigation menu with smooth scrolling
- Hero section with call-to-action
- Project gallery with hover effects
- Contact form with validation
- Mobile-responsive design throughout
</result>
<command>open index.html</command>
</attempt_task_done>

2. Direct question response:
<attempt_task_done>
<result>
The capital of France is Paris.
</result>
</attempt_task_done>
`.trim();
}
