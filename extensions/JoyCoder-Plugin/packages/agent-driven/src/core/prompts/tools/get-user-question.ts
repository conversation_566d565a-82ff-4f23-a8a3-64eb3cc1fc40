import { ToolArgs } from './types';

export function getGetUserQuestionDescription(args: ToolArgs): string {
  return `## get_user_question
Description: Ask the user a question to gather additional information needed to complete the task. Use this tool when you need clarification, encounter ambiguities, or require specific details that are missing from the original request. This enables interactive problem-solving through direct user communication. Use sparingly to maintain efficiency - only ask when the information is truly necessary to proceed effectively.

Parameters:
- question: (required) A clear, specific question that directly addresses the information you need. Frame questions to be easily understood and answerable by the user.
- options: (optional) An array of 2-5 predefined answer choices for the user to select from. Use when you can anticipate likely responses and want to streamline the interaction. Each option must be:
  • A complete, actionable answer that requires no additional user input
  • Specific and directly related to completing the task
  • Free of placeholders, brackets, or incomplete information
  • IMPORTANT: Never include options to toggle Act style or similar system-level changes

Usage Guidelines:
- Ask one focused question at a time
- Provide options when they can save user typing and cover likely scenarios
- Ensure questions are essential for task completion
- Frame questions to elicit specific, actionable responses

Usage:
<get_user_question>
<question>Your question here</question>
<options>
Array of options here (optional), e.g. ["Option 1", "Option 2", "Option 3"]
</options>
</get_user_question>

Example: Requesting to ask the user for the path to the frontend-config.json file
<get_user_question>
<question>What is the path to the frontend-config.json file?</question>
<options>
  ["Option 1", "Option 2", "Option 3"]
</options>
</get_user_question>
`.trim();
}
