import { ToolArgs } from './types';

export function getGetMcpResourceDescription(args: ToolArgs): string {
  return `
## get_mcp_resource
Description: Retrieve and access data from external resources through MCP (Model Context Protocol) servers. This tool allows you to fetch content from various data sources including files, databases, APIs, or system information that have been made available through connected MCP servers. Use this when you need to access specific external data or content that isn't directly available in the conversation context.

Parameters:
- server_name: (required) The exact name identifier of the MCP server that hosts the desired resource. This must match a currently connected and available MCP server.
- uri: (required) The complete URI (Uniform Resource Identifier) that specifies the exact resource to retrieve. This could be a file path, API endpoint, database query identifier, or other resource locator depending on the server type.

Usage:
<get_mcp_resource>
<server_name>server name here</server_name>
<uri>resource URI here</uri>
</get_mcp_resource>

Example: Requesting to access an MCP resource

<get_mcp_resource>
<server_name>weather-server</server_name>
<uri>weather://san-francisco/current</uri>
</get_mcp_resource>

Note: This tool will return the actual content or data from the specified resource, which can then be used for analysis, processing, or answering questions.
`.trim();
}
