import { ToolArgs } from './types';

export function getUseSearchFilesDescription(args: ToolArgs): string {
  return `## use_search_files
Description: Perform a powerful regex-based search across multiple files within a directory tree. This tool recursively searches through files, finds pattern matches, and returns each match with surrounding context lines for better understanding. Ideal for code analysis, finding specific patterns, locating function definitions, or searching for text across large codebases.

Parameters:
- path: (required) The target directory path to search in, relative to the current working directory (${args.cwd}). The search will recursively include all subdirectories. Use "." for current directory.
- regex: (required) The regular expression pattern to search for. Uses Rust regex syntax (similar to PCRE). Supports:
  - Basic patterns: \`function\`, \`class MyClass\`
  - Case-insensitive: \`(?i)pattern\`
  - Word boundaries: \`\\bword\\b\`
  - Capture groups: \`(pattern)\`
  - Lookahead/lookbehind: \`(?=pattern)\`, \`(?<=pattern)\`
- file_pattern: (optional) Glob pattern to filter which files to search. Examples:
  - \`*.js\` - only JavaScript files
  - \`*.{ts,tsx}\` - TypeScript files
  - \`**/*.py\` - Python files in all subdirectories
  - If omitted, searches all files (equivalent to \`*\`)

Usage Examples:
<use_search_files>
<path>src</path>
<regex>function\\s+(\\w+)</regex>
<file_pattern>*.js</file_pattern>
</use_search_files>

<use_search_files>
<path>.</path>
<regex>(?i)todo|fixme</regex>
</use_search_files>

Note: Results will show each match with surrounding context lines. Large result sets may be truncated.
`;
}
