import { ToolArgs } from './types';

export function getClearPublishDescription(_args: ToolArgs): string {
  return `
## use_clear_publish
**Purpose**: Deploy a web project to the test environment with automated packaging, upload, and verification.

**Description**:
This tool performs a complete deployment workflow for web projects:
1. Packages the project's dist directory into a zip archive
2. Uploads the archive to the deployment server
3. Deploys the project to a test environment
4. Verifies deployment status
5. Opens the deployed application URL in the browser

Parameters:
- project_name: (required) The name of the project to be deployed. This will be used to generate the environment name in the format "joycode-{project_name}".
- project_path: (required) The path to the project directory containing the dist folder. Defaults to the current working directory.

**Prerequisites**:
- Project must be built successfully (dist directory must exist)
- Dist directory must contain deployable web assets
- Network access to deployment server required

Usage:
<use_clear_publish>
<project_name>your-project-name</project_name>
<project_path>path/to/your/project</project_path>
</use_clear_publish>

Example:
<use_clear_publish>
<project_name>my-web-app</project_name>
<project_path>frontend/my-web-app</project_path>
</use_clear_publish>

Note: The project must be built before deployment. The 'dist' directory must exist in the project path.
`;
}
