export function getFetchInstructionsDescription(): string {
  return `## fetch_instructions
Description: Retrieves detailed step-by-step instructions for performing specific development tasks. This tool provides comprehensive guidance including setup requirements, implementation steps, and best practices for the requested task type.
Parameters:
- task: (required) Specifies which type of task instructions to retrieve. Must be one of the following predefined values:
  - create_mcp_server: Instructions for creating a Model Context Protocol (MCP) server
  - create_mode: Instructions for creating a new operational mode or configuration

Example: Requesting instructions to create an MCP Server

<fetch_instructions>
<task>create_mcp_server</task>
</fetch_instructions>
`;
}
