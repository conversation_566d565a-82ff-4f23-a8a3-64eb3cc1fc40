import { ToolArgs } from './types';

export function getUseListFilesDescription(args: ToolArgs): string {
  return `## use_list_files
Description: List files and directories within a specified directory. Controls listing depth through the recursive parameter - when true, lists all contents recursively; when false or omitted, lists only immediate contents. Do not use this tool to verify files you created, as the user will confirm creation success.
Parameters:
- path: (required) Directory path to list contents from, relative to current working directory ${args.cwd}
- recursive: (optional) Boolean flag for recursive listing. Defaults to false (top-level only) when omitted.
Usage:
<use_list_files>
<path>Directory path here</path>
<recursive>true or false (optional)</recursive>
</use_list_files>`.trim();
}
