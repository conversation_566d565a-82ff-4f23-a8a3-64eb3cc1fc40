import { ToolArgs } from './types';

export function getInsertContentDescription(args: ToolArgs): string {
  return `## insert_content
Description: Insert new content at a specific line position in a file without modifying existing content. This tool adds content by inserting it before the specified line number, or appends to the end of the file when line is 0. Use this for adding imports, functions, configuration blocks, comments, or any new text that should be inserted at a precise location.

Parameters:
- path: (required) File path relative to workspace directory ${args.cwd.toPosix()}
- line: (required) Line number for insertion position (1-based indexing)
	      • Use 0 to append content at the end of file
	      • Use 1 to insert at the beginning of file (before first line)
	      • Use any positive number N to insert before line N
	      • Content will be inserted as new lines, shifting existing content down
- content: (required) The exact content to insert, including any necessary newlines or formatting

Important notes:
- This tool only ADDS content, never modifies or replaces existing lines
- Line numbers refer to the current state of the file before insertion
- Content is inserted as-is, preserve proper indentation and formatting
- Use appropriate line breaks within content parameter for multi-line insertions

Example for inserting imports at start of file:
<insert_content>
<path>src/utils.ts</path>
<line>1</line>
<content>
// Add imports at start of file
import { sum } from './math';
</content>
</insert_content>

Example for appending to the end of file:
<insert_content>
<path>src/utils.ts</path>
<line>0</line>
<content>
// This is the end of the file
</content>
</insert_content>`.trim();
}
