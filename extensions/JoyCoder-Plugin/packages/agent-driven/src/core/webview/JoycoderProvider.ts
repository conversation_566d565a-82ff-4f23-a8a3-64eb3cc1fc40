import { Anthropic } from '@anthropic-ai/sdk';
import axios from 'axios';
import fs from 'fs/promises';
import os from 'os';
import { execa } from 'execa';
import pWaitFor from 'p-wait-for';
import * as path from 'path';
import * as vscode from 'vscode';
import { buildApiHandler } from '../../adaptor/api';
import { downloadTask } from '../../integrations/misc/export-markdown';
import { getTheme } from '../../integrations/theme/getTheme';
import WorkspaceTracker from '../../integrations/workspace/WorkspaceTracker';
import { McpHub } from '../../services/mcp/McpHub';
// import { FirebaseAuthManager, UserInfo } from '../../services/auth/FirebaseAuthManager';
import { ApiProvider, ModelInfo } from '../../shared/api';
import { findLast } from '../../shared/array';
import { ExtensionMessage, ExtensionState, Platform } from '../../shared/ExtensionMessage';
import { HistoryItem } from '../../shared/HistoryItem';
import { WebviewMessage } from '../../shared/WebviewMessage';
import { fileExistsAtPath, isRemoteEnvironment } from '../../utils/fs';
import { JoyCoder } from '../Joycoder';
import { getNonce } from './getNonce';
import { getUri } from './getUri';
import { AutoApprovalSettings, DEFAULT_AUTO_APPROVAL_SETTINGS } from '../../shared/AutoApprovalSettings';
import { BrowserSettings, DEFAULT_BROWSER_SETTINGS } from '../../shared/BrowserSettings';
import { ChatContent, ChatSettings } from '../../shared/ChatSettings';
import {
  isIDE,
  chatModelConfigsLocal,
  getAssetsFilePath,
  getAutoCodeModel,
  getRemoteConfigSync,
  getVscodeConfig,
  isBusiness,
  isBusinessLocal,
} from '@joycoder/shared';
import { ChatModelConfig, getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { cleanupLegacyCheckpoints } from '../../integrations/checkpoints/CheckpointMigration';
import { getTotalTasksSize } from '../../utils/storage';
import { GlobalFileNames, GlobalStateKey, SecretKey } from '../storage/disk';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { CustomModesManager } from '../config/CustomModesManager';
import { CustomModePrompts, Mode } from '../../shared/modes';
import {
  defaultModeSlug,
  PromptComponent,
  modes,
  CodebaseIndexConfig,
  CodebaseIndexModels,
  DEFAULT_CODEBASE_INDEX_CONFIG,
} from '../../../web-agent/src/utils/modes';
import { CustomSupportPrompts } from '../../../web-agent/src/utils/support-prompt';
import { handleWebviewMessage } from './handleWebviewMessage';
import { CodebaseManager } from '@joycoder/shared/src/codebase';
import { UserPromptManager } from '../user-pormpt';
import { EMBEDDING_MODEL_PROFILES } from '../../shared/embeddingModels';
import { CodeIndexManager } from '../../services/code-index/manager';
import { ISelectionInfo } from '@joycoder/shared/src/types';
import { getWorkspacePath } from '../../utils/path';

/*
https://github.com/microsoft/vscode-web-agent-toolkit-samples/blob/main/default/weather-webview/src/providers/WeatherViewProvider.ts

https://github.com/KumarVariable/vscode-extension-sidebar-html/blob/master/src/customSidebarViewProvider.ts
*/

export class JoyCoderProvider implements vscode.WebviewViewProvider {
  public static readonly sideBarId = 'joycoder.joycoder.SidebarProvider'; // used in package.json as the view's id. This value cannot be changed due to how vscode caches views based on their id, and updating the id would break existing instances of the extension.
  public static readonly tabPanelId = 'joycoder.joycoder.TabPanelProvider';

  private static activeInstances: Set<JoyCoderProvider> = new Set();
  private disposables: vscode.Disposable[] = [];
  private view?: vscode.WebviewView | vscode.WebviewPanel;
  private modelList: ChatModelConfig[] = [];
  private defaultModel = 'JoyCoder-Pro-V3';
  latestAnnouncementId = 'jan-20-2025'; // update to some unique identifier when we add a new announcement

  public customModesManager?: CustomModesManager;

  JoyCoderStack: JoyCoder[] = [];
  joycoder?: JoyCoder;
  workspaceTracker?: WorkspaceTracker;
  mcpHub?: McpHub;
  selectionContext?: ISelectionInfo | null;
  userPromptManager?: UserPromptManager;

  constructor(readonly context: vscode.ExtensionContext, public codeIndexManager?: CodeIndexManager) {
    this.codeIndexManager = codeIndexManager;
    // this.updateGlobalState('codebaseIndexModels', EMBEDDING_MODEL_PROFILES);
    // 延迟创建CustomModesManager，避免在GlobalState初始化前调用postStateToWebview
    // this.customModesManager = new CustomModesManager(this.context, async () => {
    //   await this.postStateToWebview();
    // });
    this.userPromptManager = new UserPromptManager();
  }
  setCodeIndexManager(codeIndexManager: CodeIndexManager | undefined) {
    this.codeIndexManager = codeIndexManager;
  }

  async resolve() {
    // 将当前实例添加到JoyCoderProvider的活跃实例集合中
    JoyCoderProvider.activeInstances.add(this);

    // 创建一个WorkspaceTracker实例并赋值给workspaceTracker属性
    this.workspaceTracker = new WorkspaceTracker(this);

    // 创建一个McpHub实例并赋值给mcpHub属性
    this.mcpHub = new McpHub(this);
    CodebaseManager.initCodebase(this.context);
    // Clean up legacy checkpoints
    cleanupLegacyCheckpoints(this.context.globalStorageUri.fsPath).catch((error) => {
      console.error('Failed to cleanup legacy checkpoints:', error);
    });
    // 在resolve阶段创建CustomModesManager，此时GlobalState应该已经初始化
    this.customModesManager = new CustomModesManager(this.context, async () => {
      await this.postStateToWebview();
    });

    // 创建完成后立即发送状态到webview
    await this.postStateToWebview();
  }

  // remove the current task/JoyCoder instance (at the top of the stack), ao this task is finished
  // and resume the previous task/JoyCoder instance (if it exists)
  // this is used when a sub task is finished and the parent task needs to be resumed
  async finishSubTask(lastMessage: string) {
    // remove the last JoyCoder instance from the stack (this is the finished sub task)
    await this.removeJoyCoderFromStack();
    // resume the last JoyCoder instance in the stack (if it exists - this is the 'parnt' calling task)
    this.getCurrentJoyCoder()?.resumePausedTask(lastMessage);
    //  更新顶部任务状态
    await this.postParentTaskInfo();
  }

  async addJoyCoderToStack(joycoder: JoyCoder) {
    // Add this JoyCoder instance into the stack that represents the order of all the called tasks.
    this.JoyCoderStack.push(joycoder);

    // Ensure getState() resolves correctly.
    const state = await this.getState();

    if (!state || typeof state.mode !== 'string') {
      throw new Error('检索当前模式时出错');
    }
  }

  // Removes and destroys the top JoyCoder instance (the current finished task),
  // activating the previous one (resuming the parent task).
  async removeJoyCoderFromStack() {
    if (this.JoyCoderStack.length === 0) {
      return;
    }

    // Pop the top JoyCoder instance from the stack.
    let joycoder = this.JoyCoderStack.pop();

    if (joycoder) {
      try {
        // Abort the running task and set isAbandoned to true so
        // all running promises will exit as well.
        await joycoder.abortTask();
      } catch (e) {
        console.log(
          `[subtasks] encountered error while aborting task ${joycoder.taskId}.${joycoder.instanceId}: ${e.message}`
        );
      }

      // Make sure no reference kept, once promises end it will be
      // garbage collected.
      joycoder = undefined;
      await this.postParentTaskInfo();
    }
  }

  // returns the current cline object in the stack (the top one)
  // if the stack is empty, returns undefined
  getCurrentJoyCoder(): JoyCoder | undefined {
    if (this.JoyCoderStack.length === 0) {
      return undefined;
    }
    return this.JoyCoderStack[this.JoyCoderStack.length - 1];
  }

  getJoyCoderStack() {
    return this.JoyCoderStack;
  }

  /*
	VSCode extensions use the disposable pattern to clean up resources when the sidebar/editor tab is closed by the user or system. This applies to event listening, commands, interacting with the UI, etc.
	- https://vscode-docs.readthedocs.io/en/stable/extensions/patterns-and-principles/
	- https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
	*/
  async dispose() {
    await this.clearTask();
    if (this.view && 'dispose' in this.view) {
      this.view.dispose();
    }
    while (this.disposables.length) {
      const x = this.disposables.pop();
      if (x) {
        x.dispose();
      }
    }
    // this.view = undefined;
    this.workspaceTracker?.dispose();
    this.customModesManager?.dispose();
    this.workspaceTracker = undefined;
    this.mcpHub?.dispose();
    this.mcpHub = undefined;
    // this.authManager.dispose()
    JoyCoderProvider.activeInstances.delete(this);
  }

  async setAuthToken(token?: string) {
    await this.storeSecret('authToken', token);
  }

  async setUserInfo(info?: { displayName: string | null; email: string | null; photoURL: string | null }) {
    await this.updateGlobalState('userInfo', info);
  }

  public static getVisibleInstance(): JoyCoderProvider | undefined {
    return findLast(Array.from(this.activeInstances), (instance) => instance.view?.visible === true);
  }

  resolveWebviewView(
    webviewView: vscode.WebviewView | vscode.WebviewPanel
    //context: vscode.WebviewViewResolveContext<unknown>, used to recreate a deallocated webview, but we don't need this since we use retainContextWhenHidden
    //token: vscode.CancellationToken
  ): void | Thenable<void> {
    this.view =
      webviewView ||
      vscode.window.createWebviewPanel(
        JoyCoderProvider.tabPanelId,
        'Coder',
        {
          viewColumn: vscode.window.activeTextEditor ? vscode.ViewColumn.Beside : vscode.ViewColumn.One,
          preserveFocus: true,
        },
        {
          enableScripts: true,
          retainContextWhenHidden: true,
          localResourceRoots: [this.context.extensionUri],
        }
      );
    this.resolve().catch((error) => {
      console.error('Error in resolve():', error);
    });
    webviewView.webview.options = {
      // Allow scripts in the webview
      enableScripts: true,
      localResourceRoots: [this.context.extensionUri],
    };
    webviewView.webview.html = this.getHtmlContent(webviewView.webview);

    // Sets up an event listener to listen for messages passed from the webview view context
    // and executes code based on the message that is received
    const logoUrl = getAssetsFilePath('logo.png');
    // 添加图标
    if ('iconPath' in webviewView) {
      webviewView.iconPath = logoUrl;
    }
    this.setWebviewMessageListener(webviewView.webview);

    // Logs show up in bottom panel > Debug Console

    // Listen for when the panel becomes visible
    // https://github.com/microsoft/vscode-discussions/discussions/840
    if ('onDidChangeViewState' in webviewView) {
      // WebviewView and WebviewPanel have all the same properties except for this visibility listener
      // panel
      webviewView.onDidChangeViewState(
        () => {
          if (this.view?.visible) {
            this.postMessageToWebview({
              type: 'action',
              action: 'didBecomeVisible',
            });
          }
        },
        null,
        this.disposables
      );
    } else if ('onDidChangeVisibility' in webviewView) {
      // sidebar
      webviewView.onDidChangeVisibility(
        () => {
          if (this.view?.visible) {
            this.postMessageToWebview({
              type: 'action',
              action: 'didBecomeVisible',
            });
          }
        },
        null,
        this.disposables
      );
    }

    // Listen for when the view is disposed
    // This happens when the user closes the view or when the view is closed programmatically
    webviewView.onDidDispose(
      async () => {
        await this.dispose();
      },
      null,
      this.disposables
    );

    // Listen for when color changes
    vscode.workspace.onDidChangeConfiguration(
      async (e) => {
        if (e && e.affectsConfiguration('workbench.colorTheme')) {
          // Sends latest theme name to webview
          await this.postMessageToWebview({
            type: 'theme',
            text: JSON.stringify(await getTheme()),
          });
        }
      },
      null,
      this.disposables
    );

    // if the extension is starting a new session, clear previous task state
    // this.clearTask();
    this.removeJoyCoderFromStack();
  }
  public async initJoyCoderWithSubTask(parent: JoyCoder, task?: string, images?: string[]) {
    return this.initJoyCoderWithTask(task, images, parent);
  }

  async initJoyCoderWithTask(task?: string, images?: string[], parentTask?: JoyCoder, options = {}) {
    const {
      fuzzyMatchThreshold,
      apiConfiguration,
      customInstructions,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
    } = await this.getState();
    const jc = new JoyCoder(
      this,
      apiConfiguration,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      customInstructions,
      task,
      images,
      undefined,
      fuzzyMatchThreshold,
      this.JoyCoderStack.length > 0 ? this.JoyCoderStack[0] : undefined,
      parentTask,
      this.JoyCoderStack.length + 1
    );
    this.joycoder = jc;
    await this.addJoyCoderToStack(jc);
    // 为了子任务中展示父任务
    await this.postParentTaskInfo();
    return jc;
  }

  async postParentTaskInfo() {
    const joyCoderMessage = this.JoyCoderStack[0]?.askResponseText || this.JoyCoderStack[0]?.JoyCoderMessages[0]?.text;
    await this.postMessageToWebview({
      type: 'postTaskInfo',
      parentTaskInfo: {
        rootTask: {
          joyCoderMessage,
          taskId: this.JoyCoderStack[0]?.taskId,
        },
        isParentTask: this.JoyCoderStack?.length > 1,
      },
    });
  }

  async initJoyCoderWithHistoryItem(historyItem: HistoryItem & { rootTask?: JoyCoder; parentTask?: JoyCoder }) {
    // await this.clearTask();
    await this.removeJoyCoderFromStack();

    const {
      apiConfiguration,
      customInstructions,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      customModePrompts,
      fuzzyMatchThreshold,
      mode,
    } = await this.getState();
    const modePrompt = customModePrompts?.[mode] as PromptComponent;
    const effectiveInstructions = [customInstructions, modePrompt?.customInstructions].filter(Boolean).join('\n\n');

    this.joycoder = new JoyCoder(
      this,
      apiConfiguration,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      effectiveInstructions,
      undefined,
      undefined,
      historyItem,
      fuzzyMatchThreshold,
      historyItem.rootTask,
      historyItem.parentTask,
      historyItem.number
    );
    await this.addJoyCoderToStack(this.joycoder);
  }

  // Send any JSON serializable data to the react app
  async postMessageToWebview(message: ExtensionMessage) {
    // 使用 await 关键字等待异步操作完成
    // 通过可选链操作符 ?. 安全地访问 view 属性的 webview
    // 调用 postMessage 方法向 webview 发送消息
    // message 参数是一个 ExtensionMessage 类型的消息对象
    await this.view?.webview.postMessage(message);
  }

  /**
   * Defines and returns the HTML that should be rendered within the webview panel.
   *
   * @remarks This is also the place where references to the React webview build files
   * are created and inserted into the webview HTML.
   *
   * @param webview A reference to the extension webview
   * @param extensionUri The URI of the directory containing the extension
   * @returns A template string literal containing the HTML that should be
   * rendered within the webview panel
   */
  private getHtmlContent(webview: vscode.Webview): string {
    // Get the local path to main script run in the webview,
    // then convert it to a uri we can use in the webview.
    const dirPath = ['dist/webview'];
    // The CSS file from the React build output
    const stylesUri = getUri(webview, this.context.extensionUri, [...dirPath, 'main.css']);
    // The JS file from the React build output
    const scriptUri = getUri(webview, this.context.extensionUri, [...dirPath, 'main.js']);

    const nonce = getNonce();
    //            <link href="${codiconsUri}" rel="stylesheet" />

    // Tip: Install the es6-string-html VS Code extension to enable code highlighting below
    return /*html*/ `
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no">
            <meta name="theme-color" content="#000000">
            <link rel="stylesheet" type="text/css" href="${stylesUri}">
            <link rel="stylesheet" type="text/css" href="https://storage.360buyimg.com/jxfe/ppms/u/891523/antd.dark.css">
            <link rel="stylesheet" type="text/css" href="https://storage.360buyimg.com/jxfe/ppms/u/891523/antd.light.css">
            <title>Coder</title>
          </head>
          <body>
            <noscript>You need to enable JavaScript to run this app.</noscript>
            <div id="root"></div>
            <script nonce="${nonce}" type="module" src="${scriptUri}"></script>
          </body>
        </html>
      `;
  }

  /**
   * Sets up an event listener to listen for messages passed from the webview context and
   * executes code based on the message that is received.
   *
   * @param webview A reference to the extension webview
   */
  private setWebviewMessageListener(webview: vscode.Webview) {
    webview.onDidReceiveMessage(
      async (message: WebviewMessage) => {
        handleWebviewMessage(this, message);
      },
      null,
      this.disposables
    );
  }

  async deleteAllTaskHistory() {
    await this.clearTask();
    await this.removeJoyCoderFromStack();
    const taskHistoryKey = this.getTaskHistoryKey();
    await this.updateGlobalState(taskHistoryKey, undefined);
    try {
      const isRemote = isRemoteEnvironment();

      // Remove all contents of tasks directory
      let taskDirPath: string;
      if (isRemote) {
        const taskDirUri = vscode.Uri.joinPath(this.context.globalStorageUri, 'tasks');
        taskDirPath = taskDirUri.toString();
      } else {
        taskDirPath = path.join(this.context.globalStorageUri.fsPath, 'tasks');
      }

      if (await fileExistsAtPath(taskDirPath)) {
        if (isRemote) {
          await FileSystemHelper.rmdir(taskDirPath);
        } else {
          await fs.rm(taskDirPath, { recursive: true, force: true });
        }
      }

      // Remove checkpoints directory contents
      let checkpointsDirPath: string;
      if (isRemote) {
        const checkpointsDirUri = vscode.Uri.joinPath(this.context.globalStorageUri, 'checkpoints');
        checkpointsDirPath = checkpointsDirUri.toString();
      } else {
        checkpointsDirPath = path.join(this.context.globalStorageUri.fsPath, 'checkpoints');
      }

      if (await fileExistsAtPath(checkpointsDirPath)) {
        if (isRemote) {
          await FileSystemHelper.rmdir(checkpointsDirPath);
        } else {
          await fs.rm(checkpointsDirPath, { recursive: true, force: true });
        }
      }
    } catch (error) {
      vscode.window.showErrorMessage(
        `Encountered error while deleting task history, there may be some files left behind. Error: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
    // await this.postStateToWebview()
  }

  async updateModelState(apiModelId?: string, openAiModelId?: string, openAiBaseUrl?: string) {
    const BASE_URL = 'http://chatgpt-relay.jd.com/v1/';
    this.defaultModel = await this.getModelLabel();
    await this.updateStateByName('apiModelId', apiModelId || this.defaultModel);
    await this.updateStateByName('openAiBaseUrl', openAiBaseUrl || BASE_URL);
    await this.storeSecret('openAiApiKey', 'empty');
    await this.updateStateByName('openAiModelId', openAiModelId || this.defaultModel);
  }
  async updateModelConfig(
    updateConfig?: Partial<{
      model: string;
    }>
  ) {
    let modelConfig = getChatModelAndConfig(updateConfig?.model);
    const model = modelConfig.label;
    const inList = this.modelList.filter((item) => item.label === model);
    const config = {
      model,
      modelConfig,
      ...updateConfig,
    };
    if (inList.length === 0) {
      this.defaultModel = await this.getModelLabel();
      modelConfig = getChatModelAndConfig(this.defaultModel);
      config['modelConfig'] = modelConfig;
      config['model'] = modelConfig.label;
    }
    await this.postMessageToWebview({
      type: 'updateGPTConfig',
      modelConfig: config,
    });
    await this.updateModelState(config.model, config.model, config.modelConfig.chatApiUrl);
  }

  async getModelLabel() {
    const defaultModel = this.defaultModel;
    const remoteModel = await getAutoCodeModel();
    const isRemote = !!remoteModel;
    return isRemote ? remoteModel : defaultModel;
  }
  async getModelList() {
    const chatGPTModelConfigs: ChatModelConfig[] = isBusinessLocal()
      ? chatModelConfigsLocal
      : getRemoteConfigSync().chatGPTModelConfigs;
    // 过滤掉隐藏的模型，一些历史过期模型逐步淘汰
    const items = chatGPTModelConfigs.filter(
      (modelConfig) =>
        !modelConfig.hidden &&
        (!modelConfig.hasOwnProperty('modelFunctionType') ||
          modelConfig.modelFunctionType === 'ALL' ||
          modelConfig.modelFunctionType === 'chat' ||
          modelConfig.modelFunctionType === 'agent')
    );

    const chatModelConfigsTest = isBusiness() ? '[]' : getVscodeConfig('JoyCode.config.test-llm-list');
    this.modelList = [...items, ...JSON.parse(chatModelConfigsTest)].filter(
      (item) => isIDE() || (!isIDE() && item.features?.includes('autoCode'))
    );

    console.log('this.modelList---->', this.modelList);

    await this.postMessageToWebview({
      type: 'updateGPTModel',
      modelList: this.modelList,
    });
  }

  async subscribeEmail(email?: string) {
    if (!email) {
      return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      vscode.window.showErrorMessage('Please enter a valid email address');
      return;
    }
    this.postMessageToWebview({ type: 'emailSubscribed' });
    // Currently ignoring errors to this endpoint, but after accounts we'll remove this anyways
    try {
      const response = await axios.post(
        'https://app.joycoder.bot/api/mailing-list',
        {
          email: email,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (error) {
      console.error('Failed to subscribe email:', error);
    }
  }

  async cancelTask() {
    if (this.getCurrentJoyCoder()) {
      const { historyItem } = await this.getTaskWithId(this.getCurrentJoyCoder()?.taskId ?? '');
      try {
        await this.getCurrentJoyCoder()?.abortTask();
      } catch (error) {
        console.error('Failed to abort task', error);
      }
      const jc = this.getCurrentJoyCoder();
      await pWaitFor(
        () =>
          jc === undefined ||
          jc?.isStreaming === false ||
          jc?.didFinishAbortingStream ||
          jc?.isWaitingForFirstChunk || // if only first chunk is processed, then there's no need to wait for graceful abort (closes edits, browser, etc)
          jc.isWritingHistory === false, //避免还没写完历史文件就触发下面去读历史文件
        {
          timeout: 3_000,
        }
      ).catch(() => {
        console.error('Failed to abort task');
      });
      if (this.getCurrentJoyCoder()) {
        const jc = this.getCurrentJoyCoder() as JoyCoder;
        // 'abandoned' will prevent this joycoder instance from affecting future joycoder instance gui. this may happen if its hanging on a streaming request
        jc.abandoned = true;
      }
      await this.initJoyCoderWithHistoryItem({
        ...historyItem,
        rootTask: this.getCurrentJoyCoder()?.rootTask,
        parentTask: this.getCurrentJoyCoder()?.parentTask,
      }); // clears task again, so we need to abortTask manually above
      // await this.postStateToWebview() // new JoyCoder instance will post state when it's ready. having this here sent an empty messages array to webview leading to virtuoso having to reload the entire list
    }
  }

  async updateCustomInstructions(instructions?: string) {
    // User may be clearing the field
    await this.updateGlobalState('customInstructions', instructions || undefined);
    if (this.getCurrentJoyCoder()) {
      const jc = this.getCurrentJoyCoder() as JoyCoder;
      jc.customInstructions = instructions || undefined;
    }
    await this.postStateToWebview();
  }

  // MCP
  async getDocumentsPath(): Promise<string> {
    if (process.platform === 'win32') {
      try {
        const { stdout: docsPath } = await execa('powershell', [
          '-NoProfile', // Ignore user's PowerShell profile(s)
          '-Command',
          '[System.Environment]::GetFolderPath([System.Environment+SpecialFolder]::MyDocuments)',
        ]);
        const trimmedPath = docsPath.trim();
        if (trimmedPath) {
          return trimmedPath;
        }
      } catch (err) {
        console.error('Failed to retrieve Windows Documents path. Falling back to homedir/Documents.');
      }
    } else if (process.platform === 'linux') {
      try {
        // First check if xdg-user-dir exists
        await execa('which', ['xdg-user-dir']);

        // If it exists, try to get XDG documents path
        const { stdout } = await execa('xdg-user-dir', ['DOCUMENTS']);
        const trimmedPath = stdout.trim();
        if (trimmedPath) {
          return trimmedPath;
        }
      } catch {
        // Log error but continue to fallback
        console.error('Failed to retrieve XDG Documents path. Falling back to homedir/Documents.');
      }
    }

    // Default fallback for all platforms
    return path.join(os.homedir(), 'Documents');
  }

  async ensureMcpServersDirectoryExists(): Promise<string> {
    const isRemote = isRemoteEnvironment();
    if (isRemote) {
      // 在远程环境中，返回一个默认路径，因为我们无法在远程环境中创建本地文档目录
      return '~/Documents/JoyCode/MCP';
    } else {
      const userDocumentsPath = await this.getDocumentsPath();
      const mcpServersDir = path.join(userDocumentsPath, 'JoyCode', 'MCP');
      try {
        await fs.mkdir(mcpServersDir, { recursive: true });
      } catch (error) {
        return '~/Documents/JoyCode/MCP'; // in case creating a directory in documents fails for whatever reason (e.g. permissions) - this is fine since this path is only ever used in the system prompt
      }
      return mcpServersDir;
    }
  }

  async ensureSettingsDirectoryExists(): Promise<string> {
    const isRemote = isRemoteEnvironment();
    if (isRemote) {
      const settingsDirUri = vscode.Uri.joinPath(this.context.globalStorageUri, 'settings');
      // 直接传递 URI 对象而不是字符串，让 FileSystemHelper 正确处理
      await FileSystemHelper.mkdir(settingsDirUri);
      return settingsDirUri.toString();
    } else {
      const settingsDir = path.join(this.context.globalStorageUri.fsPath, 'settings');
      await fs.mkdir(settingsDir, { recursive: true });
      return settingsDir;
    }
  }

  // OpenAi
  async getOpenAiModels(baseUrl?: string, apiKey?: string) {
    try {
      if (!baseUrl) {
        return [];
      }

      if (!URL.canParse(baseUrl)) {
        return [];
      }

      const config: Record<string, any> = {};
      if (apiKey) {
        config['headers'] = { Authorization: `Bearer ${apiKey}` };
      }

      const response = await axios.get(`${baseUrl}/models`, config);
      const modelsArray = response.data?.data?.map((model: any) => model.id) || [];
      const models = [...new Set<string>(modelsArray)];
      return models;
    } catch (error) {
      return [];
    }
  }

  private async ensureCacheDirectoryExists(): Promise<string> {
    const cacheDir = path.join(this.context.globalStorageUri.fsPath, 'cache');
    await fs.mkdir(cacheDir, { recursive: true });
    return cacheDir;
  }

  // 获取环境特定的任务历史键
  private getTaskHistoryKey(): GlobalStateKey {
    const isRemote = isRemoteEnvironment();
    return isRemote ? 'taskHistoryRemote' : 'taskHistory';
  }

  // Task history
  async getTaskWithId(
    id: string,
    options?: any
  ): Promise<{
    historyItem: HistoryItem;
    taskDirPath: string;
    apiConversationHistoryFilePath: string;
    uiMessagesFilePath: string;
    apiConversationHistory: Anthropic.MessageParam[];
  }> {
    const taskHistoryKey = this.getTaskHistoryKey();
    const history = ((await this.getGlobalState(taskHistoryKey)) as HistoryItem[] | undefined) || [];

    const historyItem = history.find((item) => item.id === id);
    if (historyItem) {
      // 在远程环境中，需要使用正确的路径构建方式
      const isRemote = isRemoteEnvironment();

      let taskDirPath: string;
      let apiConversationHistoryFilePath: string | vscode.Uri;
      let uiMessagesFilePath: string | vscode.Uri;

      if (isRemote) {
        // 远程环境：使用 vscode.Uri 构建正确的远程路径
        const globalStorageUri = this.context.globalStorageUri;

        const taskDirUri = vscode.Uri.joinPath(globalStorageUri, 'tasks', id);
        const apiConversationHistoryFileUri = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.apiConversationHistory);
        const uiMessagesFileUri = vscode.Uri.joinPath(taskDirUri, GlobalFileNames.uiMessages);

        // 在远程环境中，保持 Uri 对象用于文件操作，但返回字符串用于接口兼容性
        taskDirPath = taskDirUri.toString();
        apiConversationHistoryFilePath = apiConversationHistoryFileUri;
        uiMessagesFilePath = uiMessagesFileUri;
      } else {
        // 本地环境：使用传统的路径构建方式
        taskDirPath = path.join(this.context.globalStorageUri.fsPath, 'tasks', id);
        apiConversationHistoryFilePath = path.join(taskDirPath, GlobalFileNames.apiConversationHistory);
        uiMessagesFilePath = path.join(taskDirPath, GlobalFileNames.uiMessages);
      }
      const fileExists = await fileExistsAtPath(apiConversationHistoryFilePath);

      if (fileExists) {
        try {
          let fileContent: string;

          if (isRemote) {
            // 在远程环境中，使用 FileSystemHelper 来读取文件
            fileContent = await FileSystemHelper.readFile(apiConversationHistoryFilePath, 'utf8');
          } else {
            // 在本地环境中，根据选项选择文件读取器
            if (options?.showHistoryItem) {
              fileContent = await fs.readFile(apiConversationHistoryFilePath as string, 'utf8');
            } else {
              fileContent = await FileSystemHelper.readFile(apiConversationHistoryFilePath, 'utf8');
            }
          }

          const apiConversationHistory = JSON.parse(fileContent);

          return {
            historyItem,
            taskDirPath,
            apiConversationHistoryFilePath:
              typeof apiConversationHistoryFilePath === 'string'
                ? apiConversationHistoryFilePath
                : apiConversationHistoryFilePath.toString(),
            uiMessagesFilePath:
              typeof uiMessagesFilePath === 'string' ? uiMessagesFilePath : uiMessagesFilePath.toString(),
            apiConversationHistory,
          };
        } catch (readError) {
          console.error(`[getTaskWithId] Error reading/parsing file:`, readError);
          throw new Error(
            `Failed to read task file: ${readError instanceof Error ? readError.message : String(readError)}`
          );
        }
      }
      // await this.deleteTaskFromState(id);
      // 当用户卸载插件后，插件文件存储目录被清空，但globalState不清空
      // 用户再次安装插件，能看见对话历史（从globalState获取），但不能查看详情（存储文件已被清空）
      console.error(`[getTaskWithId] Task ${id} file not found on disk at: ${apiConversationHistoryFilePath}`);
      throw new Error(`Task ${id} not found on disk`);
    }
    console.error(`[getTaskWithId] Task ${id} not found in globalState`);
    throw new Error(`Task ${id} not found in globalState`);
  }

  async showTaskWithId(id: string) {
    try {
      if (id !== this.getCurrentJoyCoder()?.taskId) {
        // non-current task
        const { historyItem } = await this.getTaskWithId(id, { showHistoryItem: true });
        await this.initJoyCoderWithHistoryItem(historyItem); // clears existing task
      }
      await this.postMessageToWebview({
        type: 'action',
        action: 'chatButtonClicked',
      });
    } catch (error) {
      console.error(`[showTaskWithId] Error details:`, error);
      console.error(`[showTaskWithId] Error message: ${error instanceof Error ? error.message : String(error)}`);
      console.error(`[showTaskWithId] Error stack: ${error instanceof Error ? error.stack : 'No stack trace'}`);
      vscode.window.showErrorMessage(`该对话历史文件已丢失: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async exportTaskWithId(id: string) {
    try {
      const { historyItem, apiConversationHistory } = await this.getTaskWithId(id, { showHistoryItem: true });
      await downloadTask(historyItem.ts, apiConversationHistory);
    } catch (error) {
      vscode.window.showErrorMessage('导出失败，该对话历史文件已丢失');
    }
  }

  async deleteTaskWithId(id: string) {
    try {
      if (id === this.getCurrentJoyCoder()?.taskId) {
        // await this.clearTask();
        // / if we found the taskid to delete - call finish to abort this task and allow a new task to be started,
        // if we are deleting a subtask and parent task is still waiting for subtask to finish - it allows the parent to resume (this case should neve exist)
        await this.finishSubTask('任务已删除');
      }

      const { taskDirPath, apiConversationHistoryFilePath, uiMessagesFilePath } = await this.getTaskWithId(id, {
        showHistoryItem: true,
      });

      await this.deleteTaskFromState(id);

      const isRemote = isRemoteEnvironment();

      // Delete the task files
      const apiConversationHistoryFileExists = await fileExistsAtPath(apiConversationHistoryFilePath);
      if (apiConversationHistoryFileExists) {
        if (isRemote) {
          await FileSystemHelper.unlink(apiConversationHistoryFilePath);
        } else {
          await fs.unlink(apiConversationHistoryFilePath);
        }
      }

      const uiMessagesFileExists = await fileExistsAtPath(uiMessagesFilePath);
      if (uiMessagesFileExists) {
        if (isRemote) {
          await FileSystemHelper.unlink(uiMessagesFilePath);
        } else {
          await fs.unlink(uiMessagesFilePath);
        }
      }

      // Handle legacy messages file
      let legacyMessagesFilePath: string;
      if (isRemote) {
        const taskDirUri = vscode.Uri.parse(taskDirPath);
        const legacyMessagesFileUri = vscode.Uri.joinPath(taskDirUri, 'claude_messages.json');
        legacyMessagesFilePath = legacyMessagesFileUri.toString();
      } else {
        legacyMessagesFilePath = path.join(taskDirPath, 'claude_messages.json');
      }

      if (await fileExistsAtPath(legacyMessagesFilePath)) {
        if (isRemote) {
          await FileSystemHelper.unlink(legacyMessagesFilePath);
        } else {
          await fs.unlink(legacyMessagesFilePath);
        }
      }

      // Delete the checkpoints directory if it exists
      let checkpointsDir: string;
      if (isRemote) {
        const taskDirUri = vscode.Uri.parse(taskDirPath);
        const checkpointsDirUri = vscode.Uri.joinPath(taskDirUri, 'checkpoints');
        checkpointsDir = checkpointsDirUri.toString();
      } else {
        checkpointsDir = path.join(taskDirPath, 'checkpoints');
      }

      if (await fileExistsAtPath(checkpointsDir)) {
        try {
          if (isRemote) {
            await FileSystemHelper.rmdir(checkpointsDir);
          } else {
            await fs.rm(checkpointsDir, { recursive: true, force: true });
          }
        } catch (error) {
          console.error(`Failed to delete checkpoints directory for task ${id}:`, error);
          // Continue with deletion of task directory - don't throw since this is a cleanup operation
        }
      }

      // Delete the task directory
      if (isRemote) {
        await FileSystemHelper.rmdir(taskDirPath);
      } else {
        await fs.rmdir(taskDirPath); // succeeds if the dir is empty
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Task not found') {
        await this.deleteTaskFromState(id);
        return;
      }
      throw error;
    }
    await this.refreshTotalTasksSize();
  }

  async deleteTaskFromState(id: string) {
    // Remove the task from history
    const taskHistoryKey = this.getTaskHistoryKey();
    const taskHistory = ((await this.getGlobalState(taskHistoryKey)) as HistoryItem[] | undefined) || [];
    const updatedTaskHistory = taskHistory.filter((task) => task.id !== id);
    await this.updateGlobalState(taskHistoryKey, updatedTaskHistory);

    // Notify the webview that the task has been deleted
    await this.postStateToWebview();
  }

  async postStateToWebview() {
    // 如果 customModesManager 还没有初始化，跳过此次调用
    if (!this.customModesManager) {
      console.warn('CustomModesManager not initialized yet, skipping postStateToWebview');
      return;
    }

    const state = await this.getStateToPostToWebview();
    const allmodes = [...modes, ...state.customModes];
    if (!allmodes.find((modeItem) => modeItem.isActive !== false && modeItem.agentId === state.mode)) {
      state.mode = defaultModeSlug;
      this.updateStateByName('mode', defaultModeSlug);
      // this.updateGlobalState('mode', defaultModeSlug);
    }
    this.postMessageToWebview({ type: 'state', state });
  }

  async getStateToPostToWebview(): Promise<ExtensionState> {
    const {
      apiConfiguration,
      lastShownAnnouncementId,
      customInstructions,
      taskHistory,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      mode,
      customModes,
      // userInfo,
      customSupportPrompts,
      authToken,
      codebaseIndexConfig,
      codebaseIndexModels,
      joyCoderEnv,
      joyCoderBaseUrl,
    } = await this.getState();

    const cwd = this.cwd;

    return {
      version: this.context.extension?.packageJSON?.version ?? '',
      apiConfiguration,
      customInstructions,
      joyCoderEnv,
      joyCoderBaseUrl,
      isIDE: isIDE(),
      uriScheme: vscode.env.uriScheme,
      currentTaskItem: this.getCurrentJoyCoder()?.taskId
        ? (taskHistory || []).find((item: { id: string | undefined }) => item.id === this.getCurrentJoyCoder()?.taskId)
        : undefined,
      checkpointTrackerErrorMessage: this.getCurrentJoyCoder()?.checkpointTrackerErrorMessage,
      joycoderMessages: this.getCurrentJoyCoder()?.JoyCoderMessages || [],
      taskHistory: (taskHistory || [])
        .filter((item: { ts: any; task: any }) => item.ts && item.task)
        .sort((a: { ts: number }, b: { ts: number }) => b.ts - a.ts),
      shouldShowAnnouncement: lastShownAnnouncementId !== this.latestAnnouncementId,
      platform: process.platform as Platform,
      isRemoteEnvironment: isRemoteEnvironment(),
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      isLoggedIn: !!authToken,
      mode,
      customModes,
      customSupportPrompts,
      codebaseIndexConfig,
      codebaseIndexModels,
      cwd,
      // userInfo,
    };
  }

  async clearTask() {
    // this.joycoder?.abortTask();
    this.getCurrentJoyCoder()?.abortTask();
    this.removeJoyCoderFromStack();
    // this.joycoder = undefined; // removes reference to it, so once promises end it will be garbage collected
  }
  /*
	Storage
	https://dev.to/kompotkot/how-to-use-secretstorage-in-your-vscode-extensions-2hco
	https://www.eliostruyf.com/devhack-code-extension-storage-options/
	*/
  async getStateByName(name = 'mode') {
    const hasWorkspaceFolder = !!vscode.workspace.workspaceFolders;
    return hasWorkspaceFolder ? this.getWorkspaceState(name) : this.getGlobalState(name);
  }

  async updateStateByName(name = 'mode', newMode: string) {
    const hasWorkspaceFolder = !!vscode.workspace.workspaceFolders;
    return hasWorkspaceFolder ? this.updateWorkspaceState(name, newMode) : this.updateGlobalState(name, newMode);
  }

  async getState() {
    const customModes = (await this.customModesManager?.getCustomModes()) || [];

    const [
      storedApiProvider,
      apiModelId,
      apiKey,
      openAiBaseUrl,
      openAiApiKey,
      openAiModelId,
      lastShownAnnouncementId,
      customInstructions,
      taskHistory,
      autoApprovalSettings,
      browserSettings,
      chatSettings,
      vsCodeLmModelSelector,
      authToken,
      previousModeApiProvider,
      previousModeModelId,
      previousModeModelInfo,
      diffEnabled,
      writeAction, // 写入模式
      planActSeparateModelsSettingRaw,
      mode,
      contextMenuInstructions,
      customModePrompts,
      customSupportPrompts,
      codebaseIndexConfig,
      codebaseIndexModels,
      joyCoderEnv,
      joyCoderBaseUrl,
    ] = await Promise.all([
      this.getStateByName('apiProvider') as Promise<ApiProvider | undefined>,
      this.getStateByName('apiModelId') as Promise<string | undefined>,
      this.getSecret('apiKey') as Promise<string | undefined>,
      this.getStateByName('openAiBaseUrl') as Promise<string | undefined>,
      this.getSecret('openAiApiKey') as Promise<string | undefined>,
      this.getStateByName('openAiModelId') as Promise<string | undefined>,
      this.getGlobalState('lastShownAnnouncementId') as Promise<string | undefined>,
      this.getGlobalState('customInstructions') as Promise<string | undefined>,
      this.getGlobalState(this.getTaskHistoryKey()) as Promise<HistoryItem[] | undefined>,
      this.getGlobalState('autoApprovalSettings') as Promise<AutoApprovalSettings | undefined>,
      this.getGlobalState('browserSettings') as Promise<BrowserSettings | undefined>,
      this.getGlobalState('chatSettings') as Promise<ChatSettings | undefined>,
      this.getGlobalState('vsCodeLmModelSelector') as Promise<vscode.LanguageModelChatSelector | undefined>,
      this.getSecret('authToken') as Promise<string | undefined>,
      this.getGlobalState('previousModeApiProvider') as Promise<ApiProvider | undefined>,
      this.getGlobalState('previousModeModelId') as Promise<string | undefined>,
      this.getGlobalState('previousModeModelInfo') as Promise<ModelInfo | undefined>,
      this.getGlobalState('diffEnabled') as Promise<boolean | undefined>,
      this.getGlobalState('JoyCode.autoCode.writeAction') as Promise<string | undefined>,
      this.getGlobalState('planActSeparateModelsSetting') as Promise<boolean | undefined>,
      this.getStateByName('mode') as Promise<Mode | undefined>,
      this.getGlobalState('contextMenuInstructions') as Promise<string | undefined>,
      this.getGlobalState('customModePrompts') as Promise<CustomModePrompts | undefined>,
      this.getGlobalState('customSupportPrompts') as Promise<CustomSupportPrompts | undefined>,
      this.getGlobalState('codebaseIndexConfig') as Promise<CodebaseIndexConfig | undefined>,
      this.getGlobalState('codebaseIndexModels') as Promise<CodebaseIndexModels | undefined>,
      this.getGlobalState('joyCoderEnv') as Promise<string | undefined>,
      this.getGlobalState('joyCoderBaseUrl') as Promise<string | undefined>,
    ]);
    let planActSeparateModelsSetting: boolean | undefined = undefined;
    if (planActSeparateModelsSettingRaw === true || planActSeparateModelsSettingRaw === false) {
      planActSeparateModelsSetting = planActSeparateModelsSettingRaw;
    } else {
      // default to true for existing users
      if (storedApiProvider) {
        planActSeparateModelsSetting = true;
      } else {
        // default to false for new users
        planActSeparateModelsSetting = false;
      }
      // this is a special case where it's a new state, but we want it to default to different values for existing and new users.
      // persist so next time state is retrieved it's set to the correct value.
      await this.updateGlobalState('planActSeparateModelsSetting', planActSeparateModelsSetting);
    }
    let apiProvider = 'openai' as ApiProvider;
    // let apiProvider: ApiProvider = apiModelId?.toLocaleLowerCase()?.includes('gemini') ? 'gemini' : 'openai';

    return {
      apiConfiguration: {
        apiProvider,
        apiModelId,
        apiKey,
        openAiBaseUrl,
        openAiApiKey,
        openAiModelId,
        vsCodeLmModelSelector,
      },
      lastShownAnnouncementId,
      customInstructions,
      joyCoderEnv,
      joyCoderBaseUrl,
      taskHistory,
      autoApprovalSettings: autoApprovalSettings || DEFAULT_AUTO_APPROVAL_SETTINGS, // default value can be 0 or empty string
      browserSettings: browserSettings || DEFAULT_BROWSER_SETTINGS,
      chatSettings: chatSettings || ({ mode } as ChatSettings),
      authToken,
      previousModeApiProvider,
      previousModeModelId,
      previousModeModelInfo,
      diffEnabled: diffEnabled ?? true,
      writeAction,
      fuzzyMatchThreshold: 1.0,
      planActSeparateModelsSetting,
      customModes,
      mode: mode ?? defaultModeSlug,
      contextMenuInstructions,
      customModePrompts,
      customSupportPrompts,
      codebaseIndexConfig: codebaseIndexConfig || DEFAULT_CODEBASE_INDEX_CONFIG,
      codebaseIndexModels: codebaseIndexModels || EMBEDDING_MODEL_PROFILES,
    };
  }

  async updateTaskHistory(item: HistoryItem): Promise<HistoryItem[]> {
    const taskHistoryKey = this.getTaskHistoryKey();
    const history = ((await this.getGlobalState(taskHistoryKey)) as HistoryItem[]) || [];
    try {
      const existingItemIndex = history.findIndex((h) => h?.id === item?.id);
      if (existingItemIndex !== -1) {
        history[existingItemIndex] = item;
      } else {
        history.push(item);
      }
      await this.updateGlobalState(taskHistoryKey, history);
    } catch (error) {
      console.error('%c [ updateTaskHistory->error ]-1375', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      throw error;
    }
    return history;
  }

  // global

  async updateGlobalState(key: GlobalStateKey | string, value: any) {
    await this.context.globalState.update(key, value);
  }

  async getGlobalState(key: GlobalStateKey | string) {
    return await this.context.globalState.get(key);
  }

  // workspace
  async updateWorkspaceState(key: string, value: any) {
    await this.context.workspaceState.update(key, value);
  }

  async getWorkspaceState(key: string) {
    return await this.context.workspaceState.get(key);
  }

  // secrets
  async storeSecret(key: SecretKey, value?: string) {
    if (value) {
      await this.context.secrets.store(key, value);
    } else {
      await this.context.secrets.delete(key);
    }
  }

  async getSecret(key: SecretKey) {
    return await this.context.secrets.get(key);
  }

  // dev
  async resetState() {
    // vscode.window.showInformationMessage('Resetting state...');
    for (const key of this.context.globalState.keys()) {
      await this.context.globalState.update(key, undefined);
    }
    const secretKeys: SecretKey[] = ['apiKey', 'authToken'];
    for (const key of secretKeys) {
      await this.storeSecret(key, undefined);
    }
    if (this.getCurrentJoyCoder()) {
      // this.joycoder.abortTask();
      this.getCurrentJoyCoder()?.abortTask();
      this.removeJoyCoderFromStack();
      // this.joycoder = undefined;
    }
    // vscode.window.showInformationMessage('State reset');
    await this.postStateToWebview();
    await this.postMessageToWebview({
      type: 'action',
      action: 'chatButtonClicked',
    });
  }
  async refreshTotalTasksSize() {
    getTotalTasksSize(this.context)
      .then((newTotalSize) => {
        this.postMessageToWebview({
          type: 'totalTasksSize',
          totalTasksSize: newTotalSize,
        });
      })
      .catch((error) => {
        console.error('Error calculating total tasks size:', error);
      });
  }
  /**
   * Handle switching to a new mode, including updating the associated API configuration
   * @param newMode The mode to switch to
   */
  public async handleModeSwitch(newMode: Mode) {
    const modeMap: Record<string, ChatSettings['mode']> = {
      architect: 'architect',
      orchestrator: 'orchestrator',
      ask: 'ask',
      chat: 'chat',
      debug: 'debug',
      code: 'code',
    };

    const chatSettings = {
      mode: modeMap[newMode] || newMode,
    };
    const { autoApprovalSettings } = await this.getState();
    this.getCurrentJoyCoder()?.updateAutoApprovalSettings(autoApprovalSettings);
    await this.updateGlobalState('chatSettings', chatSettings);
    // await this.updateGlobalState('mode', newMode);
    await this.updateStateByName('mode', newMode);
    // Send only the necessary state updates instead of the entire state
    // This prevents unnecessary re-rendering of the chat messages
    this.postMessageToWebview({
      type: 'modeUpdate',
      mode: newMode,
      chatSettings: chatSettings,
    });
  }

  /**
   * 处理外部命令调用
   * @param params 命令参数
   */
  async handleExternalCommand(params: { mode?: string; text?: string }): Promise<void> {
    // 确保webview已加载
    await this.postMessageToWebview({
      type: 'action',
      action: 'chatButtonClicked',
    });

    // 处理模式切换
    if (params.mode) {
      await this.handleModeSwitch(params.mode);

      // 通知webview更新模式
      await this.postMessageToWebview({
        type: 'updateCoderMode',
        mode: params.mode,
      });
    }

    // 处理文本输入
    if (params.text) {
      // 检查是否已有JoyCoder实例，如果有则直接发送消息响应，避免重复创建任务
      const currentJoyCoder = this.getCurrentJoyCoder();
      if (currentJoyCoder) {
        // 直接调用JoyCoder的handleWebviewAskResponse方法，避免触发新任务创建
        currentJoyCoder.handleWebviewAskResponse('messageResponse', params.text);
      } else {
        // 如果没有JoyCoder实例，创建新任务而不是发送sendMessage避免循环调用
        await this.initJoyCoderWithTask(params.text);
      }
    }
  }
  async createAgent(prompt: string) {
    try {
      const agentInfo = this.joycoder?.createAgent(prompt);
      return agentInfo;
    } catch (error) {
      return undefined;
    }
  }

  get cwd(): string | undefined {
    const workspacePath = getWorkspacePath();
    if (!workspacePath) {
      return undefined;
    }
    // Convert vscode.Uri to string if needed
    return workspacePath instanceof vscode.Uri ? workspacePath.fsPath : workspacePath;
  }
}
