export function checkIsOpenRouterContextWindowError(error: any): boolean {
  return (
    error.message?.includes('context length') ||
    error?.message.includes(`模型请求失败`) ||
    error?.message.includes(`上下文输出超长`) ||
    error?.message.includes(`Input is too long for requested model`)
  );
}

export function checkIsAnthropicContextWindowError(response: any): boolean {
  return (
    response?.error?.error?.type === 'invalid_request_error' &&
    response?.error?.error?.message?.includes('prompt is too long')
  );
}
