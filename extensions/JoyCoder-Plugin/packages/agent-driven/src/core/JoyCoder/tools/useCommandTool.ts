import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { showSystemNotification } from '../../../integrations/notifications';
import { COMMAND_REQ_APP_STRING } from '../../../shared/combineCommandSequences';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useCommandTool(joyCoder: JoyCoder, block: ToolUse) {
  const command: string | undefined = block.params.command;

  const requiresApprovalRaw: string | undefined = block.params.requires_approval;
  const requiresApproval = requiresApprovalRaw?.toLowerCase() === 'true';

  try {
    if (block.partial) {
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        // since depending on an upcoming parameter, requiresApproval this may become an ask - we cant partially stream a say prematurely. So in this particular case we have to wait for the requiresApproval parameter to be completed before presenting it.
        // await joyCoder.say(
        // 	"command",
        // 	removeClosingTag(joyCoder,"command", command,block.partical),
        // 	undefined,
        // 	block.partial,
        // ).catch(() => {})
      } else {
        // don't need to remove last partial since we couldn't have streamed a say
        await joyCoder
          .ask('command', removeClosingTag(joyCoder, 'command', command, block.partial), block.partial)
          .catch(() => {});
      }
      return;
    } else {
      if (!command) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_command', 'command'));

        return;
      }
      if (!requiresApprovalRaw) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_command', 'requires_approval')
        );
        await joyCoder.saveCheckpoint();
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      const ignoredFileAttemptedToAccess = joyCoder.JoyCoderIgnoreController.validateCommand(command);
      if (ignoredFileAttemptedToAccess) {
        await joyCoder.say('joycoderignore_error', ignoredFileAttemptedToAccess);
        await pushToolResult(
          joyCoder,
          block,
          formatResponse.toolError(formatResponse.joycoderIgnoreError(ignoredFileAttemptedToAccess))
        );

        return;
      }

      let didAutoApprove = false;

      if (!requiresApproval && joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'command');
        await joyCoder.say('command', command, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
        didAutoApprove = true;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(joyCoder, `JoyCode 想要执行一个命令: ${command}`);
        // joyCoder.removeLastPartialMessageIfExistsWithType("say", "command")
        const didApprove = await askApproval(
          joyCoder,
          block,
          'command',
          command + `${joyCoder.shouldAutoApproveTool(block.name) && requiresApproval ? COMMAND_REQ_APP_STRING : ''}`, // ugly hack until we refactor combineCommandSequences
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }

      let timeoutId: NodeJS.Timeout | undefined;
      if (didAutoApprove && joyCoder.autoApprovalSettings.enableNotifications) {
        // if the command was auto-approved, and it's long running we need to notify the user after some time has passed without proceeding
        timeoutId = setTimeout(() => {
          showSystemNotification({
            subtitle: 'Command is still running',
            message: 'An auto-approved command has been running for 30s, and may need your attention.',
          });
        }, 30_000);
      }

      const [userRejected, result] = await joyCoder.executeCommandTool(command);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (userRejected) {
        joyCoder.didRejectTool = true;
      }

      // Re-populate file paths in case the command modified the workspace (vscode listeners do not trigger unless the user manually creates/deletes files)
      joyCoder.providerRef.deref()?.workspaceTracker?.populateFilePaths();

      await pushToolResult(joyCoder, block, result);

      await joyCoder.saveCheckpoint();

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['executing command'], error);
    await joyCoder.saveCheckpoint();
    return;
  }
}
