import { fetchInstructions } from '../../prompts/instructions/instructions';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { formatResponse } from '../../prompts/responses';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { pushToolResult, askApproval, handleError } from './common';

export async function fetchInstructionsTool(joyCoder: JoyCoder, block: ToolUse) {
  const task: string | undefined = block.params.task;
  const sharedMessageProps: JoyCoderSayTool = { tool: 'fetchInstructions', content: task };

  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({ ...sharedMessageProps, content: undefined } satisfies JoyCoderSayTool);
      await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      return;
    } else {
      if (!task) {
        joyCoder.consecutiveMistakeCount++;
        // joyCoder.recordToolError('fetch_instructions');
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('fetch_instructions', 'task')
        );
        return;
      }

      joyCoder.consecutiveMistakeCount = 0;

      const completeMessage = JSON.stringify({ ...sharedMessageProps, content: task } satisfies JoyCoderSayTool);
      if (!joyCoder.shouldAutoApproveTool(block.name)) {
        const didApprove = await askApproval(joyCoder, block, 'tool', completeMessage);
        if (!didApprove) {
          return;
        }
      }
      // Bow fetch the content and provide it to the agent.
      const provider = joyCoder.providerRef.deref();
      const mcpHub = provider?.mcpHub;

      if (!mcpHub) {
        console.warn('%c [ MCP hub not available ]-45', 'font-size:13px; background:pink; color:#bf2c9f;', mcpHub);
        // throw new Error('MCP hub not available');
      }

      const diffStrategy = joyCoder.diffStrategy;
      const context = provider?.context;
      const content = await fetchInstructions(task, { mcpHub, diffStrategy, context });

      if (!content) {
        await pushToolResult(joyCoder, block, formatResponse.toolError(`Invalid instructions request: ${task}`));
        return;
      }

      await pushToolResult(joyCoder, block, content);

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, '获取指令', error);
  }
}
