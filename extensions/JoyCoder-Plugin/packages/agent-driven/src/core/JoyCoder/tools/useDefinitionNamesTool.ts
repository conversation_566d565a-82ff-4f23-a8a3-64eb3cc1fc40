import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { parseSourceCodeForDefinitionsTopLevel } from '../../../services/tree-sitter';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { getReadablePath } from '../../../utils/path';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useDefinitionNamesTool(joyCoder: JoyCoder, block: ToolUse) {
  const relDirPath: string | undefined = block.params.path;
  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'listCodeDefinitionNames',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relDirPath, block.partial)),
  };
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        ...sharedMessageProps,
        content: '',
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      return;
    } else {
      if (!relDirPath) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_definition_names', 'path')
        );

        return;
      }

      joyCoder.consecutiveMistakeCount = 0;

      const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relDirPath);
      // const absolutePath = vscode.Uri.joinPath(cwd, relDirPath);
      const result = await parseSourceCodeForDefinitionsTopLevel(absolutePath, joyCoder.JoyCoderIgnoreController);

      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        content: result,
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(
          joyCoder,
          `JoyCode 想要查看源代码定义 ${FileSystemHelper.basename(absolutePath)}/`
        );
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'tool',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      await pushToolResult(joyCoder, block, result);

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['parsing source code definitions'], error);

    return;
  }
}
