import delay from 'delay';
import { getModeBySlug, defaultModeSlug } from '../../../../web-agent/src/utils/modes';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, askApproval, handleError } from './common';

export async function switchModeTool(joyCoder: JoyCoder, block: ToolUse) {
  const mode_slug: string | undefined = block.params.mode_slug;
  const reason: string | undefined = block.params.reason;

  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        tool: 'switchMode',
        mode: removeClosingTag(joyCoder, 'mode_slug', mode_slug, block.partial),
        reason: removeClosingTag(joyCoder, 'reason', reason, block.partial),
      });

      await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      return;
    } else {
      if (!mode_slug) {
        joyCoder.consecutiveMistakeCount++;
        // joyCoder.recordToolError('switch_mode');
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('switch_mode', 'mode_slug'));
        return;
      }

      joyCoder.consecutiveMistakeCount = 0;

      // Verify the mode exists
      const targetMode = getModeBySlug(mode_slug, (await joyCoder.providerRef.deref()?.getState())?.customModes);

      if (!targetMode) {
        // joyCoder.recordToolError('switch_mode');
        await pushToolResult(joyCoder, block, formatResponse.toolError(`Invalid mode: ${mode_slug}`));
        return;
      }

      // Check if already in requested mode
      const currentMode = (await joyCoder.providerRef.deref()?.getState())?.mode ?? defaultModeSlug;

      if (currentMode === mode_slug) {
        await pushToolResult(joyCoder, block, `Already in ${targetMode.name} mode.`);
        return;
      }

      const completeMessage = JSON.stringify({ tool: 'switchMode', mode: mode_slug, reason });
      if (!joyCoder.shouldAutoApproveTool(block.name)) {
        const didApprove = await askApproval(joyCoder, block, 'tool', completeMessage);
        if (!didApprove) {
          return;
        }
      }

      // Switch the mode using shared handler
      await joyCoder.providerRef.deref()?.handleModeSwitch(mode_slug);

      await pushToolResult(
        joyCoder,
        block,
        `Successfully switched from ${getModeBySlug(currentMode)?.name ?? currentMode} mode to ${targetMode.name} mode${
          reason ? ` because: ${reason}` : ''
        }.`
      );

      await delay(500); // Delay to allow mode change to take effect before next tool is executed

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'switching mode', error);
    return;
  }
}
