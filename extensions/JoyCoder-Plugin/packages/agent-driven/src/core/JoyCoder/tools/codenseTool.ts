import { showSystemNotification } from '../../../integrations/notifications';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError } from './common';

export async function codenseTool(joyCoder: JoyCoder, block: ToolUse) {
  const context: string | undefined = block.params.context;
  try {
    if (block.partial) {
      await joyCoder
        .ask('condense', removeClosingTag(joyCoder, 'context', context, block.partial), block.partial)
        .catch(() => {});
      return;
    } else {
      if (!context) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('condense', 'context'));
        await joyCoder.saveCheckpoint();
        return;
      }
      joyCoder.consecutiveMistakeCount = 0;

      if (joyCoder.autoApprovalSettings.enabled && joyCoder.autoApprovalSettings.enableNotifications) {
        showSystemNotification({
          subtitle: 'JoyCode wants to condense the conversation...',
          message: `JoyCode is suggesting to condense your conversation with: ${context}`,
        });
      }

      const { text, images } = await joyCoder.ask('condense', context, false);

      // If the user provided a response, treat it as feedback
      if (text || images?.length) {
        await joyCoder.say('user_feedback', text ?? '', images);
        await pushToolResult(
          joyCoder,
          block,
          formatResponse.toolResult(
            `The user provided feedback on the condensed conversation summary:\n<feedback>\n${text}\n</feedback>`,
            images
          )
        );
      } else {
        // If no response, the user accepted the condensed version
        await pushToolResult(joyCoder, block, formatResponse.toolResult(formatResponse.condense()));

        const lastMessage = joyCoder.apiConversationHistory[joyCoder.apiConversationHistory.length - 1];
        const summaryAlreadyAppended = lastMessage && lastMessage.role === 'assistant';
        const keepStrategy = summaryAlreadyAppended ? 'lastTwo' : 'none';

        // clear the context history at this point in time
        joyCoder.conversationHistoryDeletedRange = joyCoder.contextManager.getNextTruncationRange(
          joyCoder.apiConversationHistory,
          joyCoder.conversationHistoryDeletedRange,
          keepStrategy
        );
        await joyCoder.saveJoyCoderMessages();
        await joyCoder.contextManager.triggerApplyStandardContextTruncationNoticeChange(
          Date.now(),
          await joyCoder.ensureTaskDirectoryExists()
        );
      }
      await joyCoder.saveCheckpoint();
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, '压缩上下文窗口', error);
    await joyCoder.saveCheckpoint();
    return;
  }
}
