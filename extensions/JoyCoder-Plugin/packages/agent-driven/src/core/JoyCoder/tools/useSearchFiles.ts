import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { regexSearchFiles } from '../../../services/ripgrep';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { getReadablePath } from '../../../utils/path';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useSearchFiles(joyCoder: JoyCoder, block: ToolUse) {
  const relDirPath: string | undefined = block.params.path;
  const regex: string | undefined = block.params.regex;
  const filePattern: string | undefined = block.params.file_pattern;
  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'searchFiles',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relDirPath, block.partial)),
    regex: removeClosingTag(joyCoder, 'regex', regex, block.partial),
    filePattern: removeClosingTag(joyCoder, 'file_pattern', filePattern, block.partial),
  };
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        ...sharedMessageProps,
        content: '',
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      return;
    } else {
      if (!relDirPath) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_search_files', 'path'));

        return;
      }
      if (!regex) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_search_files', 'regex')
        );

        return;
      }
      joyCoder.consecutiveMistakeCount = 0;
      const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relDirPath);
      const results = await regexSearchFiles(
        joyCoder.cwd,
        FileSystemHelper.getRemotePath(absolutePath),
        regex,
        filePattern,
        joyCoder.JoyCoderIgnoreController
      );

      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        content: results,
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(
          joyCoder,
          `JoyCode 想要搜索文件在 ${FileSystemHelper.basename(absolutePath)}/`
        );
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'tool',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      await pushToolResult(joyCoder, block, results);

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['searching files'], error);

    return;
  }
}
