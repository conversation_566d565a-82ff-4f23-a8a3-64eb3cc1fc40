import { getBaseUrl, getJdhLoginInfo, isIDE } from '@joycoder/shared';
import to from 'await-to-js';
import axios from 'axios';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { removeClosingTag, pushToolResult, handleError } from './common';

export async function useWebSearchTool(joyCoder: JoyCoder, block: ToolUse) {
  const query: string | undefined = block.params.query;
  const sharedMessageProps: JoyCoderSayTool = {
    tool: 'webSearch',
    query: removeClosingTag(joyCoder, 'query', query, block.partial),
  };
  if (block.partial) {
    const partialMessage = JSON.stringify({
      ...sharedMessageProps,
      content: undefined,
    } as JoyCoderSayTool);
    await joyCoder.say('tool', partialMessage, undefined, block.partial);
    return;
  } else {
    try {
      const query: string | undefined = block?.params?.query;
      if (query) {
        await joyCoder.say('tool', JSON.stringify(sharedMessageProps), undefined, false);
        const ideUrl = getBaseUrl() + '/api/saas/openai/v1/web-search';
        const pluginUrl = 'http://jdhgpt.jd.com/v1/web-search';
        const jdhLoginInfo = getJdhLoginInfo();
        const data = {
          messages: [
            {
              role: 'user',
              content: query,
            },
          ],
          stream: false,
          model: 'search_pro_jina',
          userName: jdhLoginInfo?.userName || '',
          userToken: jdhLoginInfo?.userToken || '',
        };
        const [thrown, response]: [thrown: any, response: any] = await to(
          axios({
            method: 'post',
            url: isIDE() ? ideUrl : pluginUrl,
            headers: {
              'Content-Type': 'application/json',
              ptKey: jdhLoginInfo?.ptKey,
            },
            data,
            timeout: 5 * 60 * 1000,
          })
        );
        if (response) {
          try {
            const content = JSON.stringify(response.data.search_result);
            await pushToolResult(joyCoder, block, content);
          } catch (error) {
            await pushToolResult(joyCoder, block, '(use_web_search tool does not return anything)');
          }
        }
      } else {
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_web_search', 'query'));
      }
      return;
    } catch (error) {
      await handleError(joyCoder, block, '联网搜索异常', error);
      return;
    }
  }
}
