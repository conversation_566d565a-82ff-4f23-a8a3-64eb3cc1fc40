import '../../utils/path'; // Import to ensure String.prototype.toPosix is available
import * as vscode from 'vscode';

import CheckpointTracker from '../../integrations/checkpoints/CheckpointTracker';
import { combineApiRequests } from '../../shared/combineApiRequests';
import { combineCommandSequences } from '../../shared/combineCommandSequences';
import { JoyCoderApiReqInfo } from '../../shared/ExtensionMessage';
import { getApiMetrics } from '../../shared/getApiMetrics';
import { JoyCoderCheckpointRestore } from '../../shared/WebviewMessage';
import { JoyCoderRestoreMessageMap } from '../../adaptor/translate/message';
import { JoyCoder } from '../Joycoder';

export async function restoreCheckpoint(jc: JoyCoder, messageTs: number, restoreType: JoyCoderCheckpointRestore) {
  const messageIndex = jc.JoyCoderMessages.findIndex((m) => m.ts === messageTs);
  const message = jc.JoyCoderMessages[messageIndex];
  if (!message) {
    console.error('Message not found', jc.JoyCoderMessages);
    return;
  }

  let didWorkspaceRestoreFail = false;

  switch (restoreType) {
    case 'task':
      break;
    case 'taskAndWorkspace':
    case 'workspace':
      if (!jc.checkpointTracker) {
        try {
          jc.checkpointTracker = await CheckpointTracker.create(
            jc.taskId,
            jc.providerRef.deref()?.context.globalStorageUri.fsPath
          );
          jc.checkpointTrackerErrorMessage = undefined;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          console.warn('Failed to initialize checkpoint tracker:', errorMessage);
          jc.checkpointTrackerErrorMessage = errorMessage;
          await jc.providerRef.deref()?.postStateToWebview();
          vscode.window.showErrorMessage(errorMessage);
          didWorkspaceRestoreFail = true;
        }
      }
      if (message.lastCheckpointHash && jc.checkpointTracker) {
        try {
          await jc.checkpointTracker.resetHead(message.lastCheckpointHash);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          vscode.window.showErrorMessage('Failed to restore checkpoint: ' + errorMessage);
          didWorkspaceRestoreFail = true;
        }
      }
      break;
  }

  if (!didWorkspaceRestoreFail) {
    switch (restoreType) {
      case 'task':
      case 'taskAndWorkspace':
        jc.conversationHistoryDeletedRange = message.conversationHistoryDeletedRange;
        const newConversationHistory = jc.apiConversationHistory.slice(0, (message.conversationHistoryIndex || 0) + 2); // +1 since this index corresponds to the last user message, and another +1 since slice end index is exclusive
        await jc.overwriteApiConversationHistory(newConversationHistory);

        // update the context history state
        await jc.contextManager.truncateContextHistory(message.ts, await jc.ensureTaskDirectoryExists());

        // aggregate deleted api reqs info so we don't lose costs/tokens
        const deletedMessages = jc.JoyCoderMessages.slice(messageIndex + 1);
        const deletedApiReqsMetrics = getApiMetrics(combineApiRequests(combineCommandSequences(deletedMessages)));

        const newJoyCoderMessages = jc.JoyCoderMessages.slice(0, messageIndex + 1);
        await jc.overwriteJoyCoderMessages(newJoyCoderMessages); // calls saveJoyCoderMessages which saves historyItem

        await jc.say(
          'deleted_api_reqs',
          JSON.stringify({
            tokensIn: deletedApiReqsMetrics.totalTokensIn,
            tokensOut: deletedApiReqsMetrics.totalTokensOut,
            cacheWrites: deletedApiReqsMetrics.totalCacheWrites,
            cacheReads: deletedApiReqsMetrics.totalCacheReads,
            cost: deletedApiReqsMetrics.totalCost,
          } as JoyCoderApiReqInfo)
        );
        break;
      case 'workspace':
        break;
    }

    switch (restoreType) {
      case 'task':
        vscode.window.showInformationMessage(
          JoyCoderRestoreMessageMap['Task messages have been restored to the checkpoint']
        );
        break;
      case 'workspace':
        vscode.window.showInformationMessage(
          JoyCoderRestoreMessageMap['Workspace files have been restored to the checkpoint']
        );
        break;
      case 'taskAndWorkspace':
        vscode.window.showInformationMessage(
          JoyCoderRestoreMessageMap['Task and workspace have been restored to the checkpoint']
        );
        break;
    }
    if (restoreType !== 'task') {
      // Set isCheckpointCheckedOut flag on the message
      // Find all checkpoint messages before this one
      const checkpointMessages = jc.JoyCoderMessages.filter((m) => m.say === 'checkpoint_created');
      const currentMessageIndex = checkpointMessages.findIndex((m) => m.ts === messageTs);

      // Set isCheckpointCheckedOut to false for all checkpoint messages
      checkpointMessages.forEach((m, i) => {
        m.isCheckpointCheckedOut = i === currentMessageIndex;
      });
    }

    await jc.saveJoyCoderMessages();

    await jc.providerRef.deref()?.postMessageToWebview({ type: 'relinquishControl' });

    jc.providerRef.deref()?.cancelTask(); // the task is already cancelled by the provider beforehand, but we need to re-init to get the updated messages
  } else {
    await jc.providerRef.deref()?.postMessageToWebview({ type: 'relinquishControl' });
  }
}
