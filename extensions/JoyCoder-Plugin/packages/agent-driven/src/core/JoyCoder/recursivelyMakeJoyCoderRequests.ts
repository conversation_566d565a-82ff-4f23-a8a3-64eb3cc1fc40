import '../../utils/path'; // Import to ensure String.prototype.toPosix is available
import { Anthropic } from '@anthropic-ai/sdk';
import * as vscode from 'vscode';
import delay from 'delay';
import pWaitFor from 'p-wait-for';
import CheckpointTracker from '../../integrations/checkpoints/CheckpointTracker';
import { formatContentBlockToMarkdown } from '../../integrations/misc/export-markdown';
import { showSystemNotification } from '../../integrations/notifications';
import { findLast, findLastIndex } from '../../shared/array';
import { JoyCoderApiReqCancelReason, JoyCoderApiReqInfo } from '../../shared/ExtensionMessage';
import { calculateApiCost } from '../../utils/cost';
import { parseAssistantMessage, ToolUseName } from '../assistant-message';
import { formatResponse } from '../prompts/responses';
import { JoyCoderRestoreMessageMap } from '../../adaptor/translate/message';
import { defaultModeSlug } from '../../../web-agent/src/utils/modes';
import { presentAssistantMessage } from '../JoyCoder/presentAssistantMessage';
import { JoyCoder, UserContent } from '../Joycoder';
import { ActionType, reportAction, WorkspaceState } from '@joycoder/shared';

export async function recursivelyMakeJoyCoderRequests(
  jc: JoyCoder,
  userContent: UserContent,
  includeFileDetails: boolean = false,
  cwd: string | vscode.Uri
): Promise<boolean> {
  if (jc.abort) {
    throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
  }

  const provider = jc.providerRef.deref();

  // Used to know what models were used in the task if user wants to export metadata for error reporting purposes
  const currentProviderId = (await provider?.getGlobalState('apiProvider')) as string;
  if (currentProviderId && jc.api.getModel().id) {
    try {
      const currentMode = (await provider?.getState())?.mode ?? defaultModeSlug;
      await jc.modelContextTracker.recordModelUsage(currentProviderId, jc.api.getModel().id, currentMode);
    } catch {}
  }

  if (jc.consecutiveMistakeCount >= 3) {
    // if (jc.autoApprovalSettings.enabled && jc.autoApprovalSettings.enableNotifications) {
    //   showSystemNotification({
    //     subtitle: 'Error',
    //     message: 'JoyCode is having trouble. Would you like to continue the task?',
    //   });
    // }
    const { apiConfiguration } = (await provider?.getState()) || {};
    const { response, text, images } = await jc.ask(
      'mistake_limit_reached',
      apiConfiguration?.apiModelId?.toLowerCase().includes('claude')
        ? JoyCoderRestoreMessageMap[
            `This may indicate a failure in his thought process or inability to use a tool properly, which can be mitigated with some user guidance (e.g. "Try breaking down the task into smaller steps").`
          ]
        : JoyCoderRestoreMessageMap[
            "JoyCode uses complex prompts and iterative task execution that may be challenging for less capable models. For best results, it's recommended to use Claude 3.5 Sonnet for its advanced agentic coding capabilities."
          ]
    );
    if (response === 'messageResponse') {
      userContent.push(
        ...[
          {
            type: 'text',
            text: formatResponse.tooManyMistakes(text),
          } as Anthropic.Messages.TextBlockParam,
          ...formatResponse.imageBlocks(images),
        ]
      );
      await jc.say('user_feedback', text, images);
    }
    jc.consecutiveMistakeCount = 0;
  }

  if (
    jc.autoApprovalSettings.enabled &&
    jc.consecutiveAutoApprovedRequestsCount >= jc.autoApprovalSettings.maxRequests
  ) {
    if (jc.autoApprovalSettings.enableNotifications) {
      showSystemNotification({
        subtitle: 'Max Requests Reached',
        message: `JoyCode has auto-approved ${jc.autoApprovalSettings.maxRequests.toString()} API requests.`,
      });
    }
    await jc.ask(
      'auto_approval_max_req_reached',
      // `JoyCode has auto-approved ${jc.autoApprovalSettings.maxRequests.toString()} API requests. Would you like to reset the count and proceed with the task?`,
      `JoyCode 已自动通过了 ${jc.autoApprovalSettings.maxRequests.toString()} 个 API 请求。您想要重置计数并继续执行任务吗？`
    );
    // if we get past the promise it means the user approved and did not start a new task
    jc.consecutiveAutoApprovedRequestsCount = 0;
  }

  // get previous api req's index to check token usage and determine if we need to truncate conversation history
  const previousApiReqIndex = findLastIndex(jc.JoyCoderMessages, (m) => m.say === 'api_req_started');

  // Save checkpoint if this is the first API request
  const isFirstRequest = jc.JoyCoderMessages.filter((m) => m.say === 'api_req_started').length === 0;
  // getting verbose details is an expensive operation, it uses globby to top-down build file structure of project which for large projects can take a few seconds
  // for the best UX we show a placeholder api_req_started message with a loading spinner as this happens
  await jc.say(
    'api_req_started',
    JSON.stringify({
      request:
        userContent.map((block) => formatContentBlockToMarkdown(block)).join('\n\n') +
        `\n\n${JoyCoderRestoreMessageMap['Loading']}...`,
    })
  );

  // Initialize checkpoint tracker first if enabled and it's the first request
  if (isFirstRequest && !jc.checkpointTracker) {
    try {
      // 创建一个Promise竞争
      const checkpointPromise = CheckpointTracker.create(
        jc.taskId,
        jc.providerRef.deref()?.context.globalStorageUri.fsPath
      );

      // 创建一个超时Promise
      const timeoutPromise = new Promise((resolve, _) => {
        setTimeout(() => {
          resolve(null);
        }, 15000); // 15秒超时
      });
      // 使用Promise.race来实现超时机制
      jc.checkpointTracker = (await Promise.race([checkpointPromise, timeoutPromise])) as CheckpointTracker;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Failed to initialize checkpoint tracker:', errorMessage);
    }
  }

  const lastCheckpointMessage = findLast(jc.JoyCoderMessages, (m) => m.say === 'checkpoint_created');
  if (isFirstRequest && jc.checkpointTracker && !lastCheckpointMessage) {
    await jc.say('checkpoint_created'); // no hash since we need to wait for CheckpointTracker to be initialized
  }

  // In this Cline request loop, we need to check if this task instance
  // has been asked to wait for a subtask to finish before continuing.

  if (jc.isPaused && provider) {
    await jc.waitForResume();
    const currentMode = (await provider.getState())?.mode ?? defaultModeSlug;

    if (currentMode !== jc.pausedModeSlug) {
      // The mode has changed, we need to switch back to the paused mode.
      await provider.handleModeSwitch(jc.pausedModeSlug);

      // Delay to allow mode change to take effect before next tool is executed.
      await delay(500);

      console.log(
        `[subtasks] task ${jc.taskId}.${jc.instanceId} has switched back to '${jc.pausedModeSlug}' from '${currentMode}'`
      );
    }
  }

  const [parsedUserContent, environmentDetails] = await jc.loadContext(userContent, includeFileDetails);
  userContent = parsedUserContent;
  // add environment details as its own text block, separate from tool results
  userContent.push({ type: 'text', text: environmentDetails });

  await jc.addToApiConversationHistory({
    role: 'user',
    content: userContent,
  });

  // use this opportunity to initialize the checkpoint tracker (can be expensive to initialize in the constructor)
  // FIXME: right now we're letting users init checkpoints for old tasks, but this could be a problem if opening a task in the wrong workspace
  // isNewTask &&
  if (!jc.checkpointTracker) {
    try {
      jc.checkpointTracker = await CheckpointTracker.create(
        jc.taskId,
        jc.providerRef.deref()?.context.globalStorageUri.fsPath
      );
      jc.checkpointTrackerErrorMessage = undefined;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : JoyCoderRestoreMessageMap['Unknown error'];
      console.warn('Failed to initialize checkpoint tracker:', errorMessage);
      jc.checkpointTrackerErrorMessage = errorMessage; // will be displayed right away since we saveJoyCoderMessages next which posts state to webview
    }
  }

  // Now that checkpoint tracker is initialized, update the dummy checkpoint_created message with the commit hash. (This is necessary since we use the API request loading as an opportunity to initialize the checkpoint tracker, which can take some time)
  if (isFirstRequest) {
    const commitHash = await jc.checkpointTracker?.commit();
    const lastCheckpointMessage = findLast(jc.JoyCoderMessages, (m) => m.say === 'checkpoint_created');
    if (lastCheckpointMessage) {
      lastCheckpointMessage.lastCheckpointHash = commitHash;
      await jc.saveJoyCoderMessages();
    }
  }

  // since we sent off a placeholder api_req_started message to update the webview while waiting to actually start the API request (to load potential details for example), we need to update the text of that message
  const lastApiReqIndex = findLastIndex(jc.JoyCoderMessages, (m) => m.say === 'api_req_started');
  jc.JoyCoderMessages[lastApiReqIndex].text = JSON.stringify({
    request: userContent.map((block) => formatContentBlockToMarkdown(block)).join('\n\n'),
  } as JoyCoderApiReqInfo);
  await jc.saveJoyCoderMessages();
  await jc.providerRef.deref()?.postStateToWebview();

  try {
    let cacheWriteTokens = 0;
    let cacheReadTokens = 0;
    let inputTokens = 0;
    let outputTokens = 0;
    let totalCost: number | undefined;
    let conversationId = jc.conversationId;

    // update api_req_started. we can't use api_req_finished anymore since it's a unique case where it could come after a streaming message (ie in the middle of being updated or executed)
    // fortunately api_req_finished was always parsed out for the gui anyways, so it remains solely for legacy purposes to keep track of prices in tasks from history
    // (it's worth removing a few months from now)
    const updateApiReqMsg = (cancelReason?: JoyCoderApiReqCancelReason, streamingFailedMessage?: string) => {
      jc.JoyCoderMessages[lastApiReqIndex].text = JSON.stringify({
        ...JSON.parse(jc.JoyCoderMessages[lastApiReqIndex].text || '{}'),
        tokensIn: inputTokens,
        tokensOut: outputTokens,
        cacheWrites: cacheWriteTokens,
        cacheReads: cacheReadTokens,
        cost:
          totalCost ??
          calculateApiCost(jc.api.getModel().info, inputTokens, outputTokens, cacheWriteTokens, cacheReadTokens),
        cancelReason,
        streamingFailedMessage,
      } as JoyCoderApiReqInfo);
    };

    const abortStream = async (cancelReason: JoyCoderApiReqCancelReason, streamingFailedMessage?: string) => {
      if (jc.diffViewProvider.isEditing) {
        await jc.diffViewProvider.revertChanges(); // closes diff view
      }

      // if last message is a partial we need to update and save it
      const lastMessage = jc.JoyCoderMessages.at(-1);
      if (lastMessage && lastMessage.partial) {
        // lastMessage.ts = Date.now() DO NOT update ts since it is used as a key for virtuoso list
        lastMessage.partial = false;
        // instead of streaming partialMessage events, we do a save and post like normal to persist to disk
        console.log('updating partial message', lastMessage);
        // await jc.saveJoyCoderMessages()·
      }

      // Let assistant know their response was interrupted for when task is resumed
      await jc.addToApiConversationHistory({
        role: 'assistant',
        content: [
          {
            type: 'text',
            text:
              assistantMessage +
              `\n\n[${
                cancelReason === 'streaming_failed'
                  ? 'Response interrupted by API Error'
                  : 'Response interrupted by user'
              }]`,
          },
        ],
      });

      // update api_req_started to have cancelled and cost, so that we can display the cost of the partial stream
      updateApiReqMsg(cancelReason, streamingFailedMessage);
      await jc.saveJoyCoderMessages();

      // signals to provider that it can retrieve the saved messages from disk, as abortTask can not be awaited on in nature
      jc.didFinishAbortingStream = true;
    };

    // reset streaming state
    jc.currentStreamingContentIndex = 0;
    jc.assistantMessageContent = [];
    jc.didCompleteReadingStream = false;
    jc.userMessageContent = [];
    jc.userMessageContentReady = false;
    jc.didRejectTool = false;
    jc.didAlreadyUseTool = false;
    jc.presentAssistantMessageLocked = false;
    jc.presentAssistantMessageHasPendingUpdates = false;
    jc.didAutomaticallyRetryFailedApiRequest = false;
    await jc.diffViewProvider.reset();

    const stream = jc.attemptApiRequest(previousApiReqIndex); // yields only if the first chunk is successful, otherwise will allow the user to retry the request (most likely due to rate limit error, which gets thrown on the first chunk)
    let assistantMessage = '';
    let reasoningMessage = '';
    jc.isStreaming = true;
    try {
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.AutoGenerator,
        question: userContent?.map((item: any) => item?.text)?.join('\n'),
        conversationId: conversationId,
        model: WorkspaceState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          modeType: (await provider?.getState())?.mode,
          taskId: jc.taskId,
          sessionId: jc.sessionId,
        },
      });
    } catch (error) {
      console.error('%c [ userAsk-reportAction-error ]-3991', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
    try {
      for await (const chunk of stream) {
        if (!chunk) {
          // Sometimes chunk is undefined, no idea that can cause it, but this workaround seems to fix it
          continue;
        }
        switch (chunk.type) {
          case 'usage':
            inputTokens += chunk.inputTokens;
            outputTokens += chunk.outputTokens;
            cacheWriteTokens += chunk.cacheWriteTokens ?? 0;
            cacheReadTokens += chunk.cacheReadTokens ?? 0;
            totalCost = chunk.totalCost;
            // conversationId = chunk.conversationId ?? '';
            break;
          case 'reasoning':
            // reasoning will always come before assistant message
            reasoningMessage += chunk.reasoning || chunk.reasoning_content;
            await jc.say('reasoning', reasoningMessage, undefined, true);
            break;
          case 'text':
            if (reasoningMessage && assistantMessage.length === 0) {
              // complete reasoning message
              await jc.say('reasoning', reasoningMessage, undefined, false);
            }
            assistantMessage += chunk.text;
            // conversationId = chunk.conversationId ?? '';
            // parse raw assistant message into content blocks
            const prevLength = jc.assistantMessageContent.length;

            jc.assistantMessageContent = parseAssistantMessage(assistantMessage, conversationId, userContent);
            if (jc.assistantMessageContent.length > prevLength) {
              jc.userMessageContentReady = false; // new content we need to present, reset to false in case previous content set this to true
            }
            // present content to user
            presentAssistantMessage(jc, conversationId, cwd, userContent);
            break;
        }

        if (jc.abort) {
          console.log('aborting stream...');
          if (!jc.abandoned) {
            // only need to gracefully abort if this instance isn't abandoned (sometimes openrouter stream hangs, in which case this would affect future instances of JoyCode)
            await abortStream('user_cancelled');
          }
          break; // aborts the stream
        }

        if (jc.didRejectTool) {
          // userContent has a tool rejection, so interrupt the assistant's response to present the user's feedback
          assistantMessage += '\n\n[Response interrupted by user feedback]';
          // jc.userMessageContentReady = true // instead of setting this premptively, we allow the present iterator to finish and set userMessageContentReady when its ready
          break;
        }

        // PREV: we need to let the request finish for openrouter to get generation details
        // UPDATE: it's better UX to interrupt the request at the cost of the api cost not being retrieved
        if (jc.didAlreadyUseTool) {
          assistantMessage +=
            '\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]';
          break;
        }
      }
    } catch (error) {
      // abandoned happens when extension is no longer waiting for the JoyCode instance to finish aborting (error is thrown here when any function in the for loop throws due to jc.abort)
      if (!jc.abandoned) {
        jc.abortTask(); // if the stream failed, there's various states the task could be in (i.e. could have streamed some tools the user may have executed), so we just resort to replicating a cancel task
        const errorMessage = jc.formatErrorWithStatusCode(error);
        await abortStream('streaming_failed', errorMessage);
        const history = await jc.providerRef.deref()?.getTaskWithId(jc.taskId);
        if (history) {
          await jc.providerRef.deref()?.initJoyCoderWithHistoryItem(history.historyItem);
          // await jc.providerRef.deref()?.postStateToWebview()
        }
      }
    } finally {
      jc.isStreaming = false;
    }

    // need to call here in case the stream was aborted
    if (jc.abort) {
      throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
    }

    jc.didCompleteReadingStream = true;

    // set any blocks to be complete to allow presentAssistantMessage to finish and set userMessageContentReady to true
    // (could be a text block that had no subsequent tool uses, or a text block at the very end, or an invalid tool use, etc. whatever the case, presentAssistantMessage relies on these blocks either to be completed or the user to reject a block in order to proceed and eventually set userMessageContentReady to true)
    const partialBlocks = jc.assistantMessageContent.filter((block) => block.partial);
    partialBlocks.forEach((block) => {
      block.partial = false;
    });
    // jc.assistantMessageContent.forEach((e) => (e.partial = false)) // cant just do this bc a tool could be in the middle of executing ()
    if (partialBlocks.length > 0) {
      presentAssistantMessage(jc, conversationId, cwd, userContent); // if there is content to update then it will complete and update jc.userMessageContentReady to true, which we pwaitfor before making the next request. all this is really doing is presenting the last partial message that we just set to complete
    }

    updateApiReqMsg();
    await jc.saveJoyCoderMessages();
    await jc.providerRef.deref()?.postStateToWebview();

    const getToolActionType = (toolName: ToolUseName) => {
      switch (toolName) {
        case 'use_codebase':
          return ActionType.Codebase;
        case 'use_web_search':
          return ActionType.WebSearch;
        case 'use_mcp_tools':
        case 'get_mcp_resource':
        case 'get_mcp_instructions':
          return ActionType.MCP;
        default:
          return ActionType.AutoGenerator;
      }
    };
    const toolName = jc.assistantMessageContent.filter((item) => item.type === 'tool_use')[0]?.name;
    reportAction({
      actionCate: 'ai',
      actionType: getToolActionType(toolName),
      question: userContent?.map((item: any) => item?.text)?.join('\n'),
      result: assistantMessage,
      conversationId: conversationId,
      model: WorkspaceState.get('openAiModelId'),
      startTime: new Date(),
      extendMsg: {
        modeType: (await provider?.getState())?.mode,
        taskId: jc.taskId,
        sessionId: jc.sessionId,
      },
    });
    // now add to apiconversationhistory
    // need to save assistant responses to file before proceeding to tool use since user can exit at any moment and we wouldn't be able to save the assistant's response
    let didEndLoop = false;
    if (assistantMessage.length > 0) {
      await jc.addToApiConversationHistory({
        role: 'assistant',
        content: [{ type: 'text', text: assistantMessage }],
      });

      // NOTE: this comment is here for future reference - this was a workaround for userMessageContent not getting set to true. It was due to it not recursively calling for partial blocks when didRejectTool, so it would get stuck waiting for a partial block to complete before it could continue.
      // in case the content blocks finished
      // it may be the api stream finished after the last parsed content block was executed, so  we are able to detect out of bounds and set userMessageContentReady to true (note you should not call presentAssistantMessage since if the last block is completed it will be presented again)
      // const completeBlocks = jc.assistantMessageContent.filter((block) => !block.partial) // if there are any partial blocks after the stream ended we can consider them invalid
      // if (jc.currentStreamingContentIndex >= completeBlocks.length) {
      // 	jc.userMessageContentReady = true
      // }

      await pWaitFor(() => jc.userMessageContentReady);

      // if the model did not tool use, then we need to tell it to either use a tool or attempt_task_done
      const didToolUse = jc.assistantMessageContent.some((block) => block.type === 'tool_use');

      if (!didToolUse) {
        // normal request where tool use is required
        jc.userMessageContent.push({
          type: 'text',
          text: formatResponse.noToolsUsed(),
        });
        jc.consecutiveMistakeCount++;
      }

      const recDidEndLoop = await recursivelyMakeJoyCoderRequests(jc, jc.userMessageContent, false, cwd);
      didEndLoop = recDidEndLoop;
    } else {
      // if there's no assistant_responses, that means we got no text or tool_use content blocks from API which we should assume is an error
      await jc.say(
        'error',
        JoyCoderRestoreMessageMap[
          "Unexpected API Response: The language model did not provide any assistant messages. This may indicate an issue with the API or the model's output."
        ]
      );
      await jc.addToApiConversationHistory({
        role: 'assistant',
        content: [
          {
            type: 'text',
            text: 'Failure: I did not provide a response.',
          },
        ],
      });

      // 增加错误计数以跟踪连续的API失败次数
      jc.consecutiveMistakeCount++;

      // 向用户消息内容添加错误反馈，用于下次请求
      jc.userMessageContent.push({
        type: 'text',
        text: '上一个API请求未能返回任何响应。请重试或检查API配置。',
      });

      // 递归重试请求并携带错误反馈
      const recDidEndLoop = await recursivelyMakeJoyCoderRequests(jc, jc.userMessageContent, false, cwd);
      didEndLoop = recDidEndLoop;
    }

    return didEndLoop; // will always be false for now
  } catch (error) {
    // this should never happen since the only thing that can throw an error is the attemptApiRequest, which is wrapped in a try catch that sends an ask where if noButtonClicked, will clear current task and destroy this instance. However to avoid unhandled promise rejection, we will end this loop which will end execution of this instance (see startTask)
    return true; // needs to be true so parent loop knows to end task
  }
}
