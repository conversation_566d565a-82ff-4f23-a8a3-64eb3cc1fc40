import * as vscode from 'vscode';
import { exec } from 'child_process';
import osName from 'os-name';
import { isRemoteEnvironment } from './fs';

// /​**​
//  * 获取远程系统 OS 名称
//  * @returns Promise<string> 操作系统名称（如 "Ubuntu 22.04.3 LTS"）
//  */
export async function getRemoteOSName(): Promise<string> {
  // 检查是否处于 SSH 远程环境
  if (!isRemoteEnvironment()) {
    return osName();
  }

  try {
    // 方法1：通过读取 /etc/os-release 文件
    const osName = await readOSReleaseFile();
    if (osName) return osName;

    // 方法2：执行 uname 命令作为备用方案
    return await executeUnameCommand();
  } catch (error) {
    return osName();
  }
}

// 解析 /etc/os-release 文件
async function readOSReleaseFile(): Promise<string> {
  const uri = vscode.Uri.file('/etc/os-release');
  const data = await vscode.workspace.fs.readFile(uri);
  const content = Buffer.from(data).toString('utf8');

  const match = content.match(/PRETTY_NAME="?(.*?)"?$/m);
  return match?.[1]?.trim() || '';
}

// 执行 uname 命令作为备用
function executeUnameCommand(): Promise<string> {
  return new Promise((resolve, reject) => {
    exec('uname -a', (error, stdout) => {
      if (error) reject(error);

      // 解析 uname 输出（示例：Linux ubuntu 5.15.0-86-generic...）
      const osInfo = stdout.trim().split(/\s+/);
      resolve(`${osInfo[0]} ${osInfo[2]}`); // 返回 Linux 内核信息
    });
  });
}
