import fs from 'fs/promises';
import * as path from 'path';
import * as vscode from 'vscode';
import { Uri } from 'vscode';
import { FileSystemHelper } from './FileSystemHelper';

/**
 * Asynchronously creates all non-existing subdirectories for a given file path
 * and collects them in an array for later deletion.
 *
 * @param filePath - The full path to a file.
 * @returns A promise that resolves to an array of newly created directories.
 */
export async function createDirectoriesForFile(filePath: Uri | string): Promise<string[]> {
  const newDirectories: string[] = [];
  if (isRemoteEnvironment()) {
    const newDirs = await createRemoteDirectories(filePath);
    return newDirs;
  } else {
    const normalizedFilePath = path.normalize(typeof filePath === 'string' ? filePath : filePath.path);
    const directoryPath = path.dirname(normalizedFilePath);
    let currentPath = directoryPath;
    const dirsToCreate: string[] = [];
    while (!(await fileExistsAtPath(currentPath))) {
      dirsToCreate.push(currentPath);
      currentPath = path.dirname(currentPath);
    }
    for (let i = dirsToCreate.length - 1; i >= 0; i--) {
      await fs.mkdir(dirsToCreate[i]);
      newDirectories.push(dirsToCreate[i]);
    }
  }
  return newDirectories;
}

async function createRemoteDirectories(filePath: string | Uri): Promise<string[]> {
  const newDirectories: string[] = [];
  const uri = typeof filePath === 'string' ? FileSystemHelper.getUri(filePath) : filePath;
  const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
  if (!workspaceFolder) return newDirectories;
  let currentUri = Uri.joinPath(uri, '..');
  const dirsToCreate: Uri[] = [];
  while (!(await fileExistsAtPath(currentUri))) {
    dirsToCreate.unshift(currentUri);
    const parentUri = Uri.joinPath(currentUri, '..');
    if (parentUri.toString() === currentUri.toString() || parentUri.toString() === workspaceFolder.uri.toString()) {
      break;
    }
    currentUri = parentUri;
  }
  for (const dirUri of dirsToCreate) {
    try {
      await vscode.workspace.fs.createDirectory(dirUri);
      newDirectories.push(vscode.workspace.asRelativePath(dirUri));
    } catch (error) {
      throw error;
    }
  }
  return newDirectories;
}
/**
 * Helper function to check if a path exists.
 *
 * @param path - The path to check.
 * @returns A promise that resolves to true if the path exists, false otherwise.
 */
export async function fileExistsAtPath(filePath: string | Uri): Promise<boolean> {
  const isRemote = isRemoteEnvironment();

  try {
    // 检查是否是远程环境
    if (isRemote) {
      // 在远程环境下，需要使用 Uri 格式访问文件
      let uri: Uri;

      // 统一使用 FileSystemHelper.getUri 来处理路径转换
      uri = typeof filePath === 'string' ? FileSystemHelper.getUri(filePath) : filePath;

      await vscode.workspace.fs.stat(uri);
      return true;
    } else {
      // 本地环境下直接使用 fs.access 检查文件
      const pathToCheck = typeof filePath === 'string' ? filePath : filePath.fsPath;
      await fs.access(pathToCheck);
      return true;
    }
  } catch (error) {
    // console.log(
    //   `[fileExistsAtPath] Path does not exist or cannot access: ${
    //     typeof filePath === 'string' ? filePath : filePath.toString()
    //   }, error: ${error instanceof Error ? error.message : String(error)}`
    // );
    return false;
  }
}

/**
 * 检查当前是否在远程环境
 */
export function isRemoteEnvironment(): boolean {
  return vscode.env.remoteName !== undefined;
}

/**
 * Checks if the path is a directory
 * @param filePath - The path to check.
 * @returns A promise that resolves to true if the path is a directory, false otherwise.
 */
export async function isDirectory(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    return stats.isDirectory();
  } catch {
    return false;
  }
}

/**
 * Gets the size of a file in kilobytes
 * @param filePath - Path to the file to check
 * @returns Promise<number> - Size of the file in KB, or 0 if file doesn't exist
 */
export async function getFileSizeInKB(filePath: string | Uri): Promise<number> {
  try {
    const stats =
      isRemoteEnvironment() && typeof filePath !== 'string'
        ? await vscode.workspace.fs.stat(filePath as Uri)
        : await fs.stat(filePath as string);
    const fileSizeInKB = stats.size / 1000; // Convert bytes to KB (decimal) - matches OS file size display
    return fileSizeInKB;
  } catch {
    return 0;
  }
}
