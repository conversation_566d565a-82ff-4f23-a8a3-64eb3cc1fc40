import path from 'path';
import getFolderSize from 'get-folder-size';
import { isRemoteEnvironment } from './fs';
import * as vscode from 'vscode';

/**
 * Gets the total size of tasks and checkpoints directories
 * @param storagePath The base storage path (typically globalStorageUri.fsPath)
 * @returns The total size in bytes, or null if calculation fails
 */
export async function getTotalTasksSize(context: vscode.ExtensionContext): Promise<number | null> {
  // const storagePath = isRemote() ? :
  const isRemote = isRemoteEnvironment();
  // Remove all contents of tasks directory
  let tasksDir: string;
  let checkpointsDir: string;
  if (isRemote) {
    const taskDirUri = vscode.Uri.joinPath(context.globalStorageUri, 'tasks');
    tasksDir = taskDirUri.toString();
    const checkpointsUri = vscode.Uri.joinPath(context.globalStorageUri, 'checkpoints');
    checkpointsDir = checkpointsUri.toString();
  } else {
    tasksDir = path.join(context.globalStorageUri.fsPath, 'tasks');
    checkpointsDir = path.join(context.globalStorageUri.fsPath, 'checkpoints');
  }

  try {
    const tasksSize = await getFolderSize.loose(tasksDir);
    const checkpointsSize = await getFolderSize.loose(checkpointsDir);
    return tasksSize + checkpointsSize;
  } catch (error) {
    console.error('Failed to calculate total task size:', error);
    return null;
  }
}
