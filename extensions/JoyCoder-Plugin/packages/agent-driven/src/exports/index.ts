import * as vscode from 'vscode';
import { JoyCoderProvider } from '../core/webview/JoycoderProvider';
import { JoyCoder<PERSON>I } from './JoyCoderAPI';

export function createJoyCoderAPI(sidebarProvider: JoyCoderProvider): JoyCoderAPI {
  const api: JoyCoderAPI = {
    setCustomInstructions: async (value: string) => {
      await sidebarProvider.updateCustomInstructions(value);
    },

    getCustomInstructions: async () => {
      return (await sidebarProvider.getGlobalState('customInstructions')) as string | undefined;
    },

    startNewTask: async (task?: string, images?: string[]) => {
      await sidebarProvider.clearTask();
      await sidebarProvider.removeJoyCoderFromStack();
      await sidebarProvider.postStateToWebview();
      await sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'chatButtonClicked',
      });
      await sidebarProvider.postMessageToWebview({
        type: 'invoke',
        invoke: 'sendMessage',
        text: task,
        images: images,
      });
    },

    sendMessage: async (message?: string, images?: string[]) => {
      await sidebarProvider.postMessageToWebview({
        type: 'invoke',
        invoke: 'sendMessage',
        text: message,
        images: images,
      });
    },

    pressPrimaryButton: async () => {
      await sidebarProvider.postMessageToWebview({
        type: 'invoke',
        invoke: 'primaryButtonClick',
      });
    },

    pressSecondaryButton: async () => {
      await sidebarProvider.postMessageToWebview({
        type: 'invoke',
        invoke: 'secondaryButtonClick',
      });
    },
  };

  return api;
}
