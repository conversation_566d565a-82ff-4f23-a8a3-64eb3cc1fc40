export type ApiProvider =
  // | 'anthropic'
  // | 'openrouter'
  // | 'bedrock'
  // | 'vertex'
  'gemini' | 'openai'; // 'o3-mini' | 'o1' | 'o1-preview' | 'o1-mini' | 'gpt-4o' | 'gpt-4o-mini' |
// 'gemini' |
// | 'ollama'
// | 'lmstudio'
// | 'openai-native'
// | 'requesty'
// | 'together'
// | 'deepseek'
// | 'qwen'
// | 'mistral'
// | 'vscode-lm'
// | 'litellm';

export interface ApiHandlerOptions {
  apiModelId?: string;
  apiKey?: string; // anthropic
  liteLlmBaseUrl?: string;
  liteLlmModelId?: string;
  anthropicBaseUrl?: string;
  openRouterApiKey?: string;
  openRouterModelId?: string;
  openRouterModelInfo?: ModelInfo;
  awsAccessKey?: string;
  awsSecretKey?: string;
  awsSessionToken?: string;
  awsRegion?: string;
  awsUseCrossRegionInference?: boolean;
  awsUseProfile?: boolean;
  awsProfile?: string;
  vertexProjectId?: string;
  vertexRegion?: string;
  openAiBaseUrl?: string;
  openAiApiKey?: string;
  openAiModelId?: string;
  ollamaModelId?: string;
  ollamaBaseUrl?: string;
  lmStudioModelId?: string;
  lmStudioBaseUrl?: string;
  geminiApiKey?: string;
  openAiNativeApiKey?: string;
  deepSeekApiKey?: string;
  requestyApiKey?: string;
  requestyModelId?: string;
  togetherApiKey?: string;
  togetherModelId?: string;
  qwenApiKey?: string;
  mistralApiKey?: string;
  azureApiVersion?: string;
  vsCodeLmModelSelector?: any;
  o3MiniReasoningEffort?: string;
  qwenApiLine?: string;
  geminiBaseUrl?: string;
  thinkingBudgetTokens?: number;
  extParams?: Record<string, any>; // Additional parameters for specific providers
}

export type ApiConfiguration = ApiHandlerOptions & {
  apiProvider?: ApiProvider;
};

// Models
interface PriceTier {
  tokenLimit: number; // Upper limit (inclusive) of *input* tokens for this price. Use Infinity for the highest tier.
  price: number; // Price per million tokens for this tier.
}
export interface ModelInfo {
  maxTokens?: number;
  contextWindow?: number;
  supportsImages?: boolean;
  supportsComputerUse?: boolean;
  supportsPromptCache: boolean; // this value is hardcoded for now
  inputPrice?: number; // Keep for non-tiered input models
  outputPrice?: number; // Keep for non-tiered output models
  thinkingConfig?: {
    maxBudget?: number; // Max allowed thinking budget tokens
    outputPrice?: number; // Output price per million tokens when budget > 0
    outputPriceTiers?: PriceTier[]; // Optional: Tiered output price when budget > 0
  };
  supportsGlobalEndpoint?: boolean; // Whether the model supports a global endpoint with Vertex AI
  cacheWritesPrice?: number;
  cacheReadsPrice?: number;
  description?: string;
  tiers?: {
    contextWindow: number;
    inputPrice?: number;
    outputPrice?: number;
    cacheWritesPrice?: number;
    cacheReadsPrice?: number;
  }[];
}

// Anthropic
// https://docs.anthropic.com/en/docs/about-claude/models // prices updated 2025-01-02
export type AnthropicModelId = keyof typeof anthropicModels;
export const anthropicDefaultModelId: AnthropicModelId = 'claude-3-5-sonnet-20241022';
export const anthropicModels = {
  'claude-3-5-sonnet-20241022': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: true,
    inputPrice: 3.0, // $3 per million input tokens
    outputPrice: 15.0, // $15 per million output tokens
    cacheWritesPrice: 3.75, // $3.75 per million tokens
    cacheReadsPrice: 0.3, // $0.30 per million tokens
  },
  'claude-3-5-haiku-20241022': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0.8,
    outputPrice: 4.0,
    cacheWritesPrice: 1.0,
    cacheReadsPrice: 0.08,
  },
  'claude-3-opus-20240229': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 15.0,
    outputPrice: 75.0,
    cacheWritesPrice: 18.75,
    cacheReadsPrice: 1.5,
  },
  'claude-3-haiku-20240307': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.25,
    outputPrice: 1.25,
    cacheWritesPrice: 0.3,
    cacheReadsPrice: 0.03,
  },
} as const satisfies Record<string, ModelInfo>; // as const assertion makes the object deeply readonly

// AWS Bedrock
// https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html
export type BedrockModelId = keyof typeof bedrockModels;
export const bedrockDefaultModelId: BedrockModelId = 'anthropic.claude-3-5-sonnet-20241022-v2:0';
export const bedrockModels = {
  'anthropic.claude-3-5-sonnet-20241022-v2:0': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: false,
    inputPrice: 3.0,
    outputPrice: 15.0,
  },
  'anthropic.claude-3-5-haiku-20241022-v1:0': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 1.0,
    outputPrice: 5.0,
  },
  'anthropic.claude-3-5-sonnet-20240620-v1:0': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3.0,
    outputPrice: 15.0,
  },
  'anthropic.claude-3-opus-20240229-v1:0': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15.0,
    outputPrice: 75.0,
  },
  'anthropic.claude-3-sonnet-20240229-v1:0': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3.0,
    outputPrice: 15.0,
  },
  'anthropic.claude-3-haiku-20240307-v1:0': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.25,
    outputPrice: 1.25,
  },
} as const satisfies Record<string, ModelInfo>;

// OpenRouter
// https://openrouter.ai/models?order=newest&supported_parameters=tools
export const openRouterDefaultModelId = 'anthropic/claude-3.5-sonnet'; // will always exist in openRouterModels
export const openRouterDefaultModelInfo: ModelInfo = {
  maxTokens: 8192,
  contextWindow: 200_000,
  supportsImages: true,
  supportsComputerUse: true,
  supportsPromptCache: true,
  inputPrice: 3.0,
  outputPrice: 15.0,
  cacheWritesPrice: 3.75,
  cacheReadsPrice: 0.3,
  description:
    'The new Claude 3.5 Sonnet delivers better-than-Opus capabilities, faster-than-Sonnet speeds, at the same Sonnet prices. Sonnet is particularly good at:\n\n- Coding: New Sonnet scores ~49% on SWE-Bench Verified, higher than the last best score, and without any fancy prompt scaffolding\n- Data science: Augments human data science expertise; navigates unstructured data while using multiple tools for insights\n- Visual processing: excelling at interpreting charts, graphs, and images, accurately transcribing text to derive insights beyond just the text alone\n- Agentic tasks: exceptional tool use, making it great at agentic tasks (i.e. complex, multi-step problem solving tasks that require engaging with other systems)\n\n#multimodal',
};

// Vertex AI
// https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude
export type VertexModelId = keyof typeof vertexModels;
export const vertexDefaultModelId: VertexModelId = 'claude-3-5-sonnet-v2@20241022';
export const vertexModels = {
  'claude-3-5-sonnet-v2@20241022': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsComputerUse: true,
    supportsPromptCache: false,
    inputPrice: 3.0,
    outputPrice: 15.0,
  },
  'claude-3-5-sonnet@20240620': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 3.0,
    outputPrice: 15.0,
  },
  'claude-3-5-haiku@20241022': {
    maxTokens: 8192,
    contextWindow: 200_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 1.0,
    outputPrice: 5.0,
  },
  'claude-3-opus@20240229': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15.0,
    outputPrice: 75.0,
  },
  'claude-3-haiku@20240307': {
    maxTokens: 4096,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.25,
    outputPrice: 1.25,
  },
} as const satisfies Record<string, ModelInfo>;

export const openAiModelInfoSaneDefaults: ModelInfo = {
  maxTokens: -1,
  contextWindow: 128_000,
  supportsImages: true,
  supportsPromptCache: false,
  inputPrice: 0,
  outputPrice: 0,
  supportsComputerUse: false,
};

// Gemini
// https://ai.google.dev/gemini-api/docs/models/gemini
export type GeminiModelId = keyof typeof geminiModels;
export const geminiDefaultModelId: GeminiModelId = 'gemini-2.0-flash-001';
export const geminiModels = {
  'gemini-2.5-pro-preview-05-06': {
    maxTokens: 65536,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15,
    cacheReadsPrice: 0.31,
    tiers: [
      {
        contextWindow: 200000,
        inputPrice: 1.25,
        outputPrice: 10,
        cacheReadsPrice: 0.31,
      },
      {
        contextWindow: Infinity,
        inputPrice: 2.5,
        outputPrice: 15,
        cacheReadsPrice: 0.625,
      },
    ],
  },
  'gemini-2.5-pro-preview-06-05': {
    maxTokens: 65536,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 2.5,
    outputPrice: 15,
    cacheReadsPrice: 0.31,
  },
  'gemini-2.5-flash-preview-05-20': {
    maxTokens: 65536,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    thinkingConfig: {
      maxBudget: 24576,
      outputPrice: 3.5,
    },
  },
  'gemini-2.5-flash-preview-04-17': {
    maxTokens: 65536,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15,
    outputPrice: 0.6,
    thinkingConfig: {
      maxBudget: 24576,
      outputPrice: 3.5,
    },
  },
  'gemini-2.0-flash-001': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.1,
    outputPrice: 0.4,
    cacheReadsPrice: 0.025,
    cacheWritesPrice: 1.0,
  },
  'gemini-2.0-flash-lite-preview-02-05': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-2.0-pro-exp-02-05': {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-2.0-flash-thinking-exp-01-21': {
    maxTokens: 65_536,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-2.0-flash-thinking-exp-1219': {
    maxTokens: 8192,
    contextWindow: 32_767,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-2.0-flash-exp': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-1.5-flash-002': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: true,
    inputPrice: 0.15, // Default price (highest tier)
    outputPrice: 0.6, // Default price (highest tier)
    cacheReadsPrice: 0.0375,
    cacheWritesPrice: 1.0,
    tiers: [
      {
        contextWindow: 128000,
        inputPrice: 0.075,
        outputPrice: 0.3,
        cacheReadsPrice: 0.01875,
      },
      {
        contextWindow: Infinity,
        inputPrice: 0.15,
        outputPrice: 0.6,
        cacheReadsPrice: 0.0375,
      },
    ],
  },
  'gemini-1.5-flash-exp-0827': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-1.5-flash-8b-exp-0827': {
    maxTokens: 8192,
    contextWindow: 1_048_576,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-1.5-pro-002': {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-1.5-pro-exp-0827': {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
  'gemini-exp-1206': {
    maxTokens: 8192,
    contextWindow: 2_097_152,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0,
    outputPrice: 0,
  },
} as const satisfies Record<string, ModelInfo>;

// OpenAI Native
// https://openai.com/api/pricing/
export type OpenAiNativeModelId = keyof typeof openAiNativeModels;
export const openAiNativeDefaultModelId: OpenAiNativeModelId = 'gpt-4o';
export const openAiNativeModels = {
  // 'o3-mini': {
  //   maxTokens: 100_000,
  //   contextWindow: 200_000,
  //   supportsImages: false,
  //   supportsPromptCache: false,
  //   inputPrice: 1.1,
  //   outputPrice: 4.4,
  // },
  // don't support tool use yet
  o1: {
    maxTokens: 100_000,
    contextWindow: 200_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15,
    outputPrice: 60,
  },
  'o1-preview': {
    maxTokens: 32_768,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 15,
    outputPrice: 60,
  },
  'o1-mini': {
    maxTokens: 65_536,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 1.1,
    outputPrice: 4.4,
  },
  'gpt-4o': {
    maxTokens: 4_096,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2.5,
    outputPrice: 10,
  },
  'gpt-4o-mini': {
    maxTokens: 16_384,
    contextWindow: 128_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.6,
  },
} as const satisfies Record<string, ModelInfo>;

// Azure OpenAI
// https://learn.microsoft.com/en-us/azure/ai-services/openai/api-version-deprecation
// https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#api-specs
export const azureOpenAiDefaultApiVersion = '2024-08-01-preview';

// DeepSeek
// https://api-docs.deepseek.com/quick_start/pricing
export type DeepSeekModelId = keyof typeof deepSeekModels;
export const deepSeekDefaultModelId: DeepSeekModelId = 'deepseek-chat';
export const deepSeekModels = {
  'deepseek-chat': {
    maxTokens: 8_000,
    contextWindow: 64_000,
    supportsImages: false,
    supportsPromptCache: true, // supports context caching, but not in the way anthropic does it (deepseek reports input tokens and reads/writes in the same usage report) FIXME: we need to show users cache stats how deepseek does it
    inputPrice: 0, // technically there is no input price, it's all either a cache hit or miss (ApiOptions will not show this)
    outputPrice: 0.28,
    cacheWritesPrice: 0.14,
    cacheReadsPrice: 0.014,
  },
  'deepseek-reasoner': {
    maxTokens: 8_000,
    contextWindow: 64_000,
    supportsImages: false,
    supportsPromptCache: true, // supports context caching, but not in the way anthropic does it (deepseek reports input tokens and reads/writes in the same usage report) FIXME: we need to show users cache stats how deepseek does it
    inputPrice: 0, // technically there is no input price, it's all either a cache hit or miss (ApiOptions will not show this)
    outputPrice: 2.19,
    cacheWritesPrice: 0.55,
    cacheReadsPrice: 0.14,
  },
} as const satisfies Record<string, ModelInfo>;

// Qwen
// https://bailian.console.aliyun.com/
export type QwenModelId = keyof typeof qwenModels;
export const qwenDefaultModelId: QwenModelId = 'qwen-coder-plus-latest';
export const qwenModels = {
  'qwen-coder-plus-latest': {
    maxTokens: 129_024,
    contextWindow: 131_072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0035,
    outputPrice: 0.007,
    cacheWritesPrice: 0.0035,
    cacheReadsPrice: 0.007,
  },
  'qwen-plus-latest': {
    maxTokens: 129_024,
    contextWindow: 131_072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0008,
    outputPrice: 0.002,
    cacheWritesPrice: 0.0004,
    cacheReadsPrice: 0.001,
  },
  'qwen-turbo-latest': {
    maxTokens: 1_000_000,
    contextWindow: 1_000_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0003,
    outputPrice: 0.0006,
    cacheWritesPrice: 0.00015,
    cacheReadsPrice: 0.0003,
  },
  'qwen-max-latest': {
    maxTokens: 30_720,
    contextWindow: 32_768,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0112,
    outputPrice: 0.0448,
    cacheWritesPrice: 0.0056,
    cacheReadsPrice: 0.0224,
  },
  'qwen-coder-plus': {
    maxTokens: 129_024,
    contextWindow: 131_072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0035,
    outputPrice: 0.007,
    cacheWritesPrice: 0.0035,
    cacheReadsPrice: 0.007,
  },
  'qwen-plus': {
    maxTokens: 129_024,
    contextWindow: 131_072,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0008,
    outputPrice: 0.002,
    cacheWritesPrice: 0.0004,
    cacheReadsPrice: 0.001,
  },
  'qwen-turbo': {
    maxTokens: 1_000_000,
    contextWindow: 1_000_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0003,
    outputPrice: 0.0006,
    cacheWritesPrice: 0.00015,
    cacheReadsPrice: 0.0003,
  },
  'qwen-max': {
    maxTokens: 30_720,
    contextWindow: 32_768,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.0112,
    outputPrice: 0.0448,
    cacheWritesPrice: 0.0056,
    cacheReadsPrice: 0.0224,
  },
  'deepseek-v3': {
    maxTokens: 8_000,
    contextWindow: 64_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0,
    outputPrice: 0.28,
    cacheWritesPrice: 0.14,
    cacheReadsPrice: 0.014,
  },
  'deepseek-r1': {
    maxTokens: 8_000,
    contextWindow: 64_000,
    supportsImages: false,
    supportsPromptCache: true,
    inputPrice: 0,
    outputPrice: 2.19,
    cacheWritesPrice: 0.55,
    cacheReadsPrice: 0.14,
  },
} as const satisfies Record<string, ModelInfo>;

// Mistral
// https://docs.mistral.ai/getting-started/models/models_overview/
export type MistralModelId = keyof typeof mistralModels;
export const mistralDefaultModelId: MistralModelId = 'codestral-2501';
export const mistralModels = {
  'mistral-large-2411': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 2.0,
    outputPrice: 6.0,
  },
  'pixtral-large-2411': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 2.0,
    outputPrice: 6.0,
  },
  'ministral-3b-2410': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.04,
    outputPrice: 0.04,
  },
  'ministral-8b-2410': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.1,
  },
  'mistral-small-2501': {
    maxTokens: 32_000,
    contextWindow: 32_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.1,
    outputPrice: 0.3,
  },
  'pixtral-12b-2409': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: true,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.15,
  },
  'open-mistral-nemo-2407': {
    maxTokens: 131_000,
    contextWindow: 131_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.15,
  },
  'open-codestral-mamba': {
    maxTokens: 256_000,
    contextWindow: 256_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.15,
    outputPrice: 0.15,
  },
  'codestral-2501': {
    maxTokens: 256_000,
    contextWindow: 256_000,
    supportsImages: false,
    supportsPromptCache: false,
    inputPrice: 0.3,
    outputPrice: 0.9,
  },
} as const satisfies Record<string, ModelInfo>;

// LiteLLM
// https://docs.litellm.ai/docs/
export type LiteLLMModelId = string;
export const liteLlmDefaultModelId = 'gpt-3.5-turbo';
export const liteLlmModelInfoSaneDefaults: ModelInfo = {
  maxTokens: 4096,
  contextWindow: 8192,
  supportsImages: false,
  supportsPromptCache: false,
  inputPrice: 0,
  outputPrice: 0,
};
