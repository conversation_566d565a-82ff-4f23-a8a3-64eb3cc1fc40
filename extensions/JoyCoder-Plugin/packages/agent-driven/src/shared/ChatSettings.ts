export interface ChatSettings {
  mode: string;
  // mode: 'plan' | 'act' | 'ask' | 'orchestrator' | 'chat';
}
// export interface CoderSettings {
//   wMode: 'plan' | 'act' | 'chat' | 'auto';
//   context?: string;
// }
export interface ChatContent {
  message?: string;
  images?: string[];
}
// export const DEFAULT_CODER_SETTINGS: CoderSettings = {
//   wMode: 'chat',
// };
export const DEFAULT_CHAT_SETTINGS: ChatSettings = {
  mode: 'act',
};
// export enum ChatMode {
//   chat = 'Chat',
//   act = 'Agent',
//   auto = 'AgentAuto',
//   plan = 'Plan',
// }
