import { Anthropic } from '@anthropic-ai/sdk';
import { ApiConfiguration, ModelInfo } from '../../shared/api';
import { OpenAiHandler } from './providers/openai';
import { ApiStream } from './transform/stream';
import { <PERSON>Hand<PERSON> } from './providers/gemini';

export interface ApiHandler {
  createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream;
  getModel(): { id: string; info: ModelInfo };
  completePrompt(prompt: string): Promise<string>;
  completeAgent(prompt: string): Promise<string>;
}

export function buildApiHandler(configuration: ApiConfiguration): ApiHandler {
  const { apiProvider, ...options } = configuration;
  switch (apiProvider) {
    // case 'anthropic':
    //   return new AnthropicHandler(options);
    case 'openai':
      return new <PERSON>AiHandler(options);
    // case 'gemini':
    //   return new <PERSON>Handler(options);
    default:
      return new <PERSON><PERSON><PERSON>Handler(options);
  }
}
