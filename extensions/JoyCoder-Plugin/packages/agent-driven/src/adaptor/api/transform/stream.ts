export type ApiStream = AsyncGenerator<ApiStreamChunk>;
export type ApiStreamChunk = ApiStreamTextChunk | ApiStreamUsageChunk | ApiStreamReasoningChunk;

export interface ApiStreamTextChunk {
  type: 'text';
  text: string;
  conversationId?: string;
}
export interface ApiStreamReasoningChunk {
  type: 'reasoning';
  reasoning: string;
  reasoning_content?: string;
  conversationId?: string;
}

export interface ApiStreamUsageChunk {
  type: 'usage';
  inputTokens: number;
  conversationId?: string;
  outputTokens: number;
  cacheWriteTokens?: number;
  cacheReadTokens?: number;
  totalCost?: number; // openrouter
}
