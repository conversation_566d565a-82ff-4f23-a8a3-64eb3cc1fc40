import { globby, Options } from 'globby';
import os from 'os';
import * as path from 'path';
import * as vscode from 'vscode';
import { arePathsEqual } from '../../utils/path';
import { isRemoteEnvironment } from '../../utils/fs';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

export async function listFiles(
  dirPath: string | vscode.Uri,
  recursive: boolean,
  limit: number
): Promise<[string[], boolean]> {
  // Handle root directory check based on environment
  let absolutePath = FileSystemHelper.resolveUri(dirPath);
  absolutePath = FileSystemHelper.getRemotePath(absolutePath);
  // Do not allow listing files in root or home directory, which cline tends to want to do when the user's prompt is vague.
  // todo : check if this is a good idea 本地模式下不允许列出根目录或主目录中的文件
  const root = process.platform === 'win32' ? path.parse(absolutePath).root : '/';
  const isRoot = arePathsEqual(absolutePath, root);
  if (isRoot) {
    return [[root], false];
  }
  const homeDir = os.homedir();
  const isHomeDir = arePathsEqual(absolutePath, homeDir);
  if (isHomeDir) {
    return [[homeDir], false];
  }

  const dirsToIgnore = [
    'node_modules',
    '__pycache__',
    'env',
    'venv',
    'target/dependency',
    'build/dependencies',
    'dist',
    'out',
    'bundle',
    'vendor',
    'tmp',
    'temp',
    'deps',
    'pkg',
    'Pods',
    '.*', // '!**/.*' excludes hidden directories, while '!**/.*/**' excludes only their contents. This way we are at least aware of the existence of hidden directories.
  ].map((dir) => `**/${dir}/**`);

  const options: Options = {
    cwd: isRemoteEnvironment() ? dirPath.toString() : absolutePath,
    dot: true, // do not ignore hidden files/directories
    absolute: true,
    markDirectories: true, // Append a / on any directories matched (/ is used on windows as well, so dont use path.sep)
    gitignore: recursive || isRemoteEnvironment(), // globby ignores any files that are gitignored
    ignore: recursive || isRemoteEnvironment() ? dirsToIgnore : undefined, // just in case there is no gitignore, we ignore sensible defaults
    onlyFiles: false, // true by default, false means it will list directories on their own too
    suppressErrors: true,
  };

  // * globs all files in one dir, ** globs files in nested directories
  // For non-recursive listing, we still use a simple pattern
  const filePaths =
    recursive || isRemoteEnvironment()
      ? await globbyLevelByLevel(limit, options)
      : (await globby('*', options)).slice(0, limit);

  // Ensure paths are in the correct format for the current environment
  const formattedPaths = filePaths.map((filePath) => {
    const fileUri = vscode.Uri.file(filePath);
    return isRemoteEnvironment() ? fileUri.path : filePath;
  });

  return [formattedPaths, formattedPaths.length >= limit];
}

/*
Breadth-first traversal of directory structure level by level up to a limit:
   - Queue-based approach ensures proper breadth-first traversal
   - Processes directory patterns level by level
   - Captures a representative sample of the directory structure up to the limit
   - Minimizes risk of missing deeply nested files

- Notes:
   - Relies on globby to mark directories with /
   - Potential for loops if symbolic links reference back to parent (we could use followSymlinks: false but that may not be ideal for some projects and it's pointless if they're not using symlinks wrong)
   - Timeout mechanism prevents infinite loops
*/
async function globbyLevelByLevel(limit: number, options?: Options) {
  // 根据环境选择实现方式
  return isRemoteEnvironment()
    ? await remoteGlobbyLevelByLevel(limit, options)
    : await localGlobbyLevelByLevel(limit, options);
}

// 原来的本地实现
async function localGlobbyLevelByLevel(limit: number, options?: Options) {
  const results: Set<string> = new Set();
  const queue: string[] = ['*'];

  const globbingProcess = async () => {
    while (queue.length > 0 && results.size < limit) {
      const pattern = queue.shift()!;
      const filesAtLevel = await globby(pattern, options);

      for (const file of filesAtLevel) {
        if (results.size >= limit) {
          break;
        }
        results.add(file);
        if (file.endsWith('/')) {
          // Escape parentheses in the path to prevent glob pattern interpretation
          // This is crucial for NextJS folder naming conventions which use parentheses like (auth), (dashboard)
          // Without escaping, glob treats parentheses as special pattern grouping characters
          const escapedFile = file.replace(/\(/g, '\\(').replace(/\)/g, '\\)');
          queue.push(`${escapedFile}*`);
        }
      }
    }
    return Array.from(results).slice(0, limit);
  };

  // Timeout after 10 seconds and return partial results
  const timeoutPromise = new Promise<string[]>((_, reject) => {
    setTimeout(() => reject(new Error('Globbing timeout')), 10_000);
  });
  try {
    return await Promise.race([globbingProcess(), timeoutPromise]);
  } catch (error) {
    console.warn('Globbing timed out, returning partial results');
    return Array.from(results);
  }
}

// 新的远程实现
export async function remoteGlobbyLevelByLevel(limit: number, options?: Options) {
  const results: Set<string> = new Set();
  const queue: Set<vscode.Uri> = new Set();

  // 初始化起始目录
  const startDir = vscode.Uri.parse(typeof options?.cwd === 'string' ? options.cwd : options?.cwd?.toString() || '/');

  queue.add(startDir);
  const listEntriesInDirectory = async (dirUri: vscode.Uri): Promise<[vscode.Uri[], vscode.Uri[]]> => {
    try {
      const entries = await vscode.workspace.fs.readDirectory(dirUri);
      const files: vscode.Uri[] = [];
      const dirs: vscode.Uri[] = [];

      for (const [name, type] of entries) {
        const entryUri = vscode.Uri.joinPath(dirUri, name);

        // 检查是否应该忽略该条目
        if (shouldIgnoreEntry(name, options?.ignore)) {
          continue;
        }

        if (type === vscode.FileType.Directory) {
          dirs.push(entryUri);
        } else if (type === vscode.FileType.File) {
          files.push(entryUri);
        }
      }

      return [files, dirs];
    } catch (error) {
      console.warn(`Failed to read directory ${dirUri.fsPath}:`, error);
      return [[], []];
    }
  };

  const shouldIgnoreEntry = (name: string, ignorePatterns?: string[]): boolean => {
    if (!ignorePatterns) {
      return false;
    }

    return ignorePatterns.some((pattern) => {
      // 将 glob 模式转换为正则表达式
      const regexPattern =
        '^' +
        pattern
          .replace(/\*\*/g, '.*')
          .replace(/\*/g, '[^/]*')
          .replace(/\?/g, '[^/]')
          .replace(/\//g, '\\/')
          .replace(/\./g, '\\.') +
        '$';
      return new RegExp(regexPattern).test(name);
    });
  };

  const processDirectory = async () => {
    while (queue.size > 0 && results.size < limit) {
      const currentDir = Array.from(queue)[0];
      queue.delete(currentDir);

      const [files, dirs] = await listEntriesInDirectory(currentDir);

      // 添加文件到结果集
      for (const file of files) {
        if (results.size >= limit) {
          break;
        }
        results.add(file.path);
      }

      // 添加目录到结果集和队列
      for (const dir of dirs) {
        if (results.size >= limit) {
          break;
        }
        const dirPath = dir.path;
        results.add(dirPath + '/');
        if (queue.size < limit) {
          // 防止队列过大
          queue.add(dir);
        }
      }
    }

    return Array.from(results).slice(0, limit);
  };

  const timeoutPromise = new Promise<string[]>((_, reject) => {
    setTimeout(() => reject(new Error('远端读取超时')), 30_000);
  });

  try {
    return await Promise.race([processDirectory(), timeoutPromise]);
  } catch (error) {
    console.warn('远端地址扫描超时，返回部分结果');
    return Array.from(results);
  }
}
