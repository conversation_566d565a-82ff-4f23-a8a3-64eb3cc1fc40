import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { MCP_SETTINGS_SCHEMA } from '@joycoder/shared/src/mcp/mcp';

export class McpSettingsError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'McpSettingsError';
  }
}

export class NoWorkspaceFolderError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NoWorkspaceFolderError';
  }
}

interface McpConfig {
  mcpServers: Record<string, unknown>;
  [key: string]: unknown;
}

export async function readAndParseFile(path: string): Promise<McpConfig> {
  try {
    if (!path || path === '') {
      return JSON.parse(MCP_SETTINGS_SCHEMA) as McpConfig;
    }

    const content = await FileSystemHelper.readFile(path, 'utf-8');
    const config = JSON.parse(content) as unknown;

    if (!config || typeof config !== 'object') {
      throw new McpSettingsError(`Invalid configuration structure. File path: ${path}`);
    }

    const typedConfig = config as McpConfig;

    if (!typedConfig.mcpServers || typeof typedConfig.mcpServers !== 'object') {
      typedConfig.mcpServers = {};
    }

    return typedConfig;
  } catch (error) {
    if (error instanceof McpSettingsError) {
      throw error;
    }
    if (error instanceof SyntaxError) {
      throw new McpSettingsError(`Failed to parse JSON in file ${path}: ${error.message}`);
    }
    throw new McpSettingsError(
      `Failed to read or parse file ${path}: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
