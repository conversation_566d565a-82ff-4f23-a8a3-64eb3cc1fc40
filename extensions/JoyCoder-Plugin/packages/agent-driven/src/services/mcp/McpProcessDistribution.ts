import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import { openFile, openImage } from '../../integrations/misc/open-file';
import { McpApiService } from './McpApiService';
import { McpSendMsgType } from '@joycoder/shared/src/mcp/McpTypes';
export type ProjectType = 'global' | 'project' | 'initial';
import * as vscode from 'vscode';

export async function mcprocessDistribution(sidebarProvider: JoyCoderProvider, data: any) {
  const type = data?.type;
  try {
    switch (type) {
      case McpSendMsgType.MCP_LIST:
        return postMcpListToWebview(data);
      case McpSendMsgType.GET_MCP_SERVER:
        return getMcpDetailByName(data);
      case McpSendMsgType.GET_MCP_INSTALL_PARAM:
        return await getInstallParam(data);
      case McpSendMsgType.OPEN_MCP_SETTING_FILE:
        const projectType = data?.projectType === 'project' ? 'initial' : 'global';
        const mcpSettingsFilePath = await sidebarProvider.mcpHub?.getMcpSettingsFilePath(projectType);
        if (mcpSettingsFilePath) {
          openFile(mcpSettingsFilePath, projectType);
        }
        break;
      case McpSendMsgType.GET_MCP_SETTINGS_FILE_CONTENT:
        return await sidebarProvider.mcpHub?.readAndValidateMcpSettingsFile();
      case McpSendMsgType.UPDATE_OR_INSERT_MCP_SERVER:
        try {
          return await sidebarProvider.mcpHub?.saveOrUpdateServer(data?.serverParam, data?.projectType);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return { error: errorMessage };
        }

      case McpSendMsgType.REFRESH_MCP_SERVICE:
        return await sidebarProvider.mcpHub?.updateMcpServers();
      case McpSendMsgType.GET_MCP_CONNECTION_SERVER:
        return await sidebarProvider.mcpHub?.getMcpServers();
      case McpSendMsgType.TOGGLE_MCP_SERVER:
        return await sidebarProvider.mcpHub?.toggleServerDisabled(data?.serverName, data?.disabled, data?.projectType);
      case McpSendMsgType.RESTART_MCP_SERVER:
        return await sidebarProvider.mcpHub?.restartConnection(data?.serverName);
      case McpSendMsgType.UPDATE_TOOL_AUTO_APPROVE:
        await sidebarProvider.mcpHub?.toggleToolAutoApprove(
          data?.serverName,
          data?.toolName,
          data?.autoApprove,
          data?.projectType
        );
        break;
      case McpSendMsgType.DELETE_MCP_SERVER:
        await sidebarProvider.mcpHub?.deleteServer(data?.serverName, data?.projectType);
        break;
      default:
        console.warn('[MCP] Unmatched message type:', type, data);
        break;
    }
  } catch (error) {
    console.error('[MCP] Error processing MCP message:', error, data);
  }
  return null;
}

export async function postMcpListToWebview(data: any) {
  const response = await McpApiService.getMcpList(data?.keyword, data?.pageNum, data?.pageSize, data?.expressSetup);
  return response;
}

export async function getInstallParam(data: any) {
  return await McpApiService.getInstallMcpParam(data?.serviceId, data?.version);
}

export async function getMcpDetailByName(data: any) {
  return await McpApiService.getMcpDetailByName(data?.serviceName);
}
