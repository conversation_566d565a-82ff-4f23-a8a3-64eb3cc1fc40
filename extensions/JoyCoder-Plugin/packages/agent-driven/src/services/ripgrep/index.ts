import * as vscode from 'vscode';
import * as childProcess from 'child_process';
import * as path from 'path';
import * as readline from 'readline';
import { fileExistsAtPath, isRemoteEnvironment } from '../../utils/fs';
import { JoyCoderIgnoreController } from '../../core/ignore/JoycoderIgnoreController';
import { JoyCoderMCPMessageMap } from '../../adaptor/translate/message';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

/*
此文件提供使用 ripgrep 对文件执行正则搜索的功能。
- 本地环境：使用原有的 ripgrep 实现
- 远程环境：使用 VSCode 内置的搜索 API 作为备选方案
*/

const isWindows = /^win/.test(process.platform);
const binName = isWindows ? 'rg.exe' : 'rg';

interface SearchResult {
  filePath: string;
  line: number;
  column: number;
  match: string;
  beforeContext: string[];
  afterContext: string[];
}

const MAX_RESULTS = 300;

/**
 * 获取 ripgrep 二进制文件路径
 * 仅用于本地环境
 */
async function getBinPath(vscodeAppRoot: string): Promise<string | undefined> {
  const checkPath = async (pkgFolder: string) => {
    const fullPath = path.join(vscodeAppRoot, pkgFolder, binName);
    return (await fileExistsAtPath(fullPath)) ? fullPath : undefined;
  };

  return (
    (await checkPath('node_modules/@vscode/ripgrep/bin/')) ||
    (await checkPath('node_modules/vscode-ripgrep/bin')) ||
    (await checkPath('node_modules.asar.unpacked/vscode-ripgrep/bin/')) ||
    (await checkPath('node_modules.asar.unpacked/@vscode/ripgrep/bin/'))
  );
}

/**
 * 执行 ripgrep 命令并获取输出
 */
async function execRipgrep(bin: string, args: string[]): Promise<string> {
  return new Promise((resolve, reject) => {
    const rgProcess = childProcess.spawn(bin, args);
    const rl = readline.createInterface({
      input: rgProcess.stdout,
      crlfDelay: Infinity,
    });

    let output = '';
    let lineCount = 0;
    const maxLines = MAX_RESULTS * 5; // limiting ripgrep output with max lines since there's no other way to limit results. it's okay that we're outputting as json, since we're parsing it line by line and ignore anything that's not part of a match. This assumes each result is at most 5 lines.

    rl.on('line', (line) => {
      if (lineCount < maxLines) {
        output += line + '\n';
        lineCount++;
      } else {
        rl.close();
        rgProcess.kill();
      }
    });

    let errorOutput = '';
    rgProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    rl.on('close', () => {
      if (errorOutput) {
        reject(new Error(`${JoyCoderMCPMessageMap['ripgrep process error']}: ${errorOutput}`));
      } else {
        resolve(output);
      }
    });
    rgProcess.on('error', (error) => {
      reject(new Error(`${JoyCoderMCPMessageMap['ripgrep process error']}: ${error.message}`));
    });
  });
}

/**
 * 使用 VSCode 搜索 API 执行搜索（远程环境下的替代方案）
 */
async function vscodeSearchFiles(
  directoryPath: string,
  regex: string,
  filePattern?: string,
  joycoderIgnoreController?: JoyCoderIgnoreController
): Promise<SearchResult[]> {
  try {
    console.log('开始远程搜索...');
    console.log('搜索目录:', directoryPath);
    console.log('正则表达式:', regex);
    console.log('文件模式:', filePattern || '**/*');

    const searchResults: SearchResult[] = [];
    const processedFiles = new Set<string>(); // 用于跟踪已处理的文件，避免重复处理
    const regExp = new RegExp(regex);

    // 获取工作区文件夹
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders || workspaceFolders.length === 0) {
      console.error('没有打开的工作区文件夹');
      return [];
    }

    // 在远程环境中正确处理 URI
    let dirUri: vscode.Uri;
    if (directoryPath.startsWith('vscode-remote://')) {
      // 如果已经是远程 URI，使用 FileSystemHelper.getUri 来处理
      dirUri = FileSystemHelper.getUri(directoryPath);
    } else {
      // 尝试从工作区获取匹配的文件夹
      const workspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(directoryPath));
      if (workspaceFolder) {
        dirUri = workspaceFolder.uri;
      } else {
        // 如果找不到匹配的工作区文件夹，使用第一个工作区文件夹
        dirUri = workspaceFolders[0].uri;
      }
    }

    console.log('使用的 URI:', dirUri.toString());

    // 创建文件模式匹配函数
    const filePatternRegex = filePattern ? new RegExp(filePattern.replace(/\*/g, '.*').replace(/\?/g, '.')) : null;

    const matchesFilePattern = (fileName: string): boolean => {
      if (!filePatternRegex) return true;
      return filePatternRegex.test(fileName);
    };

    // 递归读取目录内容的函数
    async function readDirectoryRecursively(uri: vscode.Uri): Promise<void> {
      try {
        // 使用 VSCode 的 fs API 读取目录内容
        const entries = await vscode.workspace.fs.readDirectory(uri);

        for (const [name, type] of entries) {
          // 跳过 node_modules 目录
          if (name === 'node_modules') {
            continue;
          }

          const entryUri = vscode.Uri.joinPath(uri, name);
          const entryPath = entryUri.fsPath;

          // 检查是否应该被忽略
          if (joycoderIgnoreController && !joycoderIgnoreController.validateAccess(entryPath)) {
            console.log(`跳过被忽略的路径: ${entryPath}`);
            continue;
          }

          if (type === vscode.FileType.Directory) {
            // 递归处理子目录
            await readDirectoryRecursively(entryUri);
          } else if (type === vscode.FileType.File) {
            // 检查文件是否匹配模式
            if (matchesFilePattern(name) && !processedFiles.has(entryPath)) {
              processedFiles.add(entryPath);

              try {
                // 打开文件并搜索内容
                const document = await vscode.workspace.openTextDocument(entryUri);
                const text = document.getText();
                const lines = text.split('\n');

                for (let i = 0; i < lines.length; i++) {
                  const line = lines[i];
                  if (regExp.test(line)) {
                    const lineNumber = i + 1; // VSCode 使用 0 索引，我们转换为 1 索引
                    const column = line.search(regExp);

                    const beforeContext = lines.slice(Math.max(0, i - 1), i);
                    const afterContext = lines.slice(i + 1, i + 2);

                    searchResults.push({
                      filePath: entryPath,
                      line: lineNumber,
                      column,
                      match: line,
                      beforeContext,
                      afterContext,
                    });

                    if (searchResults.length >= MAX_RESULTS) {
                      console.log(`达到最大结果数 ${MAX_RESULTS}，停止搜索`);
                      return;
                    }
                  }
                }
              } catch (fileError) {
                console.error(`处理文件 ${entryPath} 时出错:`, fileError);
                // 继续处理下一个文件
              }
            }
          }
        }
      } catch (dirError) {
        console.error(`读取目录 ${uri.fsPath} 时出错:`, dirError);
        // 继续处理其他目录
      }
    }

    // 开始递归读取目录
    await readDirectoryRecursively(dirUri);

    console.log(`搜索完成，找到 ${searchResults.length} 个结果`);
    return searchResults;
  } catch (error) {
    console.error('使用 VSCode 搜索 API 失败:', error);
    throw new Error(`搜索失败: ${error.message}`);
  }
}

/**
 * 执行文件正则搜索
 * 本地环境：使用原有的 ripgrep 实现，但正确解析上下文信息
 * 远程环境：使用 VSCode 搜索 API
 */
export async function regexSearchFiles(
  cwd: string | vscode.Uri,
  directoryPath: string,
  regex: string,
  filePattern?: string,
  joycoderIgnoreController?: JoyCoderIgnoreController
): Promise<string> {
  // 判断是否为远程环境
  const isRemote = isRemoteEnvironment();
  console.log('是否为远程环境:', isRemote);

  let results: SearchResult[] = [];

  try {
    if (isRemote) {
      // 远程环境：使用 VSCode 搜索 API
      console.log('使用 VSCode 搜索 API 进行远程搜索');
      results = await vscodeSearchFiles(directoryPath, regex, filePattern, joycoderIgnoreController);
    } else {
      // 本地环境：使用原有的 ripgrep 实现
      console.log('使用 ripgrep 进行本地搜索');
      const vscodeAppRoot = vscode.env.appRoot;
      const rgPath = await getBinPath(vscodeAppRoot);

      if (!rgPath) {
        throw new Error(JoyCoderMCPMessageMap['Could not find ripgrep binary']);
      }

      const args = ['--json', '-e', regex, '--glob', filePattern || '*', '--context', '1', directoryPath];

      let output: string;
      try {
        output = await execRipgrep(rgPath, args);
      } catch (error) {
        console.error('ripgrep 执行错误:', error);
        return JoyCoderMCPMessageMap['No results found'];
      }

      let currentResult: Partial<SearchResult> | null = null;

      output.split('\n').forEach((line) => {
        if (line) {
          try {
            const parsed = JSON.parse(line);

            if (parsed.type === 'match') {
              if (currentResult) {
                results.push(currentResult as SearchResult);
              }
              currentResult = {
                filePath: parsed.data.path.text,
                line: parsed.data.line_number,
                column: parsed.data.submatches[0].start,
                match: parsed.data.lines.text,
                beforeContext: [], // 初始化为空数组
                afterContext: [], // 初始化为空数组
              };
            } else if (parsed.type === 'context' && currentResult) {
              // 正确处理上下文
              if (parsed.data.line_number < currentResult.line!) {
                currentResult.beforeContext!.push(parsed.data.lines.text);
              } else {
                currentResult.afterContext!.push(parsed.data.lines.text);
              }
            }
          } catch (error) {
            console.error(JoyCoderMCPMessageMap['Error parsing ripgrep output'], error);
          }
        }
      });

      if (currentResult) {
        results.push(currentResult as SearchResult);
      }
    }

    // 使用 JoyCoderIgnoreController 过滤结果（如果提供）
    if (joycoderIgnoreController) {
      results = results.filter((result) => joycoderIgnoreController.validateAccess(result.filePath));
    }

    console.log(`搜索完成，找到 ${results.length} 个结果`);
    return formatResults(results, cwd);
  } catch (error) {
    console.error('搜索过程中发生错误:', error);
    return `搜索失败: ${error.message}`;
  }
}

/**
 * 格式化搜索结果
 * 本地和远程环境使用相同的格式
 */
function formatResults(results: SearchResult[], cwd: string | vscode.Uri): string {
  const groupedResults: { [key: string]: SearchResult[] } = {};

  let output = '';
  if (results.length >= MAX_RESULTS) {
    output += `Showing first ${MAX_RESULTS} of ${MAX_RESULTS}+ results. Use a more specific search if necessary.\n\n`;
  } else {
    output += `Found ${results.length === 1 ? '1 result' : `${results.length.toLocaleString()} results`}.\n\n`;
  }

  // 按文件名分组结果
  results.slice(0, MAX_RESULTS).forEach((result) => {
    // 处理文件路径，确保在远程环境中正确显示
    let resultFilePath: vscode.Uri;

    if (isRemoteEnvironment() && !result.filePath.startsWith('vscode-remote://')) {
      resultFilePath = vscode.Uri.parse(`vscode-remote:${result.filePath}`);
    } else {
      resultFilePath = vscode.Uri.file(result.filePath);
    }

    const relativeFilePath = vscode.workspace.asRelativePath(resultFilePath, false);
    if (!groupedResults[relativeFilePath]) {
      groupedResults[relativeFilePath] = [];
    }
    groupedResults[relativeFilePath].push(result);
  });

  // 生成格式化输出
  for (const [filePath, fileResults] of Object.entries(groupedResults)) {
    // 为字符串添加 toPosix 方法，处理跨平台路径
    if (!String.prototype.hasOwnProperty('toPosix')) {
      Object.defineProperty(String.prototype, 'toPosix', {
        value: function () {
          return this.replace(/\\/g, '/');
        },
        enumerable: false,
      });
    }

    output += `${filePath.toPosix()}\n│----\n`;

    fileResults.forEach((result, index) => {
      const allLines = [...result.beforeContext, result.match, ...result.afterContext];
      allLines.forEach((line) => {
        output += `│${line?.trimEnd() ?? ''}\n`;
      });

      if (index < fileResults.length - 1) {
        output += '│----\n';
      }
    });

    output += '│----\n\n';
  }

  return output.trim();
}
