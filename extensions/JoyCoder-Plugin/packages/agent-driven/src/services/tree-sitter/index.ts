import { JoyCoder } from './../../core/Joycoder';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as vscode from 'vscode';
import { listFiles } from '../glob/list-files';
import { LanguageParser, loadRequiredLanguageParsers } from './languageParser';
import { fileExistsAtPath, isRemoteEnvironment } from '../../utils/fs';
import { JoyCoderIgnoreController } from '../../core/ignore/JoycoderIgnoreController';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { parseMarkdown } from './markdownParser';

// Private constant
const DEFAULT_MIN_COMPONENT_LINES_VALUE = 4;

// Getter function for MIN_COMPONENT_LINES (for easier testing)
let currentMinComponentLines = DEFAULT_MIN_COMPONENT_LINES_VALUE;
const extensions = [
  'js',
  'jsx',
  'ts',
  'tsx',
  'py',
  // Rust
  'rs',
  'go',
  // C
  'c',
  'h',
  // C++
  'cpp',
  'hpp',
  // C#
  'cs',
  // Ruby
  'rb',
  'java',
  'php',
  'swift',
].map((e) => `.${e}`);
export { extensions };

/**
 * Get the current minimum number of lines for a component to be included
 */
export function getMinComponentLines(): number {
  return currentMinComponentLines;
}
// TODO: implement caching behavior to avoid having to keep analyzing project for new tasks.
/**
 * 解析指定目录下的源代码文件，提取顶层定义信息
 *
 * @param dirPath - VS Code URI 格式的目录路径
 * @param joycoderIgnoreController - 可选的文件过滤控制器，用于控制哪些文件可以被访问和解析
 * @returns 返回包含所有解析出的定义信息的字符串。如果目录不存在或无访问权限，返回错误信息；
 *          如果没有找到任何定义，返回 "No source code definitions found"
 *
 * 该函数会：
 * 1. 验证目录是否存在且可访问
 * 2. 获取顶层文件列表（不包含 git 忽略的文件）
 * 3. 分离可解析的文件和其他文件
 * 4. 加载必要的语言解析器
 * 5. 解析每个允许访问的文件，提取定义信息
 * 6. 将解析结果组合成字符串返回
 */
export async function parseSourceCodeForDefinitionsTopLevel(
  dirPath: string | vscode.Uri,
  joycoderIgnoreController?: JoyCoderIgnoreController
): Promise<string> {
  // check if the path exists
  const dirExists = await fileExistsAtPath(FileSystemHelper.resolveUri(dirPath));
  if (!dirExists) {
    return '该目录不存在或者没有权限访问它。';
    // return 'This directory does not exist or you do not have permission to access it.';
  }

  // Get all files at top level (not gitignored)
  const [allFiles, _] = await listFiles(dirPath, false, 400);

  let result = '';

  // Separate files to parse and remaining files
  const { filesToParse, remainingFiles } = separateFiles(allFiles);

  const languageParsers = await loadRequiredLanguageParsers(filesToParse);

  // Parse specific files we have language parsers for
  // const filesWithoutDefinitions: string[] = []

  // Filter filepaths for access if controller is provided
  const allowedFilesToParse = joycoderIgnoreController
    ? joycoderIgnoreController.filterPaths(filesToParse)
    : filesToParse;

  for (const filePath of allowedFilesToParse) {
    const filePathUri = FileSystemHelper.resolveUri(dirPath, filePath);
    const definitions = await parseFile(filePathUri, languageParsers, joycoderIgnoreController);
    if (definitions) {
      let directoryPath = FileSystemHelper.relative(dirPath, filePath);
      if (isRemoteEnvironment()) {
        const definitions = await parseFile(filePathUri, languageParsers, joycoderIgnoreController);
        if (definitions) {
          const uriFilePath = vscode.Uri.file(filePath);
          directoryPath = FileSystemHelper.relative(dirPath, uriFilePath);
        }
      }
      result += `${directoryPath.toPosix()}\n${definitions}\n`;
    }
    // else {
    // 	filesWithoutDefinitions.push(file)
    // }
  }

  // List remaining files' paths
  // let didFindUnparsedFiles = false
  // filesWithoutDefinitions
  // 	.concat(remainingFiles)
  // 	.sort()
  // 	.forEach((file) => {
  // 		if (!didFindUnparsedFiles) {
  // 			result += "# Unparsed Files\n\n"
  // 			didFindUnparsedFiles = true
  // 		}
  // 		result += `${path.relative(dirPath, file)}\n`
  // 	})

  return result ? result : 'No source code definitions found.';
}

function separateFiles(allFiles: string[]): {
  filesToParse: string[];
  remainingFiles: string[];
} {
  const filesToParse = allFiles.filter((file) => extensions.includes(path.extname(file))).slice(0, 50); // 50 files max
  const remainingFiles = allFiles.filter((file) => !filesToParse.includes(file));
  return { filesToParse, remainingFiles };
}

/*
Parsing files using tree-sitter

1. Parse the file content into an AST (Abstract Syntax Tree) using the appropriate language grammar (set of rules that define how the components of a language like keywords, expressions, and statements can be combined to create valid programs).
2. Create a query using a language-specific query string, and run it against the AST's root node to capture specific syntax elements.
    - We use tag queries to identify named entities in a program, and then use a syntax capture to label the entity and its name. A notable example of this is GitHub's search-based code navigation.
	- Our custom tag queries are based on tree-sitter's default tag queries, but modified to only capture definitions.
3. Sort the captures by their position in the file, output the name of the definition, and format by i.e. adding "|----\n" for gaps between captured sections.

This approach allows us to focus on the most relevant parts of the code (defined by our language-specific queries) and provides a concise yet informative view of the file's structure and key elements.

- https://github.com/tree-sitter/node-tree-sitter/blob/master/test/query_test.js
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/query-test.js
- https://github.com/tree-sitter/tree-sitter/blob/master/lib/binding_web/test/helper.js
- https://tree-sitter.github.io/tree-sitter/code-navigation-systems
*/
async function parseFile(
  filePath: string | vscode.Uri,
  languageParsers: LanguageParser,
  joycoderIgnoreController?: JoyCoderIgnoreController
): Promise<string | null> {
  if (joycoderIgnoreController) {
    const pathToValidate = filePath instanceof vscode.Uri ? filePath.fsPath : filePath;
    if (!joycoderIgnoreController.validateAccess(pathToValidate)) {
      return null;
    }
  }
  const fileContent = await FileSystemHelper.readFile(filePath, 'utf8');
  const ext = FileSystemHelper.extname(filePath).toLowerCase().slice(1);

  const { parser, query } = languageParsers[ext] || {};
  if (!parser || !query) {
    return `Unsupported file type: ${filePath}`;
  }

  let formattedOutput = '';

  try {
    // Parse the file content into an Abstract Syntax Tree (AST), a tree-like representation of the code
    const tree = parser.parse(fileContent);

    // Apply the query to the AST and get the captures
    // Captures are specific parts of the AST that match our query patterns, each capture represents a node in the AST that we're interested in.
    const captures = query.captures(tree.rootNode);

    // Sort captures by their start position
    captures.sort((a, b) => a.node.startPosition.row - b.node.startPosition.row);

    // Split the file content into individual lines
    const lines = fileContent.split('\n');

    // Keep track of the last line we've processed
    let lastLine = -1;

    captures.forEach((capture) => {
      const { node, name } = capture;
      // Get the start and end lines of the current AST node
      const startLine = node.startPosition.row;
      const endLine = node.endPosition.row;
      // Once we've retrieved the nodes we care about through the language query, we filter for lines with definition names only.
      // name.startsWith("name.reference.") > refs can be used for ranking purposes, but we don't need them for the output
      // previously we did `name.startsWith("name.definition.")` but this was too strict and excluded some relevant definitions

      // Add separator if there's a gap between captures
      if (lastLine !== -1 && startLine > lastLine + 1) {
        formattedOutput += '|----\n';
      }
      // Only add the first line of the definition
      // query captures includes the definition name and the definition implementation, but we only want the name (I found discrepencies in the naming structure for various languages, i.e. javascript names would be 'name' and typescript names would be 'name.definition)
      if (name.includes('name') && lines[startLine]) {
        formattedOutput += `│${lines[startLine]}\n`;
      }
      // Adds all the captured lines
      // for (let i = startLine; i <= endLine; i++) {
      // 	formattedOutput += `│${lines[i]}\n`
      // }
      //}

      lastLine = endLine;
    });
  } catch (error) {
    console.log(`Error parsing file: ${error}\n`);
  }

  if (formattedOutput.length > 0) {
    return `|----\n${formattedOutput}|----\n`;
  }
  return null;
}
export async function parseSourceCodeDefinitionsForFile(
  filePath: string,
  rooIgnoreController?: JoyCoderIgnoreController
): Promise<string | undefined> {
  // check if the file exists
  const fileExists = await fileExistsAtPath(path.resolve(filePath));
  if (!fileExists) {
    return 'This file does not exist or you do not have permission to access it.';
  }

  // Get file extension to determine parser
  const ext = path.extname(filePath).toLowerCase();
  // Check if the file extension is supported
  if (!extensions.includes(ext)) {
    return undefined;
  }

  // Special case for markdown files
  if (ext === '.md' || ext === '.markdown') {
    // Check if we have permission to access this file
    if (rooIgnoreController && !rooIgnoreController.validateAccess(filePath)) {
      return undefined;
    }

    // Read file content
    const fileContent = await fs.readFile(filePath, 'utf8');

    // Split the file content into individual lines
    const lines = fileContent.split('\n');

    // Parse markdown content to get captures
    const markdownCaptures = parseMarkdown(fileContent);

    // Process the captures
    const markdownDefinitions = processCaptures(markdownCaptures, lines, 'markdown');

    if (markdownDefinitions) {
      return `# ${path.basename(filePath)}\n${markdownDefinitions}`;
    }
    return undefined;
  }

  // For other file types, load parser and use tree-sitter
  const languageParsers = await loadRequiredLanguageParsers([filePath]);

  // Parse the file if we have a parser for it
  const definitions = await parseFile(filePath, languageParsers, rooIgnoreController);
  if (definitions) {
    return `# ${path.basename(filePath)}\n${definitions}`;
  }

  return undefined;
}
/**
 * Process captures from tree-sitter or markdown parser
 *
 * @param captures - The captures to process
 * @param lines - The lines of the file
 * @param minComponentLines - Minimum number of lines for a component to be included
 * @returns A formatted string with definitions
 */
function processCaptures(captures: any[], lines: string[], language: string): string | null {
  // Determine if HTML filtering is needed for this language
  const needsHtmlFiltering = ['jsx', 'tsx'].includes(language);

  // Filter function to exclude HTML elements if needed
  const isNotHtmlElement = (line: string): boolean => {
    if (!needsHtmlFiltering) return true;
    // Common HTML elements pattern
    const HTML_ELEMENTS = /^[^A-Z]*<\/?(?:div|span|button|input|h[1-6]|p|a|img|ul|li|form)\b/;
    const trimmedLine = line.trim();
    return !HTML_ELEMENTS.test(trimmedLine);
  };

  // No definitions found
  if (captures.length === 0) {
    return null;
  }

  let formattedOutput = '';

  // Sort captures by their start position
  captures.sort((a, b) => a.node.startPosition.row - b.node.startPosition.row);

  // Track already processed lines to avoid duplicates
  const processedLines = new Set<string>();

  // First pass - categorize captures by type
  captures.forEach((capture) => {
    const { node, name } = capture;

    // Skip captures that don't represent definitions
    if (!name.includes('definition') && !name.includes('name')) {
      return;
    }

    // Get the parent node that contains the full definition
    const definitionNode = name.includes('name') ? node.parent : node;
    if (!definitionNode) return;

    // Get the start and end lines of the full definition
    const startLine = definitionNode.startPosition.row;
    const endLine = definitionNode.endPosition.row;
    const lineCount = endLine - startLine + 1;

    // Skip components that don't span enough lines
    if (lineCount < getMinComponentLines()) {
      return;
    }

    // Create unique key for this definition based on line range
    // This ensures we don't output the same line range multiple times
    const lineKey = `${startLine}-${endLine}`;

    // Skip already processed lines
    if (processedLines.has(lineKey)) {
      return;
    }

    // Check if this is a valid component definition (not an HTML element)
    const startLineContent = lines[startLine].trim();

    // Special handling for component name definitions
    if (name.includes('name.definition')) {
      // Extract component name
      const componentName = node.text;

      // Add component name to output regardless of HTML filtering
      if (!processedLines.has(lineKey) && componentName) {
        formattedOutput += `${startLine + 1}--${endLine + 1} | ${lines[startLine]}\n`;
        processedLines.add(lineKey);
      }
    }
    // For other component definitions
    else if (isNotHtmlElement(startLineContent)) {
      formattedOutput += `${startLine + 1}--${endLine + 1} | ${lines[startLine]}\n`;
      processedLines.add(lineKey);

      // If this is part of a larger definition, include its non-HTML context
      if (node.parent && node.parent.lastChild) {
        const contextEnd = node.parent.lastChild.endPosition.row;
        const contextSpan = contextEnd - node.parent.startPosition.row + 1;

        // Only include context if it spans multiple lines
        if (contextSpan >= getMinComponentLines()) {
          // Add the full range first
          const rangeKey = `${node.parent.startPosition.row}-${contextEnd}`;
          if (!processedLines.has(rangeKey)) {
            formattedOutput += `${node.parent.startPosition.row + 1}--${contextEnd + 1} | ${
              lines[node.parent.startPosition.row]
            }\n`;
            processedLines.add(rangeKey);
          }
        }
      }
    }
  });

  if (formattedOutput.length > 0) {
    return formattedOutput;
  }

  return null;
}
