import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as childProcess from 'child_process';
import { QdrantServer, getQdrantServer } from '../qdrant';

// Mock dependencies
jest.mock('fs');
jest.mock('child_process');
jest.mock('net');

const mockFs = fs as jest.Mocked<typeof fs>;
const mockChildProcess = childProcess as jest.Mocked<typeof childProcess>;

describe('Qdrant启动错误处理测试', () => {
  let qdrantServer: QdrantServer;
  let mockProcess: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // 模拟文件系统
    mockFs.existsSync.mockReturnValue(true);
    mockFs.mkdirSync.mockImplementation(() => undefined);

    // 模拟子进程
    mockProcess = {
      stdout: {
        on: jest.fn(),
      },
      stderr: {
        on: jest.fn(),
      },
      on: jest.fn(),
      kill: jest.fn(),
      pid: 12345,
    };

    mockChildProcess.spawn.mockReturnValue(mockProcess);

    qdrantServer = getQdrantServer();
  });

  describe('启动参数验证测试', () => {
    test('应该使用--storage-snapshot而不是--storage-path', async () => {
      // 模拟端口可用
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      try {
        await qdrantServer.start(6633);
      } catch (error) {
        // 忽略启动错误，我们只关心参数验证
      }

      // 验证spawn调用的参数
      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      expect(spawnCall).toBeDefined();

      const [executablePath, args] = spawnCall;

      // 验证不包含错误的参数
      expect(args).not.toContain('--storage-path');

      // 验证包含正确的参数
      expect(args).toContain('--storage-snapshot');
      expect(args).toContain('--config-path');

      // 验证参数顺序和配对
      const storageSnapshotIndex = args.indexOf('--storage-snapshot');
      const configPathIndex = args.indexOf('--config-path');

      expect(storageSnapshotIndex).toBeGreaterThan(-1);
      expect(configPathIndex).toBeGreaterThan(-1);

      // 验证参数后面跟着对应的路径值
      expect(args[storageSnapshotIndex + 1]).toMatch(/storage$/);
      expect(args[configPathIndex + 1]).toMatch(/config\.yaml$/);
    });

    test('应该处理jemalloc pthread警告', async () => {
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      // 模拟stderr输出jemalloc警告
      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => {
            callback(Buffer.from('<jemalloc>: option background_thread currently supports pthread only\n'));
          }, 100);
        }
      });

      // 模拟正常启动
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => {
            callback(Buffer.from('Actix runtime found\n'));
          }, 200);
        }
      });

      try {
        await qdrantServer.start(6633);
        // jemalloc警告不应该阻止启动
        expect(qdrantServer.getStatus()).toBe('running');
      } catch (error) {
        // 如果有其他错误，确保不是因为jemalloc警告
        expect(error.message).not.toContain('jemalloc');
      }
    });

    test('应该处理unexpected argument错误', async () => {
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      // 模拟stderr输出参数错误
      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => {
            callback(Buffer.from("error: unexpected argument '--storage-path' found\n"));
          }, 100);
        }
      });

      // 模拟进程因参数错误退出
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'exit') {
          setTimeout(() => callback(1, null), 200);
        }
      });

      await expect(qdrantServer.start(6633)).rejects.toThrow(/Qdrant服务异常退出/);
    });
  });

  describe('错误输出解析测试', () => {
    test('应该正确解析和记录错误信息', async () => {
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      const errorMessages: string[] = [];

      // 监听日志输出
      const originalLog = console.log;
      console.log = jest.fn((message) => {
        if (typeof message === 'string' && message.includes('[Qdrant Error]')) {
          errorMessages.push(message);
        }
      });

      // 模拟多种错误输出
      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => {
            callback(Buffer.from('<jemalloc>: option background_thread currently supports pthread only\n'));
          }, 50);
          setTimeout(() => {
            callback(Buffer.from("error: unexpected argument '--storage-path' found\n"));
          }, 100);
          setTimeout(() => {
            callback(Buffer.from("tip: a similar argument exists: '--storage-snapshot'\n"));
          }, 150);
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'exit') {
          setTimeout(() => callback(1, null), 300);
        }
      });

      try {
        await qdrantServer.start(6633);
      } catch (error) {
        // 验证错误被正确捕获和记录
        expect(errorMessages.length).toBeGreaterThan(0);
        expect(errorMessages.some((msg) => msg.includes('jemalloc'))).toBe(true);
        expect(errorMessages.some((msg) => msg.includes('unexpected argument'))).toBe(true);
      }

      console.log = originalLog;
    });
  });

  describe('启动命令构建测试', () => {
    test('应该构建正确的启动命令', () => {
      // 通过反射访问私有方法测试命令构建
      const dataPath = '/test/data/path';
      const port = 6633;

      // 模拟构建参数的逻辑
      const expectedArgs = [
        '--config-path',
        path.join(dataPath, 'config.yaml'),
        '--storage-snapshot',
        path.join(dataPath, 'storage'),
        '--log-level',
        'INFO',
        '--service-host',
        '127.0.0.1',
        '--service-port',
        port.toString(),
      ];

      // 验证参数结构
      expect(expectedArgs).toContain('--config-path');
      expect(expectedArgs).toContain('--storage-snapshot');
      expect(expectedArgs).not.toContain('--storage-path');

      // 验证参数配对
      const configIndex = expectedArgs.indexOf('--config-path');
      const storageIndex = expectedArgs.indexOf('--storage-snapshot');

      expect(expectedArgs[configIndex + 1]).toBe(path.join(dataPath, 'config.yaml'));
      expect(expectedArgs[storageIndex + 1]).toBe(path.join(dataPath, 'storage'));
    });

    test('应该验证所有必需的启动参数', () => {
      const requiredParams = ['--config-path', '--storage-snapshot', '--log-level', '--service-host', '--service-port'];

      const forbiddenParams = ['--storage-path'];

      // 这个测试确保我们知道哪些参数是必需的，哪些是禁止的
      requiredParams.forEach((param) => {
        expect(param).toBeDefined();
        expect(typeof param).toBe('string');
      });

      forbiddenParams.forEach((param) => {
        expect(param).toBe('--storage-path'); // 确认这是我们要避免的参数
      });
    });
  });

  describe('进程生命周期测试', () => {
    test('应该正确处理进程启动失败', async () => {
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      // 模拟进程启动时立即出错
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          setTimeout(() => callback(new Error('ENOENT: no such file or directory')), 50);
        }
      });

      await expect(qdrantServer.start(6633)).rejects.toThrow('ENOENT');
    });

    test('应该处理进程意外退出', async () => {
      const mockServer = {
        once: jest.fn((event, callback) => {
          if (event === 'listening') {
            setTimeout(() => callback(), 0);
          }
        }),
        close: jest.fn(),
        listen: jest.fn(),
      };

      require('net').createServer = jest.fn().mockReturnValue(mockServer);

      // 模拟进程启动后意外退出
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'exit') {
          setTimeout(() => callback(1, 'SIGTERM'), 100);
        }
      });

      await expect(qdrantServer.start(6633)).rejects.toThrow(/Qdrant服务异常退出.*退出码: 1/);
    });
  });
});
