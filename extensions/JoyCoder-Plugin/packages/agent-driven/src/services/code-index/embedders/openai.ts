import { OpenA<PERSON> } from 'openai';
import { ApiHandlerOptions } from '../../../shared/api';
import { IEmbedder, EmbeddingResponse, EmbedderInfo } from '../interfaces';
import {
  MAX_BATCH_TOKENS,
  MAX_ITEM_TOKENS,
  MAX_BATCH_RETRIES as MAX_RETRIES,
  INITIAL_RETRY_DELAY_MS as INITIAL_DELAY_MS,
} from '../constants';
import { OpenAiHandler } from '../../../adaptor/api/providers/openai';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { getBaseUrl, getJdhLoginInfo, isIDE, WorkspaceState } from '@joycoder/shared';

/**
 * OpenAI implementation of the embedder interface with batching and rate limiting
 */
export class OpenAiEmbedder extends OpenAiHandler implements IEmbedder {
  private embeddingsClient: OpenAI;
  private readonly defaultModelId: string;

  /**
   * Creates a new OpenAI embedder
   * @param options API handler options
   */
  constructor(options: ApiHandlerOptions & { openAiEmbeddingModelId?: string }) {
    super(options);
    const apiKey = this.options.apiKey ?? 'not-provided';
    const baseHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
    };

    const headers =
      isIDE() && getJdhLoginInfo()?.ptKey
        ? {
            ...baseHeaders,
            ptKey: getJdhLoginInfo()?.ptKey,
          }
        : baseHeaders;
    this.embeddingsClient = new OpenAI({
      apiKey,
      baseURL: options.openAiBaseUrl || `${getBaseUrl()}/api/saas/openai/v1/`,
      defaultHeaders: headers,
    });
    this.defaultModelId = options.openAiEmbeddingModelId || 'bge-code-v1';
  }

  /**
   * Creates embeddings for the given texts with batching and rate limiting
   * @param texts Array of text strings to embed
   * @param model Optional model identifier
   * @returns Promise resolving to embedding response
   */
  async createEmbeddings(texts: string[], model?: string): Promise<EmbeddingResponse> {
    const modelToUse = model || this.defaultModelId;
    const allEmbeddings: number[][] = [];
    const usage = { promptTokens: 0, totalTokens: 0 };
    const remainingTexts = [...texts];

    while (remainingTexts.length > 0) {
      const currentBatch: string[] = [];
      let currentBatchTokens = 0;
      const processedIndices: number[] = [];

      for (let i = 0; i < remainingTexts.length; i++) {
        const text = remainingTexts[i];
        const itemTokens = Math.ceil(text.length / 4);

        if (itemTokens > MAX_ITEM_TOKENS) {
          processedIndices.push(i);
          continue;
        }

        if (currentBatchTokens + itemTokens <= MAX_BATCH_TOKENS) {
          currentBatch.push(text);
          currentBatchTokens += itemTokens;
          processedIndices.push(i);
        } else {
          break;
        }
      }

      // Remove processed items from remainingTexts (in reverse order to maintain correct indices)
      for (let i = processedIndices.length - 1; i >= 0; i--) {
        remainingTexts.splice(processedIndices[i], 1);
      }

      if (currentBatch.length > 0) {
        const batchResult = await this._embedBatchWithRetries(currentBatch, modelToUse);
        allEmbeddings.push(...batchResult.embeddings);
        usage.promptTokens += batchResult.usage.promptTokens;
        usage.totalTokens += batchResult.usage.totalTokens;
      }
    }

    return { embeddings: allEmbeddings, usage };
  }

  /**
   * Helper method to handle batch embedding with retries and exponential backoff
   * @param batchTexts Array of texts to embed in this batch
   * @param model Model identifier to use
   * @returns Promise resolving to embeddings and usage statistics
   */
  private async _embedBatchWithRetries(
    batchTexts: string[],
    model: string
  ): Promise<{ embeddings: number[][]; usage: { promptTokens: number; totalTokens: number } }> {
    for (let attempts = 0; attempts < MAX_RETRIES; attempts++) {
      try {
        const label = WorkspaceState.get('openAiModelId');
        const modelConfig = getChatModelAndConfig(label);
        console.log(`Generating embeddings using model: ${model}`);

        const requestParams: any = {
          input: batchTexts,
          model: model,
        };

        // 添加业务相关参数（如果在自定义环境中需要）
        if (modelConfig?.bizId) {
          requestParams.bizId = modelConfig.bizId;
          requestParams.bizToken = modelConfig.bizToken;
        }

        const response = await this.embeddingsClient.embeddings.create(requestParams);
        console.log('%c [ response ]-126', 'font-size:13px; background:pink; color:#bf2c9f;', response);

        // 验证响应数据的完整性
        if (!response.data || !Array.isArray(response.data) || response.data.length === 0) {
          throw new Error('OpenAI API 返回的嵌入数据无效或为空');
        }

        // 验证每个嵌入向量的有效性
        const embeddings = response.data.map((item, index) => {
          if (!item.embedding || !Array.isArray(item.embedding) || item.embedding.length === 0) {
            throw new Error(`第 ${index + 1} 个文本的嵌入向量无效或为空`);
          }
          return item.embedding;
        });

        // 记录成功的嵌入生成
        console.log(`Successfully generated ${embeddings.length} embeddings for batch of ${batchTexts.length} texts`);

        return {
          embeddings,
          usage: {
            promptTokens: response.usage?.prompt_tokens || 0,
            totalTokens: response.usage?.total_tokens || 0,
          },
        };
      } catch (error: any) {
        const isRateLimitError = error?.status === 429;
        const hasMoreAttempts = attempts < MAX_RETRIES - 1;

        if (isRateLimitError && hasMoreAttempts) {
          const delayMs = INITIAL_DELAY_MS * Math.pow(2, attempts);
          await new Promise((resolve) => setTimeout(resolve, delayMs));
          continue;
        }

        // Log the error for debugging
        console.error(`OpenAI embedder error (attempt ${attempts + 1}/${MAX_RETRIES}):`, error);

        // Provide more context in the error message using robust error extraction
        let errorMessage = 'Unknown error';
        if (error?.message) {
          errorMessage = error.message;
        } else if (typeof error === 'string') {
          errorMessage = error;
        } else if (error && typeof error.toString === 'function') {
          try {
            errorMessage = error.toString();
          } catch {
            errorMessage = 'Unknown error';
          }
        }

        const statusCode = error?.status || error?.response?.status;

        if (statusCode === 401) {
          throw new Error(`创建嵌入失败：身份验证失败。请检查您的 API 密钥。`);
        } else if (statusCode) {
          throw new Error(`尝试 ${MAX_RETRIES} 次后创建嵌入失败：HTTP ${statusCode} - ${errorMessage}`);
        } else {
          throw new Error(`尝试 ${MAX_RETRIES} 次后创建嵌入失败：${errorMessage}`);
        }
      }
    }

    throw new Error(`尝试 ${MAX_RETRIES} 次后创建嵌入失败`);
  }

  get embedderInfo(): EmbedderInfo {
    return {
      name: 'openai',
    };
  }
}
