import { OpenAiEmbedder } from '../embedders/openai';
import { ApiHandlerOptions } from '../../../shared/api';

describe('OpenAiEmbedder', () => {
  let embedder: OpenAiEmbedder;
  const mockOptions: ApiHandlerOptions = {
    apiKey: 'test-key',
    openAiBaseUrl: 'http://test-api.example.com',
  };

  beforeEach(() => {
    embedder = new OpenAiEmbedder(mockOptions);
  });

  describe('constructor', () => {
    it('should initialize with default model', () => {
      expect(embedder['defaultModelId']).toBe('bge-code-v1');
    });

    it('should use custom model when provided', () => {
      const customEmbedder = new OpenAiEmbedder({
        ...mockOptions,
        openAiEmbeddingModelId: 'custom-model',
      });
      expect(customEmbedder['defaultModelId']).toBe('custom-model');
    });
  });

  describe('createEmbeddings', () => {
    it('should handle empty input array', async () => {
      const result = await embedder.createEmbeddings([]);
      expect(result.embeddings).toEqual([]);
      expect(result.usage).toEqual({ promptTokens: 0, totalTokens: 0 });
    });

    it('should process single text input correctly', async () => {
      // Mock the embeddings client
      const mockEmbedding = [0.1, 0.2, 0.3];
      jest.spyOn(embedder['embeddingsClient'].embeddings, 'create').mockResolvedValueOnce({
        data: [{ embedding: mockEmbedding }],
        usage: { prompt_tokens: 10, total_tokens: 10 },
      } as any);

      const result = await embedder.createEmbeddings(['test text']);

      expect(result.embeddings).toHaveLength(1);
      expect(result.embeddings[0]).toEqual(mockEmbedding);
      expect(result.usage).toEqual({ promptTokens: 10, totalTokens: 10 });
    });

    it('should handle batch processing correctly', async () => {
      const mockEmbeddings = [
        [0.1, 0.2],
        [0.3, 0.4],
      ];
      jest.spyOn(embedder['embeddingsClient'].embeddings, 'create').mockResolvedValueOnce({
        data: mockEmbeddings.map((embedding) => ({ embedding })),
        usage: { prompt_tokens: 20, total_tokens: 20 },
      } as any);

      const result = await embedder.createEmbeddings(['text1', 'text2']);

      expect(result.embeddings).toHaveLength(2);
      expect(result.embeddings).toEqual(mockEmbeddings);
    });

    it('should handle API errors correctly', async () => {
      jest.spyOn(embedder['embeddingsClient'].embeddings, 'create').mockRejectedValueOnce({
        status: 401,
        message: 'Invalid API key',
      });

      await expect(embedder.createEmbeddings(['test'])).rejects.toThrow('身份验证失败');
    });

    it('should respect rate limits and retry', async () => {
      const mockSuccess = {
        data: [{ embedding: [0.1, 0.2] }],
        usage: { prompt_tokens: 10, total_tokens: 10 },
      };

      jest
        .spyOn(embedder['embeddingsClient'].embeddings, 'create')
        .mockRejectedValueOnce({ status: 429, message: 'Rate limit exceeded' })
        .mockResolvedValueOnce(mockSuccess as any);

      const result = await embedder.createEmbeddings(['test']);

      expect(result.embeddings).toHaveLength(1);
      expect(result.embeddings[0]).toEqual([0.1, 0.2]);
    });
  });

  describe('embedderInfo', () => {
    it('should return correct embedder info', () => {
      expect(embedder.embedderInfo).toEqual({ name: 'openai' });
    });
  });
});
