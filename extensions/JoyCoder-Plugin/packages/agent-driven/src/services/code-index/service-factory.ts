import { OpenAiEmbedder } from './embedders/openai';
import { EmbedderProvider, getModelDimension } from '../../shared/embeddingModels';
import { QdrantVectorStore } from './vector-store/qdrant-client';
import { IEmbedder, IVectorStore } from './interfaces';
import { getBaseUrl } from '@joycoder/shared';

/**
 * Factory class responsible for creating and configuring code indexing service dependencies.
 */
export class CodeIndexServiceFactory {
  constructor(private readonly workspacePath: string) {}

  /**
   * Creates an embedder instance based on the current configuration.
   */
  public createEmbedder(): IEmbedder {
    return new OpenAiEmbedder({
      openAiBaseUrl: `${getBaseUrl()}/api/saas/openai/v1/`,
      openAiEmbeddingModelId: 'bge-code-v1',
    });
  }
  /**
   * Creates a vector store instance using the current configuration.
   */
  public createVectorStore(): IVectorStore {
    const provider = 'openai' as EmbedderProvider;
    const defaultModel = 'bge-code-v1';
    // Use the embedding model ID from config, not the chat model IDs
    const modelId = defaultModel;
    const vectorSize = getModelDimension(provider, modelId) ?? 1536; // Default to 1536 if not found
    // Assuming constructor is updated: new QdrantVectorStore(workspacePath, url, vectorSize, apiKey?)
    return new QdrantVectorStore(this.workspacePath, 'http://127.0.0.1:6333', vectorSize, '');
  }

  /**
   * Creates all required service dependencies if the service is properly configured.
   * @throws Error if the service is not properly configured
   */
  public createServices(): {
    embedder: IEmbedder;
    vectorStore: IVectorStore;
  } {
    const embedder = this.createEmbedder();
    const vectorStore = this.createVectorStore();

    return {
      embedder,
      vectorStore,
    };
  }
}
