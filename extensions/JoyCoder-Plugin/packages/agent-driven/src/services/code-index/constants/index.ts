/**Parser */
export const MAX_BLOCK_CHARS = 1000;
export const MIN_BLOCK_CHARS = 100;
export const MIN_CHUNK_REMAINDER_CHARS = 200; // Minimum characters for the *next* chunk after a split
export const MAX_CHARS_TOLERANCE_FACTOR = 1.15; // 15% tolerance for max chars

/**Search */
export const SEARCH_MIN_SCORE = 0.4;
export const MAX_SEARCH_RESULTS = 50; // Maximum number of search results to return

/**File Watcher */
export const QDRANT_CODE_BLOCK_NAMESPACE = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
export const MAX_FILE_SIZE_BYTES = 1 * 1024 * 1024; // 1MB

/**Directory Scanner */
export const MAX_LIST_FILES_LIMIT = 3_000;
export const BATCH_SEGMENT_THRESHOLD = 60; // Number of code segments to batch for embeddings/upserts
export const MAX_BATCH_RETRIES = 3;
export const INITIAL_RETRY_DELAY_MS = 500;
export const PARSING_CONCURRENCY = 10;

/**OpenAI Embedder */
export const MAX_BATCH_TOKENS = 100000;
export const MAX_ITEM_TOKENS = 8191;
export const BATCH_PROCESSING_CONCURRENCY = 10;
