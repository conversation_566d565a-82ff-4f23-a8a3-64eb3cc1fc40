import pWaitFor from 'p-wait-for';
import * as vscode from 'vscode';
import { arePathsEqual } from '../../utils/path';
import { mergePromise, TerminalProcess, TerminalProcessResultPromise } from './TerminalProcess';
import { TerminalInfo, TerminalRegistry } from './TerminalRegistry';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

/*
TerminalManager:
- Creates/reuses terminals
- Runs commands via runCommand(), returning a TerminalProcess
- Handles shell integration events

TerminalProcess extends EventEmitter and implements Promise:
- Emits 'line' events with output while promise is pending
- process.continue() resolves promise and stops event emission
- Allows real-time output handling or background execution

getUnretrievedOutput() fetches latest output for ongoing commands

Enables flexible command execution:
- Await for completion
- Listen to real-time events
- Continue execution in background
- Retrieve missed output later

Notes:
- it turns out some shellIntegration APIs are available on cursor, although not on older versions of vscode
- "By default, the shell integration script should automatically activate on supported shells launched from VS Code."
Supported shells:
Linux/macOS: bash, fish, pwsh, zsh
Windows: pwsh


Example:

const terminalManager = new TerminalManager(context);

// Run a command
const process = terminalManager.runCommand('npm install', '/path/to/project');

process.on('line', (line) => {
    console.log(line);
});

// To wait for the process to complete naturally:
await process;

// Or to continue execution even if the command is still running:
process.continue();

// Later, if you need to get the unretrieved output:
const unretrievedOutput = terminalManager.getUnretrievedOutput(terminalId);
console.log('Unretrieved output:', unretrievedOutput);

Resources:
- https://github.com/microsoft/vscode/issues/226655
- https://code.visualstudio.com/updates/v1_93#_terminal-shell-integration-api
- https://code.visualstudio.com/docs/terminal/shell-integration
- https://code.visualstudio.com/api/references/vscode-api#Terminal
- https://github.com/microsoft/vscode-extension-samples/blob/main/terminal-sample/src/extension.ts
- https://github.com/microsoft/vscode-extension-samples/blob/main/shell-integration-sample/src/extension.ts
*/

/*
The new shellIntegration API gives us access to terminal command execution output handling.
However, we don't update our VSCode type definitions or engine requirements to maintain compatibility
with older VSCode versions. Users on older versions will automatically fall back to using sendText
for terminal command execution.
Interestingly, some environments like Cursor enable these APIs even without the latest VSCode engine.
This approach allows us to leverage advanced features when available while ensuring broad compatibility.
*/
declare module 'vscode' {
  // https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L7442
  interface Terminal {
    // @ts-ignore
    shellIntegration?: {
      cwd?: vscode.Uri;
      executeCommand?: (command: string) => {
        read: () => AsyncIterable<string>;
      };
    };
  }
  // https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L10794
  interface Window {
    onDidStartTerminalShellExecution?: (
      listener: (e: any) => any,
      thisArgs?: any,
      disposables?: vscode.Disposable[]
    ) => vscode.Disposable;
  }
}

export class TerminalManager {
  terminalIds: Set<number> = new Set();
  processes: Map<number, TerminalProcess> = new Map();
  private disposables: vscode.Disposable[] = [];
  private shellIntegrationTimeout: number = 4000;

  constructor() {
    let disposable: vscode.Disposable | undefined;
    try {
      disposable = (vscode.window as vscode.Window).onDidStartTerminalShellExecution?.(async (e) => {
        // Creating a read stream here results in a more consistent output. This is most obvious when running the `date` command.
        e?.execution?.read();
      });
    } catch (error) {
      // console.error("Error setting up onDidEndTerminalShellExecution", error)
    }
    if (disposable) {
      this.disposables.push(disposable);
    }
    // Add a listener for terminal state changes to detect CWD updates
    try {
      const stateChangeDisposable = vscode.window.onDidChangeTerminalState((terminal) => {
        const terminalInfo = this.findTerminalInfoByTerminal(terminal);
        if (terminalInfo && terminalInfo.pendingCwdChange && terminalInfo.cwdResolved) {
          // Check if CWD has been updated to match the expected path
          if (this.isCwdMatchingExpected(terminalInfo)) {
            const resolver = terminalInfo.cwdResolved.resolve;
            terminalInfo.pendingCwdChange = undefined;
            terminalInfo.cwdResolved = undefined;
            resolver();
          }
        }
      });
      this.disposables.push(stateChangeDisposable);
    } catch (error) {
      console.error('Error setting up onDidChangeTerminalState', error);
    }
  }

  //Find a TerminalInfo by its VSCode Terminal instance
  private findTerminalInfoByTerminal(terminal: vscode.Terminal): TerminalInfo | undefined {
    const terminals = TerminalRegistry.getAllTerminals();
    return terminals.find((t) => t.terminal === terminal);
  }

  //Check if a terminal's CWD matches its expected pending change
  private isCwdMatchingExpected(terminalInfo: TerminalInfo): boolean {
    if (!terminalInfo.pendingCwdChange) {
      return false;
    }

    const currentCwd = terminalInfo.terminal.shellIntegration?.cwd?.fsPath;
    const targetCwd = vscode.Uri.file(FileSystemHelper.getRemotePath(terminalInfo.pendingCwdChange)).fsPath;

    if (!currentCwd) {
      return false;
    }

    return arePathsEqual(currentCwd, targetCwd);
  }

  runCommand(terminalInfo: TerminalInfo, command: string): TerminalProcessResultPromise {
    terminalInfo.busy = true;
    terminalInfo.lastCommand = command;
    const process = new TerminalProcess();
    this.processes.set(terminalInfo.id, process);

    process.once('completed', () => {
      terminalInfo.busy = false;
    });

    // if shell integration is not available, remove terminal so it does not get reused as it may be running a long-running process
    process.once('no_shell_integration', () => {
      console.log(`no_shell_integration received for terminal ${terminalInfo.id}`);
      // Remove the terminal so we can't reuse it (in case it's running a long-running process)
      TerminalRegistry.removeTerminal(terminalInfo.id);
      this.terminalIds.delete(terminalInfo.id);
      this.processes.delete(terminalInfo.id);
    });

    const promise = new Promise<void>((resolve, reject) => {
      process.once('continue', () => {
        resolve();
      });
      process.once('error', (error) => {
        console.error(`Error in terminal ${terminalInfo.id}:`, error);
        reject(error);
      });
    });

    // if shell integration is already active, run the command immediately
    if (terminalInfo.terminal.shellIntegration) {
      process.waitForShellIntegration = false;
      process.run(terminalInfo.terminal, command);
    } else {
      // docs recommend waiting 3s for shell integration to activate
      pWaitFor(() => terminalInfo.terminal.shellIntegration !== undefined, {
        timeout: this.shellIntegrationTimeout,
      }).finally(() => {
        const existingProcess = this.processes.get(terminalInfo.id);
        if (existingProcess && existingProcess.waitForShellIntegration) {
          existingProcess.waitForShellIntegration = false;
          existingProcess.run(terminalInfo.terminal, command);
        }
      });
    }

    return mergePromise(process, promise);
  }

  async getOrCreateTerminal(cwd: string | vscode.Uri): Promise<TerminalInfo> {
    const terminals = TerminalRegistry.getAllTerminals();

    // Find available terminal from our pool first (created for this task)
    const matchingTerminal = terminals.find((t) => {
      if (t.busy) {
        return false;
      }
      const terminalCwd = t.terminal.shellIntegration?.cwd; // one of joycoder's commands could have changed the cwd of the terminal
      if (!terminalCwd) {
        return false;
      }
      return arePathsEqual(vscode.Uri.file(cwd.toString()).fsPath, terminalCwd.fsPath);
    });
    if (matchingTerminal) {
      this.terminalIds.add(matchingTerminal.id);
      return matchingTerminal;
    }

    // If no matching terminal exists, try to find any non-busy terminal
    const availableTerminal = terminals.find((t) => !t.busy);
    if (availableTerminal) {
      // Set up promise and tracking for CWD change
      const cwdPromise = new Promise<void>((resolve, reject) => {
        availableTerminal.pendingCwdChange = cwd;
        availableTerminal.cwdResolved = { resolve, reject };
      });
      // Navigate back to the desired directory
      await this.runCommand(availableTerminal, `cd "${FileSystemHelper.getRemotePath(cwd)}"`);

      // Either resolve immediately if CWD already updated or wait for event/timeout
      if (this.isCwdMatchingExpected(availableTerminal)) {
        if (availableTerminal.cwdResolved) {
          availableTerminal.cwdResolved.resolve();
        }
        availableTerminal.pendingCwdChange = undefined;
        availableTerminal.cwdResolved = undefined;
      } else {
        try {
          // Wait with a timeout for state change event to resolve
          await Promise.race([
            cwdPromise,
            new Promise<void>((_, reject) =>
              setTimeout(() => reject(new Error(`CWD timeout: Failed to update to ${cwd}`)), 1000)
            ),
          ]);
        } catch (err) {
          // Clear pending state on timeout
          availableTerminal.pendingCwdChange = undefined;
          availableTerminal.cwdResolved = undefined;
        }
      }
      this.terminalIds.add(availableTerminal.id);
      return availableTerminal;
    }

    // If all terminals are busy, create a new one
    const newTerminalInfo = TerminalRegistry.createTerminal(cwd);
    this.terminalIds.add(newTerminalInfo.id);
    return newTerminalInfo;
  }

  getTerminals(busy: boolean): { id: number; lastCommand: string }[] {
    return Array.from(this.terminalIds)
      .map((id) => TerminalRegistry.getTerminal(id))
      .filter((t): t is TerminalInfo => t !== undefined && t.busy === busy)
      .map((t) => ({ id: t.id, lastCommand: t.lastCommand }));
  }

  /**
   * 获取指定终端的未检索输出
   * @param terminalId 终端ID
   * @returns 未检索的输出字符串，如果终端不存在则返回空字符串
   */
  getUnretrievedOutput(terminalId: number): string {
    if (!this.terminalIds.has(terminalId)) {
      return '';
    }
    const process = this.processes.get(terminalId);
    return process ? process.getUnretrievedOutput() : '';
  }

  isProcessHot(terminalId: number): boolean {
    const process = this.processes.get(terminalId);
    return process ? process.isHot : false;
  }

  disposeAll() {
    // for (const info of this.terminals) {
    // 	//info.terminal.dispose() // dont want to dispose terminals when task is aborted
    // }
    this.terminalIds.clear();
    this.processes.clear();
    this.disposables.forEach((disposable) => disposable.dispose());
    this.disposables = [];
  }

  setShellIntegrationTimeout(timeout: number): void {
    this.shellIntegrationTimeout = timeout;
  }
}
