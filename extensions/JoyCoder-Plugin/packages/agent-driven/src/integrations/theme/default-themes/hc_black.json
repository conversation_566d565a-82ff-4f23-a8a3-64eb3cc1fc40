{"$schema": "vscode://schemas/color-theme", "name": "Dark High Contrast", "colors": {"editor.background": "#000000", "editor.foreground": "#FFFFFF", "editorIndentGuide.background1": "#FFFFFF", "editorIndentGuide.activeBackground1": "#FFFFFF", "sideBarTitle.foreground": "#FFFFFF", "selection.background": "#008000", "editor.selectionBackground": "#FFFFFF", "statusBarItem.remoteBackground": "#00000000", "ports.iconRunningProcessForeground": "#FFFFFF", "editorWhitespace.foreground": "#7c7c7c", "actionBar.toggledBackground": "#383a49"}, "tokenColors": [{"scope": ["meta.embedded", "source.groovy.embedded", "string meta.image.inline.markdown", "variable.legacy.builtin.python"], "settings": {"foreground": "#FFFFFF"}}, {"scope": "emphasis", "settings": {"fontStyle": "italic"}}, {"scope": "strong", "settings": {"fontStyle": "bold"}}, {"scope": "meta.diff.header", "settings": {"foreground": "#000080"}}, {"scope": "comment", "settings": {"foreground": "#7ca668"}}, {"scope": "constant.language", "settings": {"foreground": "#569cd6"}}, {"scope": ["constant.numeric", "constant.other.color.rgb-value", "constant.other.rgb-value", "support.constant.color"], "settings": {"foreground": "#b5cea8"}}, {"scope": "constant.regexp", "settings": {"foreground": "#b46695"}}, {"scope": "constant.character", "settings": {"foreground": "#569cd6"}}, {"scope": "entity.name.tag", "settings": {"foreground": "#569cd6"}}, {"scope": ["entity.name.tag.css", "entity.name.tag.less"], "settings": {"foreground": "#d7ba7d"}}, {"scope": "entity.other.attribute-name", "settings": {"foreground": "#9cdcfe"}}, {"scope": ["entity.other.attribute-name.class.css", "source.css entity.other.attribute-name.class", "entity.other.attribute-name.id.css", "entity.other.attribute-name.parent-selector.css", "entity.other.attribute-name.parent.less", "source.css entity.other.attribute-name.pseudo-class", "entity.other.attribute-name.pseudo-element.css", "source.css.less entity.other.attribute-name.id", "entity.other.attribute-name.scss"], "settings": {"foreground": "#d7ba7d"}}, {"scope": "invalid", "settings": {"foreground": "#f44747"}}, {"scope": "markup.underline", "settings": {"fontStyle": "underline"}}, {"scope": "markup.bold", "settings": {"fontStyle": "bold"}}, {"scope": "markup.heading", "settings": {"fontStyle": "bold", "foreground": "#6796e6"}}, {"scope": "markup.italic", "settings": {"fontStyle": "italic"}}, {"scope": "markup.strikethrough", "settings": {"fontStyle": "strikethrough"}}, {"scope": "markup.inserted", "settings": {"foreground": "#b5cea8"}}, {"scope": "markup.deleted", "settings": {"foreground": "#ce9178"}}, {"scope": "markup.changed", "settings": {"foreground": "#569cd6"}}, {"name": "brackets of XML/HTML tags", "scope": ["punctuation.definition.tag"], "settings": {"foreground": "#808080"}}, {"scope": "meta.preprocessor", "settings": {"foreground": "#569cd6"}}, {"scope": "meta.preprocessor.string", "settings": {"foreground": "#ce9178"}}, {"scope": "meta.preprocessor.numeric", "settings": {"foreground": "#b5cea8"}}, {"scope": "meta.structure.dictionary.key.python", "settings": {"foreground": "#9cdcfe"}}, {"scope": "storage", "settings": {"foreground": "#569cd6"}}, {"scope": "storage.type", "settings": {"foreground": "#569cd6"}}, {"scope": "storage.modifier", "settings": {"foreground": "#569cd6"}}, {"scope": "string", "settings": {"foreground": "#ce9178"}}, {"scope": "string.tag", "settings": {"foreground": "#ce9178"}}, {"scope": "string.value", "settings": {"foreground": "#ce9178"}}, {"scope": "string.regexp", "settings": {"foreground": "#d16969"}}, {"name": "String interpolation", "scope": ["punctuation.definition.template-expression.begin", "punctuation.definition.template-expression.end", "punctuation.section.embedded"], "settings": {"foreground": "#569cd6"}}, {"name": "Reset JavaScript string interpolation expression", "scope": ["meta.template.expression"], "settings": {"foreground": "#ffffff"}}, {"scope": ["support.type.vendored.property-name", "support.type.property-name", "source.css variable", "source.coffee.embedded"], "settings": {"foreground": "#d4d4d4"}}, {"scope": "keyword", "settings": {"foreground": "#569cd6"}}, {"scope": "keyword.control", "settings": {"foreground": "#569cd6"}}, {"scope": "keyword.operator", "settings": {"foreground": "#d4d4d4"}}, {"scope": ["keyword.operator.new", "keyword.operator.expression", "keyword.operator.cast", "keyword.operator.sizeof", "keyword.operator.logical.python"], "settings": {"foreground": "#569cd6"}}, {"scope": "keyword.other.unit", "settings": {"foreground": "#b5cea8"}}, {"scope": "support.function.git-rebase", "settings": {"foreground": "#d4d4d4"}}, {"scope": "constant.sha.git-rebase", "settings": {"foreground": "#b5cea8"}}, {"name": "coloring of the Java import and package identifiers", "scope": ["storage.modifier.import.java", "variable.language.wildcard.java", "storage.modifier.package.java"], "settings": {"foreground": "#d4d4d4"}}, {"name": "coloring of the TS this", "scope": "variable.language.this", "settings": {"foreground": "#569cd6"}}, {"name": "Function declarations", "scope": ["entity.name.function", "support.function", "support.constant.handlebars", "source.powershell variable.other.member"], "settings": {"foreground": "#DCDCAA"}}, {"name": "Types declaration and references", "scope": ["support.class", "support.type", "entity.name.type", "entity.name.namespace", "entity.name.scope-resolution", "entity.name.class", "storage.type.cs", "storage.type.generic.cs", "storage.type.modifier.cs", "storage.type.variable.cs", "storage.type.annotation.java", "storage.type.generic.java", "storage.type.java", "storage.type.object.array.java", "storage.type.primitive.array.java", "storage.type.primitive.java", "storage.type.token.java", "storage.type.groovy", "storage.type.annotation.groovy", "storage.type.parameters.groovy", "storage.type.generic.groovy", "storage.type.object.array.groovy", "storage.type.primitive.array.groovy", "storage.type.primitive.groovy"], "settings": {"foreground": "#4EC9B0"}}, {"name": "Types declaration and references, TS grammar specific", "scope": ["meta.type.cast.expr", "meta.type.new.expr", "support.constant.math", "support.constant.dom", "support.constant.json", "entity.other.inherited-class"], "settings": {"foreground": "#4EC9B0"}}, {"name": "Control flow / Special keywords", "scope": ["keyword.control", "source.cpp keyword.operator.new", "source.cpp keyword.operator.delete", "keyword.other.using", "keyword.other.directive.using", "keyword.other.operator"], "settings": {"foreground": "#C586C0"}}, {"name": "Variable and parameter name", "scope": ["variable", "meta.definition.variable.name", "support.variable"], "settings": {"foreground": "#9CDCFE"}}, {"name": "Object keys, TS grammar specific", "scope": ["meta.object-literal.key"], "settings": {"foreground": "#9CDCFE"}}, {"name": "CSS property value", "scope": ["support.constant.property-value", "support.constant.font-name", "support.constant.media-type", "support.constant.media", "constant.other.color.rgb-value", "constant.other.rgb-value", "support.constant.color"], "settings": {"foreground": "#CE9178"}}, {"name": "HC Search Editor context line override", "scope": "meta.resultLinePrefix.contextLinePrefix.search", "settings": {"foreground": "#CBEDCB"}}], "semanticHighlighting": true, "semanticTokenColors": {"newOperator": "#FFFFFF", "stringLiteral": "#ce9178", "customLiteral": "#DCDCAA", "numberLiteral": "#b5cea8"}}