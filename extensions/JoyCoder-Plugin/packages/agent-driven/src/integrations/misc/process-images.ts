import * as vscode from 'vscode';
import fs from 'fs/promises';
import * as path from 'path';

export async function selectImages(): Promise<string[]> {
  const isRemote = vscode.env.remoteName !== undefined;
  if (isRemote) {
    return selectImagesRemote();
  } else {
    return selectImagesLocal();
  }
}

async function selectImagesRemote(): Promise<string[]> {
  const options: vscode.OpenDialogOptions = {
    canSelectMany: true,
    openLabel: 'Select',
    filters: {
      Images: ['png', 'jpg', 'jpeg', 'webp'],
    },
    // 使用本地文件系统
    defaultUri: vscode.Uri.file(process.env.HOME || process.env.USERPROFILE || '/'),
  };

  // 在本地环境打开文件选择器
  const fileUris = await vscode.window.showOpenDialog(options);

  if (!fileUris || fileUris.length === 0) {
    return [];
  }

  // 处理本地文件并转换为 base64
  return await Promise.all(
    fileUris.map(async (uri) => {
      const imagePath = uri.fsPath;
      const buffer = await fs.readFile(imagePath);
      const base64 = buffer.toString('base64');
      const mimeType = getMimeType(imagePath);
      const dataUrl = `data:${mimeType};base64,${base64}`;
      return dataUrl;
    })
  );
}

async function selectImagesLocal(): Promise<string[]> {
  const options: vscode.OpenDialogOptions = {
    canSelectMany: true,
    openLabel: 'Select',
    filters: {
      Images: ['png', 'jpg', 'jpeg', 'webp'], // supported by anthropic and openrouter
    },
  };
  const fileUris = await vscode.window.showOpenDialog(options);

  if (!fileUris || fileUris.length === 0) {
    return [];
  }

  return await Promise.all(
    fileUris.map(async (uri) => {
      const imagePath = uri.fsPath;
      const buffer = await fs.readFile(imagePath);
      const base64 = buffer.toString('base64');
      const mimeType = getMimeType(imagePath);
      const dataUrl = `data:${mimeType};base64,${base64}`;
      return dataUrl;
    })
  );
}

function getMimeType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.png':
      return 'image/png';
    case '.jpeg':
    case '.jpg':
      return 'image/jpeg';
    case '.webp':
      return 'image/webp';
    default:
      throw new Error(`文件类型不支持: ${ext}`);
    // throw new Error(`Unsupported file type: ${ext}`);
  }
}
