// @ts-ignore-next-line
import pdf from 'pdf-parse/lib/pdf-parse';
import mammoth from 'mammoth';
import fs from 'fs/promises';
import * as vscode from 'vscode';
import { isBinaryFile } from 'isbinaryfile';
import { getFileSizeInKB, isRemoteEnvironment } from '../../utils/fs';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { safeDecodeURI } from '../../utils/string';
import * as chardet from 'jschardet';
import ExcelJS from 'exceljs';

export async function detectEncoding(fileBuffer: Buffer, fileExtension?: string): Promise<string> {
  const detected = chardet.detect(fileBuffer);
  if (typeof detected === 'string') {
    return detected;
  } else if (detected && (detected as any).encoding) {
    return (detected as any).encoding;
  } else {
    if (fileExtension) {
      const isBinary = await isBinaryFile(fileBuffer).catch(() => false);
      if (isBinary) {
        throw new Error(`不支持读取此文件类型: ${fileExtension}`);
      }
    }
    return 'utf8';
  }
}

export async function extractTextFromFile(filePath: string | vscode.Uri): Promise<string> {
  try {
    await FileSystemHelper.access(filePath);
  } catch (error) {
    // 提供更详细的错误信息，包括原始路径，对中文文件名进行解码
    const pathStr = typeof filePath === 'string' ? filePath : filePath.toString();
    const decodedPathStr = safeDecodeURI(pathStr);
    const errorMsg = error instanceof Error ? error.message : String(error);
    throw new Error(`文件没找到: ${decodedPathStr} (${errorMsg})`);
  }
  const fileExtension = FileSystemHelper.extname(filePath).toLowerCase();
  switch (fileExtension) {
    case '.pdf':
      return extractTextFromPDF(filePath);
    case '.docx':
      return extractTextFromDOCX(FileSystemHelper.getRemotePath(filePath));
    case '.ipynb':
      return extractTextFromIPYNB(filePath);
    case '.xlsx':
      return extractTextFromExcel(filePath);
    default: // TODO: isBinaryFile是否支持远程服务待定
      const isBinary = await isBinaryFile(FileSystemHelper.getRemotePath(filePath)).catch(() => false);
      if (!isBinary) {
        // If file is over 300KB, throw an error
        const fileSizeInKB = await getFileSizeInKB(filePath);
        if (fileSizeInKB > 300) {
          // throw new Error(`文件太大,无法读入上下文.`);
          return '(The file is too large to be read into the context)';
        }
        return await FileSystemHelper.readFile(filePath, 'utf8');
      } else {
        return '(Binary file, unable to display content)';
        // throw new Error(`无法读取该文件类型的文本: ${fileExtension}`);
      }
  }
}
async function extractTextFromPDF(filePath: string | vscode.Uri): Promise<string> {
  const dataBuffer = isRemoteEnvironment()
    ? await FileSystemHelper.readFile(filePath)
    : await fs.readFile(filePath as string);
  const data = await pdf(dataBuffer);
  return data.text;
}

async function extractTextFromDOCX(filePath: string): Promise<string> {
  const result = await mammoth.extractRawText({ path: filePath });
  return result.value;
}

async function extractTextFromIPYNB(filePath: string | vscode.Uri): Promise<string> {
  const data = isRemoteEnvironment()
    ? await FileSystemHelper.readFile(filePath, 'utf8')
    : await fs.readFile(filePath as string, 'utf8');
  const notebook = JSON.parse(data);
  let extractedText = '';

  for (const cell of notebook.cells) {
    if ((cell.cell_type === 'markdown' || cell.cell_type === 'code') && cell.source) {
      extractedText += cell.source.join('\n') + '\n';
    }
  }

  return extractedText;
}

/**
 * Extract and format text from xlsx files
 */
async function extractTextFromExcel(filePath: string | vscode.Uri): Promise<string> {
  const workbook = new ExcelJS.Workbook();
  let excelText = '';

  try {
    const fexcelPath = FileSystemHelper.getRemotePath(filePath);
    await workbook.xlsx.readFile(fexcelPath);

    workbook.eachSheet(
      (
        worksheet: {
          state: string;
          name: any;
          eachRow: (arg0: { includeEmpty: boolean }, arg1: (row: any, rowNumber: any) => boolean) => void;
        },
        sheetId: any
      ) => {
        // Skip hidden sheets
        if (worksheet.state === 'hidden' || worksheet.state === 'veryHidden') {
          return;
        }

        excelText += `--- Sheet: ${worksheet.name} ---\n`;

        worksheet.eachRow(
          { includeEmpty: false },
          (
            row: { eachCell: (arg0: { includeEmpty: boolean }, arg1: (cell: any, colNumber: any) => void) => void },
            rowNumber: number
          ) => {
            // Optional: limit processing for very large sheets
            if (rowNumber > 50000) {
              excelText += `[... truncated at row ${rowNumber} ...]\n`;
              return false;
            }

            const rowTexts: string[] = [];
            let hasContent = false;

            row.eachCell({ includeEmpty: true }, (cell: any, colNumber: any) => {
              const cellText = formatCellValue(cell);
              if (cellText.trim()) {
                hasContent = true;
              }
              rowTexts.push(cellText);
            });

            // Only add rows with actual content
            if (hasContent) {
              excelText += rowTexts.join('\t') + '\n';
            }

            return true;
          }
        );

        excelText += '\n'; // Blank line between sheets
      }
    );

    return excelText.trim();
  } catch (error: any) {
    console.error(`Error extracting text from Excel ${filePath}:`, error);
    throw new Error(`Failed to extract text from Excel: ${error.message}`);
  }
}

/**
 * Format the data inside Excel cells
 */
function formatCellValue(cell: ExcelJS.Cell): string {
  const value = cell.value;
  if (value === null || value === undefined) {
    return '';
  }

  // Handle error values (#DIV/0!, #N/A, etc.)
  if (typeof value === 'object' && 'error' in value) {
    return `[Error: ${value.error}]`;
  }

  // Handle dates - ExcelJS can parse them as Date objects
  if (value instanceof Date) {
    return value.toISOString().split('T')[0]; // Just the date part
  }

  // Handle rich text
  if (typeof value === 'object' && 'richText' in value) {
    return value.richText.map((rt: any) => rt.text).join('');
  }

  // Handle hyperlinks
  if (typeof value === 'object' && 'text' in value && 'hyperlink' in value) {
    return `${value.text} (${value.hyperlink})`;
  }

  // Handle formulas - get the calculated result
  if (typeof value === 'object' && 'formula' in value) {
    if ('result' in value && value.result !== undefined && value.result !== null) {
      return value.result.toString();
    } else {
      return `[Formula: ${value.formula}]`;
    }
  }

  return value.toString();
}

export function addLineNumbers(content: string, startLine: number = 1): string {
  // If content is empty, return empty string - empty files should not have line numbers
  // If content is empty but startLine > 1, return "startLine | " because we know the file is not empty
  // but the content is empty at that line offset
  if (content === '') {
    return startLine === 1 ? '' : `${startLine} | \n`;
  }

  // Split into lines and handle trailing line feeds (\n)
  const lines = content.split('\n');
  const lastLineEmpty = lines[lines.length - 1] === '';
  if (lastLineEmpty) {
    lines.pop();
  }

  const maxLineNumberWidth = String(startLine + lines.length - 1).length;
  const numberedContent = lines
    .map((line, index) => {
      const lineNumber = String(startLine + index).padStart(maxLineNumberWidth, ' ');
      return `${lineNumber} | ${line}`;
    })
    .join('\n');

  return numberedContent + '\n';
}
// Checks if every line in the content has line numbers prefixed (e.g., "1 | content" or "123 | content")
// Line numbers must be followed by a single pipe character (not double pipes)
export function everyLineHasLineNumbers(content: string): boolean {
  const lines = content.split(/\r?\n/); // Handles both CRLF (carriage return (\r) + line feed (\n)) and LF (line feed (\n)) line endings
  return lines.length > 0 && lines.every((line) => /^\s*\d+\s+\|(?!\|)/.test(line));
}

/**
 * Strips line numbers from content while preserving the actual content.
 *
 * @param content The content to process
 * @param aggressive When false (default): Only strips lines with clear number patterns like "123 | content"
 *                   When true: Uses a more lenient pattern that also matches lines with just a pipe character,
 *                   which can be useful when LLMs don't perfectly format the line numbers in diffs
 * @returns The content with line numbers removed
 */
export function stripLineNumbers(content: string, aggressive: boolean = false): string {
  // Split into lines to handle each line individually
  const lines = content.split(/\r?\n/);

  // Process each line
  const processedLines = lines.map((line) => {
    // Match line number pattern and capture everything after the pipe
    const match = aggressive ? line.match(/^\s*(?:\d+\s)?\|\s(.*)$/) : line.match(/^\s*\d+\s+\|(?!\|)\s?(.*)$/);
    return match ? match[1] : line;
  });

  // Join back with original line endings (carriage return (\r) + line feed (\n) or just line feed (\n))
  const lineEnding = content.includes('\r\n') ? '\r\n' : '\n';
  return processedLines.join(lineEnding);
}

/**
 * Truncates multi-line output while preserving context from both the beginning and end.
 * When truncation is needed, it keeps 20% of the lines from the start and 80% from the end,
 * with a clear indicator of how many lines were omitted in between.
 *
 * @param content The multi-line string to truncate
 * @param lineLimit Optional maximum number of lines to keep. If not provided or 0, returns the original content
 * @returns The truncated string with an indicator of omitted lines, or the original content if no truncation needed
 *
 * @example
 * // With 10 line limit on 25 lines of content:
 * // - Keeps first 2 lines (20% of 10)
 * // - Keeps last 8 lines (80% of 10)
 * // - Adds "[...15 lines omitted...]" in between
 */
export function truncateOutput(content: string, lineLimit?: number): string {
  if (!lineLimit) {
    return content;
  }

  // Count total lines
  let totalLines = 0;
  let pos = -1;
  while ((pos = content.indexOf('\n', pos + 1)) !== -1) {
    totalLines++;
  }
  totalLines++; // Account for last line without line feed (\n)

  if (totalLines <= lineLimit) {
    return content;
  }

  const beforeLimit = Math.floor(lineLimit * 0.2); // 20% of lines before
  const afterLimit = lineLimit - beforeLimit; // remaining 80% after

  // Find start section end position
  let startEndPos = -1;
  let lineCount = 0;
  pos = 0;
  while (lineCount < beforeLimit && (pos = content.indexOf('\n', pos)) !== -1) {
    startEndPos = pos;
    lineCount++;
    pos++;
  }

  // Find end section start position
  let endStartPos = content.length;
  lineCount = 0;
  pos = content.length;
  while (lineCount < afterLimit && (pos = content.lastIndexOf('\n', pos - 1)) !== -1) {
    endStartPos = pos + 1; // Start after the line feed (\n)
    lineCount++;
  }

  const omittedLines = totalLines - lineLimit;
  const startSection = content.slice(0, startEndPos + 1);
  const endSection = content.slice(endStartPos);
  return startSection + `\n[...${omittedLines} lines omitted...]\n\n` + endSection;
}

/**
 * Applies run-length encoding to compress repeated lines in text.
 * Only compresses when the compression description is shorter than the repeated content.
 *
 * @param content The text content to compress
 * @returns The compressed text with run-length encoding applied
 */
export function applyRunLengthEncoding(content: string): string {
  if (!content) {
    return content;
  }

  let result = '';
  let pos = 0;
  let repeatCount = 0;
  let prevLine = null;

  while (pos < content.length) {
    const nextNewlineIdx = content.indexOf('\n', pos); // Find next line feed (\n) index
    const currentLine = nextNewlineIdx === -1 ? content.slice(pos) : content.slice(pos, nextNewlineIdx + 1);

    if (prevLine === null) {
      prevLine = currentLine;
    } else if (currentLine === prevLine) {
      repeatCount++;
    } else {
      if (repeatCount > 0) {
        const compressionDesc = `<previous line repeated ${repeatCount} additional times>\n`;
        if (compressionDesc.length < prevLine.length * (repeatCount + 1)) {
          result += prevLine + compressionDesc;
        } else {
          for (let i = 0; i <= repeatCount; i++) {
            result += prevLine;
          }
        }
        repeatCount = 0;
      } else {
        result += prevLine;
      }
      prevLine = currentLine;
    }

    pos = nextNewlineIdx === -1 ? content.length : nextNewlineIdx + 1;
  }

  if (repeatCount > 0 && prevLine !== null) {
    const compressionDesc = `<previous line repeated ${repeatCount} additional times>\n`;
    if (compressionDesc.length < prevLine.length * repeatCount) {
      result += prevLine + compressionDesc;
    } else {
      for (let i = 0; i <= repeatCount; i++) {
        result += prevLine;
      }
    }
  } else if (prevLine !== null) {
    result += prevLine;
  }

  return result;
}

/**
 * Processes carriage returns (\r) in terminal output to simulate how a real terminal would display content.
 * This function is optimized for performance by using in-place string operations and avoiding memory-intensive
 * operations like split/join.
 *
 * Key features:
 * 1. Processes output line-by-line to maximize chunk processing
 * 2. Uses string indexes and substring operations instead of arrays
 * 3. Single-pass traversal of the entire input
 * 4. Special handling for multi-byte characters (like emoji) to prevent corruption
 * 5. Replacement of partially overwritten multi-byte characters with spaces
 *
 * @param input The terminal output to process
 * @returns The processed terminal output with carriage returns (\r) handled
 */
export function processCarriageReturns(input: string): string {
  // Quick check: if no carriage returns (\r), return the original input
  if (input.indexOf('\r') === -1) return input;

  let output = '';
  let i = 0;
  const len = input.length;

  // Single-pass traversal of the entire input
  while (i < len) {
    // Find current line's end position (line feed (\n) or end of text)
    let lineEnd = input.indexOf('\n', i);
    if (lineEnd === -1) lineEnd = len;

    // Check if current line contains carriage returns (\r)
    let crPos = input.indexOf('\r', i);
    if (crPos === -1 || crPos >= lineEnd) {
      // No carriage returns (\r) in this line, copy entire line
      output += input.substring(i, lineEnd);
    } else {
      // Line has carriage returns (\r), handle overwrite logic
      let curLine = input.substring(i, crPos);
      curLine = processLineWithCarriageReturns(input, curLine, crPos, lineEnd);
      output += curLine;
    }

    // 'curLine' now holds the processed content of the line *without* its original terminating line feed (\n) character.
    // 'lineEnd' points to the position of that line feed (\n) in the original input, or to the end of the input string if no line feed (\n) was found.
    // This check explicitly adds the line feed (\n) character back *only if* one was originally present at this position (lineEnd < len).
    // This ensures we preserve the original structure, correctly handling inputs both with and without a final line feed (\n),
    // rather than incorrectly injecting a line feed (\n) if the original input didn't end with one.
    if (lineEnd < len) output += '\n';

    // Move to next line
    i = lineEnd + 1;
  }

  return output;
}

/**
 * Processes backspace characters (\b) in terminal output using index operations.
 * Uses indexOf to efficiently locate and handle backspaces.
 *
 * Technically terminal only moves the cursor and overwrites in-place,
 * but we assume \b is destructive as an optimization which is acceptable
 * for all progress spinner cases and most terminal output cases.
 *
 * @param input The terminal output to process
 * @returns The processed output with backspaces handled
 */
export function processBackspaces(input: string): string {
  let output = '';
  let pos = 0;
  let bsPos = input.indexOf('\b');

  while (bsPos !== -1) {
    // Fast path: exclude char before backspace
    output += input.substring(pos, bsPos - 1);

    // Move past backspace
    pos = bsPos + 1;

    // Count consecutive backspaces
    let count = 0;
    while (input[pos] === '\b') {
      count++;
      pos++;
    }

    // Trim output mathematically for consecutive backspaces
    if (count > 0 && output.length > 0) {
      output = output.substring(0, Math.max(0, output.length - count));
    }

    // Find next backspace
    bsPos = input.indexOf('\b', pos);
  }

  // Add remaining content
  if (pos < input.length) {
    output += input.substring(pos);
  }

  return output;
}

/**
 * Helper function to process a single line with carriage returns.
 * Handles the overwrite logic for a line that contains one or more carriage returns (\r).
 *
 * @param input The original input string
 * @param initialLine The line content up to the first carriage return
 * @param initialCrPos The position of the first carriage return in the line
 * @param lineEnd The position where the line ends
 * @returns The processed line with carriage returns handled
 */
function processLineWithCarriageReturns(
  input: string,
  initialLine: string,
  initialCrPos: number,
  lineEnd: number
): string {
  let curLine = initialLine;
  let crPos = initialCrPos;

  while (crPos < lineEnd) {
    // Find next carriage return (\r) or line end (line feed (\n))
    let nextCrPos = input.indexOf('\r', crPos + 1);
    if (nextCrPos === -1 || nextCrPos >= lineEnd) nextCrPos = lineEnd;

    // Extract segment after carriage return (\r)
    let segment = input.substring(crPos + 1, nextCrPos);

    // Skip empty segments
    if (segment !== '') {
      // Determine how to handle overwrite
      if (segment.length >= curLine.length) {
        // Complete overwrite
        curLine = segment;
      } else {
        // Partial overwrite - need to check for multi-byte character boundary issues
        const potentialPartialChar = curLine.charAt(segment.length);
        const segmentLastCharCode = segment.length > 0 ? segment.charCodeAt(segment.length - 1) : 0;
        const partialCharCode = potentialPartialChar.charCodeAt(0);

        // Simplified condition for multi-byte character detection
        if (
          (segmentLastCharCode >= 0xd800 && segmentLastCharCode <= 0xdbff) || // High surrogate at end of segment
          (partialCharCode >= 0xdc00 && partialCharCode <= 0xdfff) || // Low surrogate at overwrite position
          (curLine.length > segment.length + 1 && partialCharCode >= 0xd800 && partialCharCode <= 0xdbff) // High surrogate followed by another character
        ) {
          // If a partially overwritten multi-byte character is detected, replace with space
          const remainPart = curLine.substring(segment.length + 1);
          curLine = segment + ' ' + remainPart;
        } else {
          // Normal partial overwrite
          curLine = segment + curLine.substring(segment.length);
        }
      }
    }

    crPos = nextCrPos;
  }

  return curLine;
}
