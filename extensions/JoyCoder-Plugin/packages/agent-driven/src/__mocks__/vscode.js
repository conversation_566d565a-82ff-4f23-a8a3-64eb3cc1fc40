module.exports = {
  env: {
    language: 'en',
  },
  Uri: {
    file: (path) => ({ fsPath: path }),
    parse: (uri) => ({ fsPath: uri.replace('file://', '') }),
  },
  workspace: {
    getConfiguration: () => ({
      get: (key) => {
        const config = {
          'joycoder.browserViewportSize': '1280x800',
        };
        return config[key];
      },
      update: jest.fn(),
    }),
  },
  window: {
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    createOutputChannel: () => ({
      appendLine: jest.fn(),
      append: jest.fn(),
      show: jest.fn(),
      clear: jest.fn(),
      dispose: jest.fn(),
    }),
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
  },
  ExtensionContext: class {
    constructor() {
      this.subscriptions = [];
      this.extensionPath = '/mock/extension/path';
      this.globalStoragePath = '/mock/storage/path';
      this.storagePath = '/mock/storage/path';
      this.logPath = '/mock/log/path';
      this.extensionUri = { fsPath: '/mock/extension/path' };
      this.globalStorageUri = { fsPath: '/mock/settings/path' };
      this.workspaceState = {
        get: () => undefined,
        update: () => Promise.resolve(),
      };
      this.globalState = {
        get: () => undefined,
        update: () => Promise.resolve(),
        setKeysForSync: () => {},
      };
      this.asAbsolutePath = (relativePath) => `/mock/extension/path/${relativePath}`;
      this.extension = {
        packageJSON: {
          version: '1.0.0',
        },
      };
    }
  },
};
