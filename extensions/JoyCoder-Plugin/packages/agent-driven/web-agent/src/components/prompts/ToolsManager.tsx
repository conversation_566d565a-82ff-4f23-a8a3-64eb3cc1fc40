import React from 'react';
import { Checkbox, Button } from 'antd';
import { ToolsManagerProps } from './types';
import { TOOL_GROUPS, ToolGroup } from '../../utils/modes';

const availableGroups = (Object.keys(TOOL_GROUPS) as ToolGroup[]).filter(
  (group) => !TOOL_GROUPS[group].alwaysAvailable,
);

const ToolsManager: React.FC<ToolsManagerProps> = ({
  getCurrentMode,
  customModes,
  updateCustomMode,
  isToolsEditMode,
  setIsToolsEditMode,
}) => {
  const handleGroupChange = (group: ToolGroup) => (e: any) => {
    const currentMode = getCurrentMode();
    if (!currentMode || !customModes?.some((m) => m.agentId === currentMode.agentId)) return;

    const oldGroups = currentMode.groups || [];
    let newGroups = e.target.checked
      ? [...oldGroups, group]
      : oldGroups.filter((g: ToolGroup | ToolGroup[]) => (Array.isArray(g) ? g[0] !== group : g !== group));

    updateCustomMode(currentMode.agentId, {
      ...currentMode,
      groups: newGroups,
      source: currentMode.source || 'global',
    });
  };

  const currentMode = getCurrentMode();
  const isCustomMode = customModes?.some((m) => m.agentId === currentMode?.agentId);

  return (
    <div className="tools-manager mb-4">
      <div className="flex justify-between items-center mb-1">
        <div className="font-bold">可用功能</div>
        {isCustomMode && (
          <Button
            icon={<span className={`codicon codicon-${isToolsEditMode ? 'check' : 'edit'}`} />}
            onClick={() => setIsToolsEditMode(!isToolsEditMode)}
            title={isToolsEditMode ? '完成编辑' : '编辑工具'}
          />
        )}
      </div>
      {!isCustomMode && <div className="text-sm text-vscode-descriptionForeground mb-2">内置模式不支持自定义工具</div>}
      {isToolsEditMode && isCustomMode ? (
        <div className="grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-2">
          {availableGroups.map((group) => {
            const isGroupEnabled = currentMode?.groups?.some((g: ToolGroup[]) =>
              Array.isArray(g) ? g[0] === group : g === group,
            );

            return (
              <Checkbox
                key={group}
                checked={isGroupEnabled}
                onChange={handleGroupChange(group)}
                disabled={!isCustomMode}
                style={{ margin: 0 }}
              >
                {getToolName(group)}
                {group === 'edit' && (
                  <div className="text-xs text-vscode-descriptionForeground mt-0.5">
                    允许编辑文件权限：{' '}
                    {(() => {
                      const editGroup = currentMode?.groups?.find(
                        (g: any) => Array.isArray(g) && g[0] === 'edit' && g[1]?.fileRegex,
                      );
                      if (!Array.isArray(editGroup)) return '所有文件';
                      return editGroup[1].description || `/${editGroup[1].fileRegex}/`;
                    })()}
                  </div>
                )}
              </Checkbox>
            );
          })}
        </div>
      ) : (
        <div className="text-sm text-vscode-foreground mb-2 leading-relaxed">
          {(() => {
            const enabledGroups = currentMode?.groups || [];
            if (enabledGroups.length === 0) {
              return '没有可用的工具';
            }
            return enabledGroups
              .map((group: ToolGroup) => {
                const groupName = Array.isArray(group) ? group[0] : group;
                const displayName = getToolName(groupName);
                if (Array.isArray(group) && group[1]?.fileRegex) {
                  const description = group[1].description || `/${group[1].fileRegex}/`;
                  return `${displayName} (${description})`;
                }
                return displayName;
              })
              .join(', ');
          })()}
        </div>
      )}
    </div>
  );
};

const getToolName = (group: string) => {
  const toolNames: { [key: string]: string } = {
    create: '创建',
    delete: '删除',
    rename: '重命名',
    move: '移动',
    copy: '复制',
    search: '搜索',
    replace: '替换',
    read: '读取文件',
    edit: '编辑文件',
    browser: '浏览器',
    command: '运行命令',
    mcp: 'MCP服务',
  };
  return toolNames[group] || group;
};

export default ToolsManager;
