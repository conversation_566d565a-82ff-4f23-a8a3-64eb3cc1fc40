import { Mode, PromptComponent, ToolGroup } from '../../utils/modes';
import { SupportPromptType } from '../../utils/support-prompt';

export type ModeSource = 'global' | 'project';

export type PromptsViewProps = {
  onDone: () => void;
};

export interface ModeSelectorProps {
  visualMode: string;
  setVisualMode: (mode: string) => void;
  modes: readonly any[];
  customModes?: readonly any[];
  switchMode: (agentId: string) => void;
  setIsToolsEditMode: (value: boolean) => void;
  updateCustomMode: (agentId: string, any: any) => void;
  handleModeSwitch: (any: any) => void;
  openCreateModeDialog: () => void;
}

export interface ToolsManagerProps {
  visualMode: string;
  getCurrentMode: () => any | undefined;
  customModes?: readonly any[];
  updateCustomMode: (agentId: string, any: any) => void;
  setIsToolsEditMode: (value: boolean) => void;
  isToolsEditMode: boolean;
}

export interface CustomInstructionsProps {
  visualMode: string;
  customModes?: readonly any[];
  customModePrompts?: Record<string, PromptComponent>;
  updateAgentPrompt: (mode: Mode, promptData: PromptComponent) => void;
  updateCustomMode: (agentId: string, any: any) => void;
  handleAgentReset: (modeSlug: string, type: 'agentDefinition' | 'customInstructions' | 'whenToUse') => void;
  getCurrentMode: () => any | undefined;
  isToolsEditMode: boolean;
}

export interface SupportPromptsProps {
  customSupportPrompts?: Record<string, string>;
  updateSupportPrompt: (type: SupportPromptType, value: string | undefined) => void;
  handleSupportReset: (type: SupportPromptType) => void;
  listApiConfigMeta?: any[];
}

export interface CreateModeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateMode: (newMode: any) => void;
  availableGroups: ToolGroup[];
  isToolsEditMode?: boolean;
  isRemoteEnvironment?: boolean;
}

export interface SystemPromptDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedPromptTitle: string;
  selectedPromptContent: string;
}
