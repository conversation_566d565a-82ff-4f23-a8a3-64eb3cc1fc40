import { VSCodeButton, VSCodeTextField, VSCodeRadioGroup, VSCodeRadio } from '@vscode/webview-ui-toolkit/react';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { vscode } from '../../utils/vscode';
import { Virtuoso } from 'react-virtuoso';
import { memo, useMemo, useState, useEffect, useCallback } from 'react';
import Fuse, { FuseResult } from 'fuse.js';
import { formatLargeNumber } from '../../utils/format';
import { formatSize } from '../../utils/size';
import DangerButton from '../common/DangerButton';
import { useEvent } from 'react-use';
import { ExtensionMessage } from '../../../../src/shared/ExtensionMessage';

type HistoryViewProps = {
  onDone: () => void;
};

type SortOption = 'newest' | 'oldest' | 'mostExpensive' | 'mostTokens' | 'mostRelevant';

const HistoryView = ({ onDone }: HistoryViewProps) => {
  const { taskHistory, totalTasksSize } = useExtensionState();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState<SortOption>('newest');
  const [lastNonRelevantSort, setLastNonRelevantSort] = useState<SortOption | null>('newest');
  const [deleteAllDisabled, setDeleteAllDisabled] = useState(false);

  const handleMessage = useCallback((event: MessageEvent<ExtensionMessage>) => {
    if (event.data.type === 'relinquishControl') {
      setDeleteAllDisabled(false);
    }
  }, []);
  useEvent('message', handleMessage);

  useEffect(() => {
    if (searchQuery && sortOption !== 'mostRelevant' && !lastNonRelevantSort) {
      setLastNonRelevantSort(sortOption);
      setSortOption('mostRelevant');
    } else if (!searchQuery && sortOption === 'mostRelevant' && lastNonRelevantSort) {
      setSortOption(lastNonRelevantSort);
      setLastNonRelevantSort(null);
    }
  }, [searchQuery, sortOption, lastNonRelevantSort]);

  const handleHistorySelect = (id: string) => {
    vscode.postMessage({ type: 'showTaskWithId', text: id });
  };

  const handleDeleteHistoryItem = (id: string) => {
    vscode.postMessage({ type: 'deleteTaskWithId', text: id });
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: false,
    });
  };

  const presentableTasks = useMemo(() => {
    return taskHistory.filter((item) => item.ts && item.task);
  }, [taskHistory]);

  const fuse = useMemo(() => {
    return new Fuse(presentableTasks, {
      keys: ['task'],
      threshold: 0.6,
      shouldSort: true,
      isCaseSensitive: false,
      ignoreLocation: false,
      includeMatches: true,
      minMatchCharLength: 1,
    });
  }, [presentableTasks]);

  const taskHistorySearchResults = useMemo(() => {
    let results = searchQuery ? highlight(fuse.search(searchQuery)) : presentableTasks;

    results.sort((a, b) => {
      switch (sortOption) {
        case 'oldest':
          return a.ts - b.ts;
        case 'mostExpensive':
          return (b.totalCost || 0) - (a.totalCost || 0);
        case 'mostTokens':
          return (
            (b.tokensIn || 0) +
            (b.tokensOut || 0) +
            (b.cacheWrites || 0) +
            (b.cacheReads || 0) -
            ((a.tokensIn || 0) + (a.tokensOut || 0) + (a.cacheWrites || 0) + (a.cacheReads || 0))
          );
        case 'mostRelevant':
          // NOTE: you must never sort directly on object since it will cause members to be reordered
          return searchQuery ? 0 : b.ts - a.ts; // Keep fuse order if searching, otherwise sort by newest
        case 'newest':
        default:
          return b.ts - a.ts;
      }
    });

    return results;
  }, [presentableTasks, searchQuery, fuse, sortOption]);

  return (
    <>
      <style>
        {`
					.history-item:hover {
						background-color: var(--vscode-list-hoverBackground);
					}
					.delete-button, .export-button {
						opacity: 0;
						pointer-events: none;
					}
					.history-item:hover .delete-button,
					.history-item:hover .export-button {
						opacity: 1;
						pointer-events: auto;
					}
					.history-item-highlight {
						background-color: var(--vscode-editor-findMatchHighlightBackground);
						color: inherit;
					}
				`}
      </style>
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '10px 17px 10px 20px',
          }}
        >
          <h3
            style={{
              color: 'var(--vscode-foreground)',
              margin: 0,
            }}
          >
            历史消息
          </h3>
          <VSCodeButton
            style={{
              background: 'var(--vscode-button-secondaryBackground, #72747C)',
              color: 'var(--vscode-button-secondaryForeground, #72747C)',
              borderRadius: '4px',
              height: '28px',
            }}
            onClick={onDone}
          >
            完成
          </VSCodeButton>
        </div>
        <div style={{ padding: '5px 17px 6px 17px' }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '6px',
            }}
          >
            <VSCodeTextField
              style={{ width: '100%' }}
              placeholder="搜索"
              value={searchQuery}
              onInput={(e) => {
                const newValue = (e.target as HTMLInputElement)?.value;
                setSearchQuery(newValue);
                if (newValue && !searchQuery && sortOption !== 'mostRelevant') {
                  setLastNonRelevantSort(sortOption);
                  setSortOption('mostRelevant');
                }
              }}
            >
              <div
                slot="start"
                className="codicon codicon-search"
                style={{
                  fontSize: 13,
                  marginTop: 2.5,
                  opacity: 0.8,
                }}
              ></div>
              {searchQuery && (
                <div
                  className="input-icon-button codicon codicon-close"
                  aria-label="Clear search"
                  onClick={() => setSearchQuery('')}
                  slot="end"
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                  }}
                />
              )}
            </VSCodeTextField>
            <VSCodeRadioGroup
              style={{ display: 'flex', flexWrap: 'wrap' }}
              value={sortOption}
              onChange={(e) => setSortOption((e.target as HTMLInputElement).value as SortOption)}
            >
              <VSCodeRadio value="newest">时间最新</VSCodeRadio>
              <VSCodeRadio value="oldest">时间正序</VSCodeRadio>
              {/* <VSCodeRadio value="mostExpensive">Most Expensive</VSCodeRadio> */}
              <VSCodeRadio value="mostTokens">Tokens数最多</VSCodeRadio>
              <VSCodeRadio value="mostRelevant" disabled={!searchQuery} style={{ opacity: searchQuery ? 1 : 0.5 }}>
                最相关
              </VSCodeRadio>
            </VSCodeRadioGroup>
          </div>
        </div>
        <div style={{ flexGrow: 1, overflowY: 'auto', margin: 0 }}>
          {/* {presentableTasks.length === 0 && (
						<div
							style={{

								alignItems: "center",
								fontStyle: "italic",
								color: "var(--vscode-descriptionForeground)",
								textAlign: "center",
								padding: "0px 10px",
							}}>
							<span
								className="codicon codicon-robot"
								style={{ fontSize: "60px", marginBottom: "10px" }}></span>
							<div>Start a task to see it here</div>
						</div>
					)} */}
          <Virtuoso
            style={{
              flexGrow: 1,
              overflowY: 'scroll',
            }}
            data={taskHistorySearchResults}
            itemContent={(index, item) => (
              <div
                key={item.id}
                className="history-item"
                style={{
                  cursor: 'pointer',
                  borderBottom: index < taskHistory.length - 1 ? '1px solid var(--vscode-panel-border)' : 'none',
                }}
                onClick={() => handleHistorySelect(item.id)}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '8px',
                    padding: '12px 20px',
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span
                      style={{
                        color: 'var(--vscode-descriptionForeground)',
                        fontWeight: 500,
                        fontSize: '0.85em',
                        textTransform: 'uppercase',
                      }}
                    >
                      {formatDate(item.ts)}
                    </span>
                    <VSCodeButton
                      appearance="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteHistoryItem(item.id);
                      }}
                      className="delete-button"
                      style={{ padding: '0px 0px' }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '3px',
                          fontSize: '11px',
                          // fontWeight: "bold",
                        }}
                      >
                        <span className="codicon codicon-trash"></span>
                        {formatSize(item.size)}
                      </div>
                    </VSCodeButton>
                  </div>
                  <div
                    style={{
                      fontSize: 'var(--vscode-font-size)',
                      color: 'var(--vscode-foreground)',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word',
                      overflowWrap: 'anywhere',
                    }}
                    dangerouslySetInnerHTML={{
                      __html: item.task,
                    }}
                  />
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '4px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                          flexWrap: 'wrap',
                        }}
                      >
                        {/* {<span
                          style={{
                            fontWeight: 500,
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          Tokens:
                        </span>
                        <span
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '3px',
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          <i
                            className="codicon codicon-arrow-up"
                            style={{
                              fontSize: '12px',
                              fontWeight: 'bold',
                              marginBottom: '-2px',
                            }}
                          />
                          {formatLargeNumber(item.tokensIn || 0)}
                        </span>
                        <span
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '3px',
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          <i
                            className="codicon codicon-arrow-down"
                            style={{
                              fontSize: '12px',
                              fontWeight: 'bold',
                              marginBottom: '-2px',
                            }}
                          />
                          {formatLargeNumber(item.tokensOut || 0)}
                        </span>} */}
                      </div>
                      {/* 历史导出按钮 */}
                      {/* {!item.totalCost && <ExportButton itemId={item.id} />} */}
                    </div>

                    {!!item.cacheWrites && (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                          flexWrap: 'wrap',
                        }}
                      >
                        <span
                          style={{
                            fontWeight: 500,
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          缓存:
                        </span>
                        <span
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '3px',
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          <i
                            className="codicon codicon-database"
                            style={{
                              fontSize: '12px',
                              fontWeight: 'bold',
                              marginBottom: '-1px',
                            }}
                          />
                          +{formatLargeNumber(item.cacheWrites || 0)}
                        </span>
                        <span
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '3px',
                            color: 'var(--vscode-descriptionForeground)',
                          }}
                        >
                          <i
                            className="codicon codicon-arrow-right"
                            style={{
                              fontSize: '12px',
                              fontWeight: 'bold',
                              marginBottom: 0,
                            }}
                          />
                          {formatLargeNumber(item.cacheReads || 0)}
                        </span>
                      </div>
                    )}
                    {/* {!!item.totalCost && (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginTop: -2,
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                          }}
                        >
                          <span
                            style={{
                              fontWeight: 500,
                              color: 'var(--vscode-descriptionForeground)',
                            }}
                          >
                            API Cost:
                          </span>
                          <span
                            style={{
                              color: 'var(--vscode-descriptionForeground)',
                            }}
                          >
                            ${item.totalCost?.toFixed(4)}
                          </span>
                        </div>
                        <ExportButton itemId={item.id} />
                      </div>
                    )} */}
                  </div>
                </div>
              </div>
            )}
          />
        </div>
        <div
          style={{
            padding: '10px 10px',
            borderTop: '1px solid var(--vscode-panel-border)',
          }}
        >
          <VSCodeButton
            style={{
              width: '100%',
              height: '28px',
              background: 'var(--vscode-button-secondaryBackground, #72747C)',
              color: 'var(--vscode-button-secondaryForeground, #72747C)',
              borderRadius: '4px',
              fontSize: '12px',
              display: 'none',
            }}
            disabled={deleteAllDisabled || taskHistory.length === 0}
            onClick={() => {
              setDeleteAllDisabled(true);
              vscode.postMessage({ type: 'clearAllTaskHistory' });
            }}
          >
            清除所有历史记录{totalTasksSize !== null ? ` (${formatSize(totalTasksSize)})` : ''}
          </VSCodeButton>
        </div>
      </div>
    </>
  );
};

const ExportButton = ({ itemId }: { itemId: string }) => (
  <VSCodeButton
    className="export-button"
    appearance="icon"
    onClick={(e) => {
      e.stopPropagation();
      vscode.postMessage({ type: 'exportTaskWithId', text: itemId });
    }}
  >
    <div style={{ fontSize: '11px', fontWeight: 500, opacity: 1 }}>导出</div>
  </VSCodeButton>
);

// https://gist.github.com/evenfrost/1ba123656ded32fb7a0cd4651efd4db0
export const highlight = (
  fuseSearchResult: FuseResult<any>[],
  highlightClassName: string = 'history-item-highlight',
) => {
  const set = (obj: Record<string, any>, path: string, value: any) => {
    const pathValue = path.split('.');
    let i: number;

    for (i = 0; i < pathValue.length - 1; i++) {
      obj = obj[pathValue[i]] as Record<string, any>;
    }

    obj[pathValue[i]] = value;
  };

  // Function to merge overlapping regions
  const mergeRegions = (regions: [number, number][]): [number, number][] => {
    if (regions.length === 0) return regions;

    // Sort regions by start index
    regions.sort((a, b) => a[0] - b[0]);

    const merged: [number, number][] = [regions[0]];

    for (let i = 1; i < regions.length; i++) {
      const last = merged[merged.length - 1];
      const current = regions[i];

      if (current[0] <= last[1] + 1) {
        // Overlapping or adjacent regions
        last[1] = Math.max(last[1], current[1]);
      } else {
        merged.push(current);
      }
    }

    return merged;
  };

  const generateHighlightedText = (inputText: string, regions: [number, number][] = []) => {
    if (regions.length === 0) {
      return inputText;
    }

    // Sort and merge overlapping regions
    const mergedRegions = mergeRegions(regions);

    let content = '';
    let nextUnhighlightedRegionStartingIndex = 0;

    mergedRegions.forEach((region) => {
      const start = region[0];
      const end = region[1];
      const lastRegionNextIndex = end + 1;

      content += [
        inputText.substring(nextUnhighlightedRegionStartingIndex, start),
        `<span class="${highlightClassName}">`,
        inputText.substring(start, lastRegionNextIndex),
        '</span>',
      ].join('');

      nextUnhighlightedRegionStartingIndex = lastRegionNextIndex;
    });

    content += inputText.substring(nextUnhighlightedRegionStartingIndex);

    return content;
  };

  return fuseSearchResult
    .filter(({ matches }) => matches && matches.length)
    .map(({ item, matches }) => {
      const highlightedItem = { ...item };

      matches?.forEach((match) => {
        if (match.key && typeof match.value === 'string' && match.indices) {
          // Merge overlapping regions before generating highlighted text
          const mergedIndices = mergeRegions([...match.indices]);
          set(highlightedItem, match.key, generateHighlightedText(match.value, mergedIndices));
        }
      });

      return highlightedItem;
    });
};

export default memo(HistoryView);
