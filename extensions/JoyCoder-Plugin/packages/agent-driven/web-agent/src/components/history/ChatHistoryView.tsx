import { memo, useMemo } from 'react';
import { useExtensionState } from '../../context/ExtensionStateContext';
import { vscode } from '../../utils/vscode';
import { locale } from '../../adaptor/locales';

type ChatHistoryViewProps = {
  onHistorySelect?: (id: string) => void;
};

const ChatHistoryView = ({ onHistorySelect }: ChatHistoryViewProps) => {
  const { taskHistory } = useExtensionState();

  const handleHistorySelect = (id: string) => {
    if (onHistorySelect) {
      onHistorySelect(id);
    } else {
      vscode.postMessage({ type: 'showTaskWithId', text: id });
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    vscode.postMessage({ type: 'newTask', text: suggestion, images: [], agentId: 'code' });
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date?.toLocaleString('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const presentableTasks = useMemo(() => {
    return taskHistory
      .filter((item) => item.ts && item.task)
      .slice(0, 3); // 只显示前3条
  }, [taskHistory]);

  const defaultSuggestions = locale.welcome.examples;

  // 如果没有历史记录，显示默认建议
  if (presentableTasks.length === 0) {
    return (
      <div style={{
        padding: '30px 20px',
      }}>
        <div style={{
          height: '17px',
          lineHeight: '17px',
          fontSize: '12px',
          fontFamily: 'PingFang SC',
          fontWeight: 'normal',
          color: 'var(--vscode-tab-unfocusedInactiveForeground)'
        }}>
          可以试一试
        </div>
        {Object.values(defaultSuggestions).map((suggestion: string, index: number) => (
          <div
            key={index}
            onClick={() => handleSuggestionClick(suggestion)}
            style={{
              display: 'flex',
              alignItems: 'center',
              marginTop: '8px',
              height: '32px',
              lineHeight: '32px',
              cursor: 'pointer',
              background: 'var(--vscode-editor-background)',
              borderRadius: '6px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--vscode-list-hoverBackground)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--vscode-editor-background)';
            }}
          >
            <img src='https://aichat.s3.cn-north-1.jdcloud-oss.com/JoyCoderIDE/dist-dev/star.svg'
              alt='star'
              style={{
                fontSize: '14px',
                margin: '0 9px 0 13px',
              }}
            />
            <div style={{
              fontSize: '12px',
              color: 'var(--vscode-editor-foreground)',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              width: 'calc(100% - 74px)',
              marginRight: '10px'
            }}>
              {suggestion}
            </div>
            <i
              className="icon iconfont icon-xinjianjiantou"
              style={{
                fontSize: '14px',
                color: 'var(--vscode-tab-unfocusedInactiveForeground)',
                marginRight: '14px',
              }}
            />
          </div>
        ))}
      </div>
    );
  }

  // 有历史记录时显示历史记录
  return (
    <div style={{
      padding: '30px 20px',
    }}>
      <div style={{
        height: '17px',
        lineHeight: '17px',
        fontSize: '12px',
        fontFamily: 'PingFang SC',
        fontWeight: 'normal',
        color: 'var(--vscode-dropdown-foreground)'
      }}>
        历史记录
      </div>
      {presentableTasks.map((item, index) => (
        <div
          key={item.id}
          onClick={() => handleHistorySelect(item.id)}
          style={{
            display: 'flex',
            alignItems: 'center',
            marginTop: '8px',
            height: '32px',
            lineHeight: '32px',
            cursor: 'pointer',
            background: 'var(--vscode-editor-background)',
            borderRadius: '6px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--vscode-list-hoverBackground)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'var(--vscode-editor-background)';
          }}
        >
          <i
            className="icon iconfont icon-lishijilu"
            style={{
              fontSize: '14px',
              color: 'var(--vscode-editor-foreground)',
              margin: '0 9px 0 13px',
            }}
          />
          <div style={{
            fontSize: '12px',
            color: 'var(--vscode-editor-foreground)', // 使用-dropBackground)',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            width: 'calc(100% - 154px)',
            marginRight: '10px'
          }}>
            {item.task}
          </div>
          <div style={{
            fontSize: '12px',
            color: 'var(--vscode-tab-unfocusedInactiveForeground)',
            fontFamily: 'PingFang SC',
            fontWeight: 'normal',
            width: '110px'
          }}>
            {formatDate(item.ts)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(ChatHistoryView);
