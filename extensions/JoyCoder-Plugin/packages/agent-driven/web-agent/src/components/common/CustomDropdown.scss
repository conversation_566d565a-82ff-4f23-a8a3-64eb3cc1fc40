.custom-dropdown-trigger {
  cursor: pointer;
  
  &.disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.custom-dropdown-menu {
  box-sizing: border-box;
  font-size: 14px;
  width: 160px;
  border: 1px solid gba(48,48,53,1);
  border-radius: 6px;
  max-height: 356px;
  overflow: auto;
  padding: 0 0 12px 4px;

  scrollbar-width: thin;
  scrollbar-color: var(--vscode-scrollbarSlider-background) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--vscode-scrollbarSlider-background);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--vscode-scrollbarSlider-hoverBackground);
  }

  &.custom-dropdown-placement-top {
    transform: translateY(-100%);
    margin-top: -8px;
    overflow: auto;
    background: rgba(34,35,37,1);
    box-shadow:  0 4px 24px 0 rgba(0,0,0,1), 0 2px 4px 0 rgba(0,0,0,1);
  }

  &.custom-dropdown-placement-bottom {
    margin-top: 8px;
  }

  &.custom-dropdown-placement-topLeft {
    transform: translateY(-100%);
    margin-top: -8px;
  }

  &.custom-dropdown-placement-topRight {
    transform: translateY(-100%) translateX(-100%);
    margin-top: -8px;
  }

  &.custom-dropdown-placement-bottomLeft {
    margin-top: 8px;
  }

  &.custom-dropdown-placement-bottomRight {
    transform: translateX(-100%);
    margin-top: 8px;
  }
}

.custom-dropdown-menu-item {
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--vscode-editor-foreground);
  border-radius: 4px;
  
  &:hover {
    background-color: var(--vscode-list-hoverBackground, rgba(255, 255, 255, 0.1));
  }

  &:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  &:last-child {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
}
.custom-dropdown-menu-item-group {
  padding-top: 12px;
}
.custom-dropdown-menu-item-group:first-child {
  border-bottom: 1px solid rgba(48,48,53,1);
}

.custom-dropdown-menu-item-group-title {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: rgba(255,255,255,0.3);
  line-height: 18px;
  padding: 0 0 4px 8px;
}

.custom-dropdown-menu-item-group-list {
  padding-bottom: 4px;
}

// 模型选择下拉框特定样式
.joycoder-chatgpt-model-dropdown-container {
  .custom-dropdown-menu {
    width: 250px !important;
  }
}

// VSCode 浅色主题适配
.vscode-light {
  .custom-dropdown-menu {
    color: rgba(0, 0, 0, 0.85);
    background-color: #ffffff;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
  }

  .custom-dropdown-menu-item {
    color: #616161;
    
    &:hover {
      background-color: #0000000a;
    }
  }

  .custom-dropdown-menu-item-group-title {
    color: rgba(0, 0, 0, 0.3);
  }
}