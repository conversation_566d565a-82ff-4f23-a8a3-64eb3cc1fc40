// UI组件导出
export { default as Avatar } from './Avatar';
export type { AvatarProps } from './Avatar';

export { default as ModeInfo } from './ModeInfo';
export type { ModeInfoProps } from './ModeInfo';

export { default as CopyButton } from './CopyButton';
export type { CopyButtonProps } from './CopyButton';

export { default as FileIcon } from './FileIcon';
export type { FileIconProps } from './FileIcon';

// 其他现有组件
export { default as CodeBlock } from './CodeBlock';
export { default as MarkdownBlock } from './MarkdownBlock';
export { default as Thumbnails } from './Thumbnails';
export { default as SuccessButton } from './SuccessButton';
export { default as DangerButton } from './DangerButton';
export { default as SettingsButton } from './SettingsButton';
export { default as Tooltip } from './Tooltip';
export { default as VSCodeButtonLink } from './VSCodeButtonLink';
export { CheckmarkControl } from './CheckmarkControl';
export { CheckpointControls, CheckpointOverlay } from './CheckpointControls';

// Lottie动画组件
export { default as LottieAnimation } from './LottieAnimation';
export type { LottieAnimationProps } from './LottieAnimation';

// 思考动画组件
export { default as ThinkingAnimation } from './ThinkingAnimation';
export type { ThinkingAnimationProps } from './ThinkingAnimation';

// 公共动画资源导出
export { default as waitingAnimation } from '../../assets/animations/waiting.json';
