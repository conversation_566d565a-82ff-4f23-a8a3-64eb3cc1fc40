import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"

const StyledButton = styled(VSCodeButton)`
	--success-button-bg: rgb(48, 48, 53);
	--success-button-hover: rgb(48, 48, 53);
	--success-button-active: rgb(48, 48, 53);

	background-color: var(--success-button-bg) !important;
	border-color: var(--success-button-bg) !important;
	color: var(--vscode-editor-foreground) !important;

	&:hover {
		background-color: var(--success-button-hover) !important;
		border-color: var(--success-button-hover) !important;
	}

	&:active {
		background-color: var(--success-button-active) !important;
		border-color: var(--success-button-active) !important;
	}
`

interface SuccessButtonProps extends React.ComponentProps<typeof VSCodeButton> {}

const SuccessButton: React.FC<SuccessButtonProps> = (props) => {
	return <StyledButton {...props} />
}

export default SuccessButton
