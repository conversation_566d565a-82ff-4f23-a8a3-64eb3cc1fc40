.joycode-loading{
  position: relative;
  width: 10px;
  height: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  .center-dot {
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 10;
  }

  .blue-glow {
    position: absolute;
    width: 8px;
    height: 8px;
    background: linear-gradient(90deg, rgba(0,192,252,1) 0%,rgba(35,84,213,1) 100%);
    filter: blur(2px);
    border-radius: 50%;
    animation: orbit 1s linear infinite;
  }

  @keyframes orbit {
    from {
      transform: rotate(0deg) translateX(2px) rotate(0deg);
    }
    to {
      transform: rotate(360deg) translateX(2px) rotate(-360deg);
    }
  }
}
