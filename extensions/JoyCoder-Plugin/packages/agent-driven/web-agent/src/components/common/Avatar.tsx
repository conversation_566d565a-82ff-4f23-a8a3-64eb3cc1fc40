import React from 'react';
import styled from 'styled-components';
import defaultAvatar from '../../assets/images/logo_bg_blue.svg';

export interface AvatarProps {
  /** 图片alt文本 */
  alt: string;
  /** 头像大小，默认为20px */
  size?: number;
  /** 自定义className */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 智能体ID，用于显示对应的图标 */
  agentId?: string;
}

const AvatarIcon = styled.i<{ $size: number }>`
  font-size: 20px;
  color: var(--vscode-foreground, #72747C);
  display: flex;
  align-items: center;
  justify-content: center;
`;

// ChatModeIcon 映射，将 agentId 映射到图标类名
const ChatModeIcon: Record<string, string> = {
  architect: 'guihua',
  chat: 'wenda',
  code: 'bianma',
  orchestrator: 'tiaoduzhe',
  'design-engineer': 'ui',
  user: 'yonghu',
  debug: 'ceshi',
  // 可以根据需要添加更多映射
};

/**
 * Avatar组件 - 显示用户或AI的头像
 * 根据agentId匹配ChatModeIcon显示对应图标，匹配不上使用默认图标
 */
export const Avatar: React.FC<AvatarProps> = ({
  alt,
  size = 20,
  className,
  style,
  agentId
}) => {
  // 获取对应的图标类名，所有情况都使用图标字体显示
  // 如果agentId存在且在ChatModeIcon中有映射，使用对应图标
  // 否则统一使用默认图标 'zidingyizhinengtitouxiang'
  const iconClassName = agentId && ChatModeIcon[agentId]
    ? ChatModeIcon[agentId]
    : 'zidingyizhinengtitouxiang';

  return (
    (agentId && ChatModeIcon[agentId]) ? <AvatarIcon
      $size={size}
      className={`icon iconfont icon-${iconClassName}`}
    /> : 
    <img style={{ width: '20px'}} src={defaultAvatar} alt='zidingyizhinengtitouxiang'/>
  );
};

export default Avatar;
