import { memo, useMemo } from 'react';
import { getLanguageFromPath } from '../../utils/getLanguageFromPath';
import CodeBlock, { CODE_BLOCK_BG_COLOR } from './CodeBlock';

interface CodeAccordianProps {
  code?: string;
  diff?: string;
  language?: string | undefined;
  path?: string;
  isFeedback?: boolean;
  isConsoleLogs?: boolean;
  isExpanded: boolean;
  onToggleExpand: () => void;
  isLoading?: boolean;
  icon?: React.ReactNode;
}

/*
We need to remove leading non-alphanumeric characters from the path in order for our leading ellipses trick to work.
^: Anchors the match to the start of the string.
[^a-zA-Z0-9]+: Matches one or more characters that are not alphanumeric.
The replace method removes these matched characters, effectively trimming the string up to the first alphanumeric character.
*/
export const cleanPathPrefix = (path: string): string => path.replace(/^[^\u4e00-\u9fa5a-zA-Z0-9]+/, '');

const CodeAccordian = ({
  icon,
  code,
  diff,
  language,
  path,
  isFeedback,
  isConsoleLogs,
  isExpanded,
  onToggleExpand,
  isLoading,
}: CodeAccordianProps) => {
  const inferredLanguage = useMemo(
    () => code && (language ?? (path ? getLanguageFromPath(path) : undefined)),
    [path, language, code],
  );

  return (
    <div
      style={{
        borderRadius: 3,
        backgroundColor: CODE_BLOCK_BG_COLOR,
        overflow: 'hidden', // This ensures the inner scrollable area doesn't overflow the rounded corners
        border: '1px solid var(--vscode-editorGroup-border)',
        margin: '10px 0',
      }}
    >
      {(path || isFeedback || isConsoleLogs) && (
        <div
          style={{
            // color: 'var(--vscode-descriptionForeground)',
            display: 'flex',
            alignItems: 'center',
            padding: '9px 10px',
            cursor: isLoading ? 'wait' : 'pointer',
            opacity: isLoading ? 0.7 : 1,
            // pointerEvents: isLoading ? "none" : "auto",
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
          }}
          onClick={isLoading ? undefined : onToggleExpand}
        >
          {isFeedback || isConsoleLogs ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                className={`codicon codicon-${isFeedback ? 'feedback' : 'output'}`}
                style={{ marginRight: '6px' }}
              ></span>
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  marginRight: '8px',
                }}
              >
                {isFeedback ? 'User Edits' : 'Console Logs'}
              </span>
            </div>
          ) : (
            <>
              {icon}
              {path?.startsWith('.') && <span>.</span>}
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  marginRight: '8px',
                  // trick to get ellipsis at beginning of string
                  direction: 'rtl',
                  textAlign: 'left',
                }}
              >
                {cleanPathPrefix(path ?? '') + '\u200E'}
              </span>
            </>
          )}
          <div style={{ flexGrow: 1 }}></div>
          {/* <span className={`codicon codicon-chevron-${isExpanded ? "up" : "down"}`}></span> */}
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <div style={{ width: '26px' }}>审查</div>
            <span
              className={`codicon codicon-chevron-right`}
              style={{
                fontSize: 13.5,
                margin: '1px 0',
              }}
            ></span>
          </span>
        </div>
      )}
      {(!(path || isFeedback || isConsoleLogs) || isExpanded) && (
        <div
          //className="code-block-scrollable" this doesn't seem to be necessary anymore, on silicon macs it shows the native mac scrollbar instead of the vscode styled one
          style={{
            overflowX: 'auto',
            overflowY: 'hidden',
            maxWidth: '100%',
          }}
        >
          <CodeBlock
            source={`${'```'}${diff !== undefined ? 'diff' : inferredLanguage}\n${(
              code ??
              diff ??
              ''
            ).trim()}\n${'```'}`}
          />
        </div>
      )}
    </div>
  );
};

// memo does shallow comparison of props, so if you need it to re-render when a nested object changes, you need to pass a custom comparison function
export default memo(CodeAccordian);
