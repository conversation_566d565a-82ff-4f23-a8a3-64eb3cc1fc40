import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { useChatCopy } from '../../utils/useCopy';

export interface CopyButtonProps {
  /** 要复制的文本内容 */
  text: string;
  /** 按钮标题提示文本 */
  title?: string;
  /** 复制成功后的提示文本 */
  successTitle?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 复制成功后的回调 */
  onCopySuccess?: () => void;
  /** 复制失败后的回调 */
  onCopyError?: (error: Error) => void;
}

/**
 * 复制按钮公共组件
 * 提供统一的复制功能和视觉反馈
 */
export const CopyButton = memo(({
  text,
  title = '复制',
  successTitle = '已复制',
  style,
  className,
  onCopySuccess,
  onCopyError
}: CopyButtonProps) => {
  const [copyStatus, setCopyStatus] = useState(false);
  const { copyToClipboard } = useChatCopy();
  const timer = useRef<NodeJS.Timeout | null>(null);

  const handleCopy = useCallback(async () => {
    try {
      await copyToClipboard(text);
      setCopyStatus(true);
      onCopySuccess?.();
      
      if (timer.current) {
        clearTimeout(timer.current);
      }
      timer.current = setTimeout(() => {
        setCopyStatus(false);
        timer.current = null;
      }, 3000);
    } catch (error) {
      onCopyError?.(error as Error);
    }
  }, [text, copyToClipboard, onCopySuccess, onCopyError]);

  useEffect(() => {
    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, []);

  const defaultStyle: React.CSSProperties = {
    background: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: '4px',
    borderRadius: '3px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'var(--vscode-descriptionForeground)',
    transition: 'all 0.2s ease',
    minWidth: 'auto',
    minHeight: 'auto',
    ...style
  };

  return (
    <button
      onClick={handleCopy}
      style={defaultStyle}
      className={className}
      title={copyStatus ? successTitle : title}
      onMouseEnter={(e) => {
        if (!copyStatus) {
          e.currentTarget.style.background = 'var(--vscode-toolbar-hoverBackground)';
        }
      }}
      onMouseLeave={(e) => {
        if (!copyStatus) {
          e.currentTarget.style.background = style?.background || 'transparent';
        }
      }}
    >
      <span
        className={`icon iconfont ${copyStatus ? 'icon-duigou' : 'icon-fuzhi'}`}
        style={{
          fontSize: '14px',
          color: copyStatus ? 'var(--vscode-charts-green)' : 'inherit',
        }}
      />
    </button>
  );
});

CopyButton.displayName = 'CopyButton';

export default CopyButton;
