import React, { useState, useRef, useLayoutEffect, memo } from 'react';
import { useWindowSize } from 'react-use';
import { vscode } from '../../utils/vscode';

interface ThumbnailsProps {
  images: string[];
  style?: React.CSSProperties;
  setImages?: React.Dispatch<React.SetStateAction<string[]>>;
  onHeightChange?: (height: number) => void;
}

const Thumbnails = ({ images, style, setImages, onHeightChange }: ThumbnailsProps) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { width } = useWindowSize();

  useLayoutEffect(() => {
    if (containerRef.current) {
      let height = containerRef.current.clientHeight;
      // some browsers return 0 for clientHeight
      if (!height) {
        height = containerRef.current.getBoundingClientRect().height;
      }
      onHeightChange?.(height);
    }
    setHoveredIndex(null);
  }, [images, width, onHeightChange]);

  const handleDelete = (index: number) => {
    setImages?.((prevImages) => prevImages.filter((_, i) => i !== index));
  };

  const isDeletable = setImages !== undefined;

  const handleImageClick = (image: string) => {
    vscode.postMessage({ type: 'openImage', text: image });
  };

  return (
    <div
      ref={containerRef}
      style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: 5,
        rowGap: 3,
        ...style,
        justifyContent: 'flex-start',
        alignItems: 'fcenter',
        paddingRight: '6px',
        margin: '5px 8px 0 5px',
      }}
    >
      {images.map((image, index) => (
        <div
          key={index}
          style={{
            position: 'relative',
            background: 'var(--vscode-button-secondaryBackground)',
            borderRadius: '2px',
            display: 'flex',
            alignItems: 'center',
            padding: '2px 4px',
            gap: '2px',
            color: 'var(--vscode-button-secondaryForeground)',
          }}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          <img
            src={image}
            alt={`Thumbnail ${index + 1}`}
            style={{
              width: 12,
              height: 12,
              objectFit: 'cover',
              borderRadius: 4,
              cursor: 'pointer',
            }}
            onClick={() => handleImageClick(image)}
          />
          <span>Image</span>
          {isDeletable && hoveredIndex === index && (
            <div
              onClick={() => handleDelete(index)}
              style={{
                position: 'absolute',
                top: -4,
                right: -4,
                width: 10,
                height: 10,
                borderRadius: '50%',
                backgroundColor: 'var(--vscode-badge-background)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer',
              }}
            >
              <span
                className="icon iconfont icon-guanbi"
                style={{
                  color: 'var(--vscode-foreground)',
                  fontSize: 10,
                  fontWeight: 'bold',
                }}
              ></span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default memo(Thumbnails);
