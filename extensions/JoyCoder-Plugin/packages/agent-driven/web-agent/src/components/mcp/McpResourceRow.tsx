import { McpResource, McpResourceTemplate } from '@joycoder/shared/src/mcp/mcp';

type McpResourceRowProps = {
  item: McpResource | McpResourceTemplate;
};

const McpResourceRow = ({ item }: McpResourceRowProps) => {
  const hasUri = 'uri' in item;
  const uri = hasUri ? item.uri : item.uriTemplate;

  return (
    <div
      key={uri}
      style={{
        padding: '3px 0',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '4px',
        }}
      >
        <span className={`codicon codicon-symbol-file`} style={{ marginRight: '6px' }} />
        <span style={{ fontWeight: 500, wordBreak: 'break-all' }}>{uri}</span>
      </div>
      <div
        style={{
          fontSize: '12px',
          opacity: 0.8,
          margin: '4px 0',
        }}
      >
        {item.name && item.description
          ? `${item.name}: ${item.description}`
          : !item.name && item.description
            ? item.description
            : !item.description && item.name
              ? item.name
              : '没有描述信息'}
      </div>
      <div
        style={{
          fontSize: '12px',
        }}
      >
        <span style={{ opacity: 0.8 }}>返回值 </span>
        <code
          style={{
            color: 'var(--vscode-textPreformat-foreground)',
            background: 'var(--vscode-textPreformat-background)',
            padding: '1px 4px',
            borderRadius: '3px',
          }}
        >
          {item.mimeType || '未知类型'}
        </code>
      </div>
    </div>
  );
};

export default McpResourceRow;
