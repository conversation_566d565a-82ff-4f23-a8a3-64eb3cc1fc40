import styled from 'styled-components';
import { CODE_BLOCK_BG_COLOR } from '../common/CodeBlock';
import { vscode } from '../../utils/vscode';

const OptionButton = styled.button<{ isSelected?: boolean; isNotSelectable?: boolean }>`
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  border: none;
  display: flex;
  align-items: left;
  background: ${(props) => (props.isSelected ? 'var(--vscode-button-secondaryBackground, #72747C)' : 'var(--vscode-editor-background, #72747C)')};
  color: ${(props) => (props.isSelected ? 'var(--vscode-button-secondaryForeground, #72747C)' : 'var(--vscode-editor-foreground, #72747C)')};
  border-radius: 4px;
  cursor: ${(props) => (props.isNotSelectable ? 'default' : 'pointer')};
  text-align: left;
  font-size: 12px;
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
  overflow-wrap: break-word;

  ${(props) =>
    !props.isNotSelectable &&
    `
		&:hover {
			background: var(--vscode-button-secondaryBackground, #72747C);
			color: var(--vscode-button-secondaryForeground, #72747C);
		}
	`}
`;

export const OptionsButtons = ({
  options,
  selected,
  isActive,
}: {
  options?: string[];
  selected?: string;
  isActive?: boolean;
}) => {
  if (!options?.length) return null;

  const hasSelected = selected !== undefined && options.includes(selected);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        paddingTop: 15,
        // marginTop: "22px",
      }}
    >
      {/* <div style={{ color: "var(--vscode-descriptionForeground)", fontSize: "11px", textTransform: "uppercase" }}>
				SELECT ONE:
			</div> */}
      {options.map((option, index) => (
        <OptionButton
          key={index}
          isSelected={option === selected}
          isNotSelectable={hasSelected || !isActive}
          onClick={() => {
            if (hasSelected || !isActive) {
              return;
            }
            vscode.postMessage({
              type: 'optionsResponse',
              text: option,
            });
          }}
        >
          {option}
        </OptionButton>
      ))}
    </div>
  );
};
