import { VSCodeButton, VSCodeLink } from '@vscode/webview-ui-toolkit/react';
import { memo } from 'react';
import { getAsVar, VSC_DESCRIPTION_FOREGROUND, VSC_INACTIVE_SELECTION_BACKGROUND } from '../../utils/vscStyles';

interface AnnouncementProps {
  version: string;
  hideAnnouncement: () => void;
}

/*
You must update the latestAnnouncementId in JoyCoderProvider for new announcements to show to users. This new id will be compared with whats in state for the 'last announcement shown', and if it's different then the announcement will render. As soon as an announcement is shown, the id will be updated in state. This ensures that announcements are not shown more than once, even if the user doesn't close it themselves.
*/
const Announcement = ({ version, hideAnnouncement }: AnnouncementProps) => {
  // const minorVersion = version.split(".").slice(0, 2).join(".") // 2.0.0 -> 2.0
  return (
    <div
      style={{
        backgroundColor: getAsVar(VSC_INACTIVE_SELECTION_BACKGROUND),
        borderRadius: '3px',
        padding: '12px 16px',
        margin: '5px 15px 5px 15px',
        position: 'relative',
        flexShrink: 0,
      }}
    >
      {/* <VSCodeButton appearance="icon" onClick={hideAnnouncement} style={{ position: "absolute", top: "8px", right: "8px" }}>
				<span className="codicon codicon-close"></span>
			</VSCodeButton>
		  */}

      {/*<ul style={{ margin: "0 0 8px", paddingLeft: "12px" }}>
				 <li>
					OpenRouter now supports prompt caching! They also have much higher rate limits than other providers,
					so I recommend trying them out.
					<br />
					{!apiConfiguration?.openRouterApiKey && (
						<VSCodeButtonLink
							href={getOpenRouterAuthUrl(vscodeUriScheme)}
							style={{
								transform: "scale(0.85)",
								transformOrigin: "left center",
								margin: "4px -30px 2px 0",
							}}>
							Get OpenRouter API Key
						</VSCodeButtonLink>
					)}
					{apiConfiguration?.openRouterApiKey && apiConfiguration?.apiProvider !== "openrouter" && (
						<VSCodeButton
							onClick={() => {
								vscode.postMessage({
									type: "apiConfiguration",
									apiConfiguration: { ...apiConfiguration, apiProvider: "openrouter" },
								})
							}}
							style={{
								transform: "scale(0.85)",
								transformOrigin: "left center",
								margin: "4px -30px 2px 0",
							}}>
							Switch to OpenRouter
						</VSCodeButton>
					)}
				</li>
				<li>
					<b>Edit JoyCoder's changes before accepting!</b> When he creates or edits a file, you can modify his
					changes directly in the right side of the diff view (+ hover over the 'Revert Block' arrow button in
					the center to undo "<code>{"// rest of code here"}</code>" shenanigans)
				</li>
				<li>
					New <code>use_search_files</code> tool that lets JoyCoder perform regex searches in your project, letting
					him refactor code, address TODOs and FIXMEs, remove dead code, and more!
				</li>
				<li>
					When JoyCoder runs commands, you can now type directly in the terminal (+ support for Python
					environments)
				</li>
			</ul>*/}
      <div
        style={{
          height: '1px',
          background: getAsVar(VSC_DESCRIPTION_FOREGROUND),
          opacity: 0.1,
          margin: '8px 0',
        }}
      />
      <p style={{ margin: '0' }}>
        Join our{' '}
        <VSCodeLink style={{ display: 'inline' }} href="https://discord.gg/joycoder">
          discord
        </VSCodeLink>{' '}
        or{' '}
        <VSCodeLink style={{ display: 'inline' }} href="https://www.reddit.com/r/joycoder/">
          r/joycoder
        </VSCodeLink>
        for more updates!
      </p>
    </div>
  );
};

export default memo(Announcement);
