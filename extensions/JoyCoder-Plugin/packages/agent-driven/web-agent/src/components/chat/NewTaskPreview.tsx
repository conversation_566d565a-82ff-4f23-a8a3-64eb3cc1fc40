import React from 'react';
import MarkdownBlock from '../common/MarkdownBlock';

interface NewTaskPreviewProps {
  context: string;
}

const NewTaskPreview: React.FC<NewTaskPreviewProps> = ({ context }) => {
  return (
    <div className="rounded-[3px] p-[14px] pb-[6px] overflow-hidden text-wrap-force joycoder-task-box" style={{
      backgroundColor: 'var(--vscode-editor-background)',
      color: 'var(--vscode-editor-foreground)',
    }}>
      <span style={{ fontWeight: 'bold' }}>任务</span>
      <MarkdownBlock markdown={context} />
    </div>
  );
};

export default NewTaskPreview;
