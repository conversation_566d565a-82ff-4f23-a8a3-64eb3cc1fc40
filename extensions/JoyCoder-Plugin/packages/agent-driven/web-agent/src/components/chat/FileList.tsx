import React, { useState } from 'react';
import { vscode } from '../../utils/vscode';

interface FileTreeItemProps {
  path: string;
  level: number;
}

const FileTreeItem: React.FC<FileTreeItemProps> = ({ path, level }) => {
  const parts = path.split('/');
  const name = parts[parts.length - 1];
  const extName = name.includes('.') ? name.split('.').pop() || '' : '';

  const getFileIcon = (ext: string) => {
    return 'file';
  };

  const itemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    padding: '3px 0',
    paddingLeft: `${level * 16}px`,
    fontSize: '13px',
    color: 'var(--vscode-foreground, #cccccc)',
    cursor: 'pointer',
    userSelect: 'none',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  };

  const iconStyle: React.CSSProperties = {
    width: '16px',
    height: '16px',
    marginRight: '6px',
    display: 'inline-flex',
    justifyContent: 'center',
    alignItems: 'center',
  };

  return (
    <div
      style={itemStyle}
      className="file-tree-item"
      onClick={() => {
        vscode.postMessage({
          type: 'openFile',
          text: path,
        });
        console.log(path);
      }}
    >
      <div style={iconStyle}>
        <span
          className={`codicon codicon-${getFileIcon(extName)}`}
          style={{
            color: 'var(--vscode-foreground)',
            margin: '0 6px -1.5px 0',
          }}
        ></span>
      </div>
      <span>{name}</span>
    </div>
  );
};

interface FileListProps {
  files: string[];
}

const FileList: React.FC<FileListProps> = ({ files }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const containerStyle: React.CSSProperties = {
    fontFamily: 'var(--vscode-font-family, "Segoe WPC", "Segoe UI", sans-serif)',
    fontSize: 'var(--vscode-font-size, 13px)',
    backgroundColor: 'var(--vscode-sideBar-background, #252526)',
    color: 'var(--vscode-foreground, #cccccc)',
    border: '1px solid var(--vscode-widget-border, #454545)',
    borderRadius: '3px',
    overflow: 'hidden',
    marginTop: '8px',
    marginBottom: '8px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.16)',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    padding: '8px 12px',
    backgroundColor: 'var(--vscode-sideBarSectionHeader-background, #252526)',
    borderBottom: isExpanded ? '1px solid var(--vscode-widget-border, #454545)' : 'none',
    cursor: 'pointer',
    userSelect: 'none',
  };

  const chevronStyle: React.CSSProperties = {
    display: 'inline-block',
    width: '16px',
    height: '16px',
    marginRight: '6px',
    transition: 'transform 0.2s',
    transform: isExpanded ? 'rotate(90deg)' : 'none',
  };

  const contentStyle: React.CSSProperties = {
    maxHeight: isExpanded ? '100px' : '0',
    overflow: 'auto',
    transition: 'max-height 0.3s ease-in-out',
  };

  return (
    <div style={containerStyle}>
      <div style={headerStyle} onClick={toggleExpand}>
        <div style={chevronStyle}>
          <span
            className={`codicon codicon-chevron-right`}
            style={{
              color: 'var(--vscode-foreground)',
              margin: '0 10px 0 0',
            }}
          ></span>
        </div>
        <span>搜索到当前代码库中 {files.length} 个文件</span>
      </div>
      <div style={contentStyle}>
        {isExpanded && files.map((file, index) => <FileTreeItem key={index} path={file} level={1} />)}
      </div>
    </div>
  );
};

export default FileList;
