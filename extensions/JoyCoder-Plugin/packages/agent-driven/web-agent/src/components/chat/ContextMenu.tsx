import React, { useEffect, useMemo, useRef } from 'react';
import { ContextMenuOptionType, ContextMenuQueryItem, getContextMenuOptions } from '../../utils/context-mentions';
import { cleanPathPrefix } from '../common/CodeAccordian';

interface ContextMenuProps {
  onSelect: (type: ContextMenuOptionType, value?: string) => void;
  searchQuery: string;
  onMouseDown: () => void;
  selectedIndex: number;
  setSelectedIndex: (index: number) => void;
  selectedType: ContextMenuOptionType | null;
  queryItems: ContextMenuQueryItem[];
  updateStatus?: string;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  onSelect,
  searchQuery,
  onMouseDown,
  selectedIndex,
  setSelectedIndex,
  selectedType,
  queryItems,
  updateStatus,
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  const filteredOptions = useMemo(
    () => getContextMenuOptions(searchQuery, selectedType, queryItems),
    [searchQuery, selectedType, queryItems],
  );

  useEffect(() => {
    if (menuRef.current) {
      const selectedElement = menuRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        const menuRect = menuRef.current.getBoundingClientRect();
        const selectedRect = selectedElement.getBoundingClientRect();

        if (selectedRect.bottom > menuRect.bottom) {
          menuRef.current.scrollTop += selectedRect.bottom - menuRect.bottom;
        } else if (selectedRect.top < menuRect.top) {
          menuRef.current.scrollTop -= menuRect.top - selectedRect.top;
        }
      }
    }
  }, [selectedIndex]);

  const renderOptionContent = (option: ContextMenuQueryItem) => {
    switch (option.type) {
      case ContextMenuOptionType.Web:
        return <span>Web</span>;
      case ContextMenuOptionType.UserRules:
        if (option.value) {
          // 去掉结尾的点
          const trimmedValue = option.value.endsWith('.') ? option.value.slice(0, -1) : option.value;
          return (
            <>
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  direction: 'ltr',
                  textAlign: 'left',
                }}
              >
                {trimmedValue}
              </span>
            </>
          );
        } else {
          return <span>Rules</span>;
        }
      case ContextMenuOptionType.Resource:
        // document的子类，暂定为文件
        if (option.value) {
          return (
            <>
              <span>/</span>
              {option.value?.startsWith('/.') && <span>.</span>}
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  direction: 'rtl',
                  textAlign: 'left',
                }}
              >
                {cleanPathPrefix(option.value || '') + '\u200E'}
              </span>
            </>
          );
        } else {
          return <span>Resource</span>;
        }
      case ContextMenuOptionType.Problems:
        return <span>Problems</span>;
      case ContextMenuOptionType.URL:
        return <span>粘贴URL来获取内容</span>;
      case ContextMenuOptionType.NoResults:
        return <span>{updateStatus === 'Updating' ? '数据索引中···' : '暂无数据'}</span>;
      case ContextMenuOptionType.File:
      case ContextMenuOptionType.Folder:
        if (option.value) {
          return (
            <>
              <span>/</span>
              {option.value?.startsWith('/.') && <span>.</span>}
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  direction: 'rtl',
                  textAlign: 'left',
                }}
              >
                {cleanPathPrefix(option.value || '') + '\u200E'}
              </span>
            </>
          );
        } else {
          return <span>{option.type === ContextMenuOptionType.File ? 'File' : 'Folder'}</span>;
        }
      case ContextMenuOptionType.Codebase:
        return <span>Codebase</span>;
    }
  };

  const getIconForOption = (option: ContextMenuQueryItem): string => {
    switch (option.type) {
      case ContextMenuOptionType.File:
        return 'rizhi';
      case ContextMenuOptionType.Folder:
        return 'wenjianjia';
      case ContextMenuOptionType.Problems:
        return 'wenhao';
      case ContextMenuOptionType.Web:
        return 'lianwangsousuo';
      case ContextMenuOptionType.UserRules:
        return option.value ? 'wendang' : 'guize';
      case ContextMenuOptionType.Resource:
        return 'wendang';
      case ContextMenuOptionType.URL:
        return 'link';
      case ContextMenuOptionType.Codebase:
        return 'wenjian';
      case ContextMenuOptionType.NoResults:
        return updateStatus === 'Updating' ? 'loading' : 'info';
      default:
        return 'file';
    }
  };

  const isOptionSelectable = (option: ContextMenuQueryItem): boolean => {
    return option.type !== ContextMenuOptionType.NoResults && option.type !== ContextMenuOptionType.URL;
  };

  return (
    <div
      style={{
        position: 'absolute',
        bottom: 'calc(100% - 10px)',
        left: 15,
        right: 15,
        overflowX: 'hidden',
        width: '200px',
        background: 'var(--vscode-editor-background)',
        // border: '1px solid rgba(48,48,53,1)',
        border: '1px solid var(--vscode-editorWidget-background)',
        borderRadius: '6px',
        // boxShadow: '0 8px 48px 0 rgba(0,0,0,1), 0 4px 8px 0 rgba(0,0,0,1)',
        boxShadow:
          'var(--vscode-editorWidget-background) 0px 8px 48px 0px, var(--vscode-editorWidget-background) 0px 4px 8px 0px',
      }}
      onMouseDown={onMouseDown}
    >
      <div
        ref={menuRef}
        style={{
          zIndex: 500,
          display: 'flex',
          flexDirection: 'column',
          maxHeight: '240px',
          overflowY: 'auto',
          padding: '4px 7px',
        }}
      >
        {/* Can't use virtuoso since it requires fixed height and menu height is dynamic based on # of items */}
        {filteredOptions.map((option, index) => (
          <div
            key={`${option.type}-${option.value || index}`}
            onClick={() => isOptionSelectable(option) && onSelect(option.type, option.value)}
            style={{
              padding: '6px 12px',
              cursor: isOptionSelectable(option) ? 'pointer' : 'default',
              // color:
              //   index === selectedIndex && isOptionSelectable(option) ? 'var(--vscode-editorWidget-foreground)' : '',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              backgroundColor:
                index === selectedIndex && isOptionSelectable(option) ? 'var(--vscode-button-secondaryBackground, #72747C)' : '',
              color:
                index === selectedIndex && isOptionSelectable(option) ? 'var(--vscode-button-secondaryForeground, #72747C)' : '',
              borderRadius: index === selectedIndex && isOptionSelectable(option) ? '4px' : '0',
            }}
            onMouseEnter={() => isOptionSelectable(option) && setSelectedIndex(index)}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                flex: 1,
                minWidth: 0,
                overflow: 'hidden',
              }}
            >
              <i
                className={`icon iconfont icon-${getIconForOption(option)} ${getIconForOption(option) === 'loading' && updateStatus === 'Updating' ? 'spinning-loading' : ''}`}
                style={{
                  marginRight: '8px',
                  flexShrink: 0,
                  fontSize: '15px',
                }}
              />
              {renderOptionContent(option)}
            </div>
            {(option.type === ContextMenuOptionType.File ||
              option.type === ContextMenuOptionType.Folder ||
              (option.type === ContextMenuOptionType.UserRules && !option.value) ||
              option.type === ContextMenuOptionType.Resource) &&
              !option.value && (
                <i
                  className="icon iconfont icon-youjiantou"
                  style={{
                    fontSize: '14px',
                    flexShrink: 0,
                    marginLeft: 8,
                  }}
                />
              )}
            {(option.type === ContextMenuOptionType.Problems ||
              option.type === ContextMenuOptionType.Web ||
              option.type === ContextMenuOptionType.Codebase ||
              ((option.type === ContextMenuOptionType.File ||
                option.type === ContextMenuOptionType.Folder ||
                option.type === ContextMenuOptionType.Resource ||
                option.type === ContextMenuOptionType.UserRules) &&
                option.value)) && (
              <i
                className="icon iconfont icon-xinzeng"
                style={{
                  fontSize: '14px',
                  flexShrink: 0,
                  marginLeft: 8,
                }}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContextMenu;
