import { useCallback } from 'react';
import { Button } from 'antd';
import { vscode } from '../../utils/vscode';

interface FollowUpSuggestProps {
  suggestions?: string[];
  ts: number;
}

export const FollowUpSuggest = ({ suggestions = [], ts = 1 }: FollowUpSuggestProps) => {
  const handleSuggestionClick = useCallback((suggestion: string) => {
    vscode.postMessage({
      type: 'optionsResponse',
      text: suggestion,
    });
  }, []);

  // Don't render if there are no suggestions or no click handler.
  if (!suggestions?.length) {
    return null;
  }

  return (
    <div className="flex mb-2 flex-col h-full gap-2">
      {suggestions.map((suggestion) => (
        <div key={`${suggestion}-${ts}`} className="w-full relative group">
          <Button
            type="default"
            className="text-left whitespace-normal break-words w-full h-auto py-3 justify-start pr-8"
            onClick={(event) => handleSuggestionClick(suggestion)}
            aria-label={suggestion}
          >
            <div>{suggestion}</div>
          </Button>
        </div>
      ))}
    </div>
  );
};
