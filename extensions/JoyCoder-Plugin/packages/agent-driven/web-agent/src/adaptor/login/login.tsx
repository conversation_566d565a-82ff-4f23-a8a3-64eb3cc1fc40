import { vscode } from '../../utils/vscode';
import { locale } from '../locales';
import './index.scss';

export default function Login(props?: any) {
  const login = () => {
    vscode.postMessage({
      type: 'JUMP_LOGIN',
    });
  };

  return (
    <>
      {props?.isLogined === false && (
        <div className="joycoder-chart-login">
          <div className="joycoder-chart-login-logo">
            <div className='logo-img'></div>
          </div>
          <div className="joycoder-chart-login-title">{locale.welcome.title}</div>
          <div className="joycoder-chart-login-btn" onClick={login}>
            登录
          </div>
        </div>
      )}
    </>
  );
}
