.joycoder-chart-login{
  font-family: 'PingFang SC';
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  margin: 0;
  position: fixed;
  z-index: 10;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background);
  &-logo{
    margin: 40vh auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .logo-img{
      width: 131px;
      height: 30px;
      background: url(../../assets/images/home-logo.svg) no-repeat;
      background-size: 131px 30px;
    }
  }
  &-title{
    line-height: 24px;
    font-size: 24px;
    text-align: center;
    margin: 12px 0 0;
    font-weight: 300;
    color: var(--vscode-editor-foreground);
  }
  &-btn{
    width: 200px;
    height: 28px;
    line-height: 28px;
    border-radius: 4px;
    background: var(--vscode-button-secondaryBackground, #72747c);
    color: var(--vscode-button-secondaryForeground, #72747c);
    font-size: 12px;
    font-family: PingFang SC;
    text-align: center;
    font-weight: 600;
    margin: 6.8vh auto 0;
    cursor: pointer;
  }
}
.joycoder-light {
  .joycoder-chart-desc,.joycoder-chart-question{
    h4{
      color: var(--vscode-button-secondaryForeground, #72747c);
    }
  }
}

.vscode-light {
  .logo-img {
    width: 131px;
    height: 30px;
    background: url(../../assets/images/home-logo-light.svg) no-repeat;
    background-size: 131px 30px;
  }
}
