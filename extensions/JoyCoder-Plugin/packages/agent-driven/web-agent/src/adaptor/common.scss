.joycoder-auto-code{
  &-content{
    position: relative;
    overflow: hidden;
    width: 100%;
    min-height: 64px;
  }
  &-btns {
    position: absolute;
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    top: 0;
    left: 0;
    z-index: 1;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    overflow: hidden;
    padding: 0 16px;
  }
  &-btn{
    width: 32px;
    height: 32px;
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 4px;
    margin-right: 16px;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    &.add{
      background-image: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/plus.svg);
    }
    &.history{
      background-image: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/history.svg);
    }
    &.server{
      background-image: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/server.svg);
      background-size: 60% 60%;
    }
  }
  &-thinking-text {
    color: #666;
    font-style: italic;
    padding-left: 13px;
  }
  &-thinking-text::after {
    content: '';
    animation: dots 1.5s infinite;
  }

  @keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }
}
