import { useCallback, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useEvent } from 'react-use';
import styled from 'styled-components';
import { ExtensionMessage, JoyCoderMessage } from '../../../../src/shared/ExtensionMessage';
import { MessageUtils } from '../../../../src/shared/MessageUtils';
import { vscode } from '../../utils/vscode';
import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';
import LottieAnimation from '../../components/common/LottieAnimation';
import waitingAnimation from '../../assets/animations/waiting.json';

interface CheckmarkControlAdaptorProps {
  messageTs?: number;
  previousMessage?: JoyCoderMessage | null;
}

export interface CheckmarkControlAdaptorRef {
  handleRollbackClick: () => void;
  shouldShowTextButton: boolean;
  isDisabled: boolean;
}

export const CheckmarkControlAdaptor = forwardRef<CheckmarkControlAdaptorRef, CheckmarkControlAdaptorProps>(
  ({ messageTs, previousMessage }, ref) => {
    const [restoreBothDisabled, setRestoreBothDisabled] = useState(false);
    const [showRestoreConfirm, setShowRestoreConfirm] = useState(false);

    // 判断上一条消息是否为AI消息
    const isPreviousMessageAI = useCallback(() => {
      if (!previousMessage) return false;

      // 使用统一的MessageUtils判断逻辑
      return MessageUtils.isAIMessage(previousMessage);
    }, [previousMessage]);

    // 根据上一条消息类型决定显示样式
    // 只有当上一条消息是AI消息时才显示文字按钮，用户消息时只显示图标
    const shouldShowTextButton = isPreviousMessageAI();

    const handleMessage = useCallback((event: MessageEvent<ExtensionMessage>) => {
      if (event.data.type === 'relinquishControl') {
        setRestoreBothDisabled(false);
        setShowRestoreConfirm(false);
      }
    }, []);

    const handleRestoreBoth = () => {
      setRestoreBothDisabled(true);
      setShowRestoreConfirm(false);
      vscode.postMessage({
        type: 'checkpointRestore',
        number: messageTs,
        text: 'taskAndWorkspace',
      });
    };

    const handleCancel = () => {
      setShowRestoreConfirm(false);
    };

    const handleButtonClick = useCallback(() => {
      setShowRestoreConfirm(!showRestoreConfirm);
    }, [showRestoreConfirm]);

    // 暴露给父组件的方法
    useImperativeHandle(
      ref,
      () => ({
        handleRollbackClick: handleButtonClick,
        shouldShowTextButton,
        isDisabled: restoreBothDisabled,
      }),
      [handleButtonClick, shouldShowTextButton, restoreBothDisabled],
    );

    useEvent('message', handleMessage);

    return (
      <Container>
        {showRestoreConfirm && (
          <RestoreConfirmDialog>
            <DialogContainer>
              <div>
                <DialogTitle>回滚对话及代码变更至此消息之前？</DialogTitle>
                <WaitingText>
                  <LottieAnimation
                    animationData={waitingAnimation}
                    width="12px"
                    height="12px"
                    loop={true}
                    autoplay={true}
                  />
                  等待回复
                  <DynamicDots />
                </WaitingText>
              </div>
              <ButtonGroup>
                <CancelButton onClick={handleCancel}>取消</CancelButton>
                <ConfirmButton onClick={handleRestoreBoth} disabled={restoreBothDisabled}>
                  确定
                </ConfirmButton>
              </ButtonGroup>
            </DialogContainer>
          </RestoreConfirmDialog>
        )}
      </Container>
    );
  },
);

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  width: 100%;
`;

const RestoreConfirmDialog = styled.div`
  background: var(--vscode-editor-background, --vscode-sideBar-background, #72747c);
  border: 1px solid var(--vscode-editorGroup-border, #72747c);
  border-radius: 6px;
  padding: 0 16px;
  margin-top: 8px;
  width: 100%;
  box-shadow: 0 2px 6px 0 rgba(48, 49, 51, 0.06);
  height: 60px;
  display: flex;
  align-items: center;
`;

const DialogContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

const DialogTitle = styled.div`
  color: var(--vscode-editor-foreground, #72747c);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 18px;
  font-family: PingFang SC;
  font-weight: normal;
`;

const DynamicDots = () => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev.length >= 3) return '';
        return prev + '.';
      });
    }, 400);

    return () => clearInterval(interval);
  }, []);

  return <span>{dots}</span>;
};

const WaitingText = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--vscode-editor-foreground, #72747c);
  font-size: 12px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  height: 28px;
`;

const CancelButton = styled(VSCodeButton)`
  min-width: 48px;
  background: var(--vscode-button-secondaryBackground, #72747c);
  color: var(--vscode-button-secondaryForeground, #72747c);
  &::part(control) {
    padding: 0 !important;
  }
`;

const ConfirmButton = styled(VSCodeButton)`
  min-width: 48px;
  // background: var(--vscode-button-background);
  // color: var(--vscode-button-foreground);
  background: var(--vscode-button-background, #72747c);
  color: var(--vscode-button-foreground, #72747c);
  font-size: 12px;
  &::part(control) {
    padding: 0 !important;
  }

  &:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground, #72747c);
    opacity: 0.8;
  }
`;
