import { memo, useMemo } from 'react';
// import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';
import { getLanguageFromPath } from '../../utils/getLanguageFromPath';
import CodeBlock from '../../components/common/CodeBlock';
import { FileIcon } from '../../components/common';
import { ButtonText } from '../../../../src/shared/ExtensionMessage';
import { convertConflictToDiff } from '../../utils/convertConflictToDiff';
interface CodeAccordianProps {
  code?: string;
  diff?: string;
  language?: string | undefined;
  path?: string;
  isFeedback?: boolean;
  isConsoleLogs?: boolean;
  isExpanded: boolean;
  onToggleExpand: () => void;
  isLoading?: boolean;
  icon?: React.ReactNode;
  lastOne?: boolean;
  handleSecondaryButtonClick?: () => void;
  handlePrimaryButtonClick?: () => void;
  isStreaming?: boolean;
  buttonText?: ButtonText;
  progressStatus?: any;
}

/*
We need to remove leading non-alphanumeric characters from the path in order for our leading ellipses trick to work.
^: Anchors the match to the start of the string.
[^a-zA-Z0-9]+: Matches one or more characters that are not alphanumeric.
The replace method removes these matched characters, effectively trimming the string up to the first alphanumeric character.
*/
export const cleanPathPrefix = (path: string): string => path.replace(/^[^\u4e00-\u9fa5a-zA-Z0-9]+/, '');



const CodeAccordian = ({
  icon,
  code,
  diff,
  language,
  path,
  isFeedback,
  isConsoleLogs,
  isExpanded,
  onToggleExpand,
  isLoading,
  progressStatus,
}: CodeAccordianProps) => {
  const inferredLanguage = useMemo(
    () => code && (language ?? (path ? getLanguageFromPath(path) : undefined)),
    [path, language, code],
  );

  // const diffStyledCode = useMemo(() => diff && getDiffLinesFromDiff, [diff]);

  return (
    <div
      style={{
        borderRadius: 3,
        margin: '4px 0 10px',
        backgroundColor: 'var(--vscode-editor-background, --vscode-sideBar-background, #72747C)',
        overflow: 'hidden',
        border: '1px solid var(--vscode-editorGroup-border, #72747C)'
      }}
    >
      {(path || isFeedback || isConsoleLogs) && (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '9px 10px',
            cursor: isLoading ? 'wait' : 'pointer',
            opacity: isLoading ? 0.7 : 1,
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
          }}
          onClick={isLoading ? undefined : onToggleExpand}
        >
          {isFeedback || isConsoleLogs ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                className={`codicon codicon-${isFeedback ? 'feedback' : 'output'}`}
                style={{ marginRight: '6px' }}
              ></span>
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  marginRight: '8px',
                }}
              >
                {isFeedback ? 'User Edits' : 'Console Logs'}
              </span>
            </div>
          ) : (
            <>
              <FileIcon path={path} fallbackIcon={icon} />
              {path?.startsWith('.') && <span>.</span>}
              <span
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  marginRight: '8px',
                  // trick to get ellipsis at beginning of string
                  direction: 'rtl',
                  textAlign: 'left',
                }}
              >
                {cleanPathPrefix(path ?? '') + '\u200E'}
              </span>
            </>
          )}
          <div style={{ flexGrow: 1 }}></div>
          {progressStatus && progressStatus.text && (
            <>
              {progressStatus.icon && <span className={`codicon codicon-${progressStatus.icon} mr-1`} />}
              <span className="mr-1 ml-auto text-vscode-descriptionForeground">{progressStatus.text}</span>
            </>
          )}
          <span
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <i className={`icon iconfont icon-a-${isExpanded ? 'shouqishangxia' : 'zhankaishangxia'}`} />
          </span>
        </div>
      )}
      {(!(path || isFeedback || isConsoleLogs) || isExpanded) && (
        <div
          //className="code-block-scrollable" this doesn't seem to be necessary anymore, on silicon macs it shows the native mac scrollbar instead of the vscode styled one
          style={{
            overflowX: 'auto',
            overflowY: 'hidden',
            maxWidth: '100%',
          }}
        >
          <CodeBlock
            source={`${'```'}${diff !== undefined ? 'diff' : inferredLanguage}\n${(
              code ??
              convertConflictToDiff(diff || '') ??
              ''
            ).trim()}\n${'```'}`}
          />
        </div>
      )}
    </div>
  );
};

// memo does shallow comparison of props, so if you need it to re-render when a nested object changes, you need to pass a custom comparison function
export default memo(CodeAccordian);
