import React from 'react';
import { VSCodeButton } from '@vscode/webview-ui-toolkit/react';
import { locale } from '../locales';

interface HeaderToolsProps {
  newChat: () => void;
  toggleHistoryPreview: () => void;
  toggleMCP: () => void;
}

const HeaderTools: React.FC<HeaderToolsProps> = ({ newChat, toggleHistoryPreview, toggleMCP }) => {
  return (
    <div
      style={{
        padding: '0 20px',
        flexShrink: 0,
        height: 44,
        borderBottom: '1px solid rgba(179, 179, 179, 0.1)',
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'center',
      }}
    >
      <VSCodeButton
        appearance="icon"
        onClick={newChat}
        style={{
          opacity: 0.9,
          width: 24,
          height: 24,
          border: '1px solid rgba(190, 190, 190, 0.1)',
          borderRadius: 4,
          marginLeft: 10,
          background: 'rgba(34, 34, 34, 0.5)',
        }}
      >
        <div
          style={{
            fontSize: 'var(--vscode-font-size)',
            color: 'var(--vscode-descriptionForeground)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <img
            src="https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/plus.svg"
            style={{ width: '16px', height: '16px', display: 'block' }}
            alt={locale.header.newChat}
            title={locale.header.newChat}
          />
        </div>
      </VSCodeButton>

      <VSCodeButton
        appearance="icon"
        onClick={toggleHistoryPreview}
        style={{
          opacity: 0.9,
          width: 24,
          height: 24,
          border: '1px solid rgba(179, 179, 179,0.1)',
          borderRadius: 4,
          marginLeft: 10,
          background: 'rgba(34, 34, 34, 0.5)',
        }}
      >
        <div
          style={{
            fontSize: 'var(--vscode-font-size)',
            color: 'var(--vscode-descriptionForeground)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <img
            src="https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/history.svg"
            style={{ width: '16px', height: '16px', display: 'block' }}
            alt={locale.header.history}
            title={locale.header.history}
          />
        </div>
      </VSCodeButton>

      <VSCodeButton
        appearance="icon"
        onClick={toggleMCP}
        style={{
          opacity: 0.9,
          width: 24,
          height: 24,
          border: '1px solid rgba(179, 179, 179,0.1)',
          borderRadius: 4,
          marginLeft: 10,
          background: 'rgba(34, 34, 34, 0.5)',
        }}
      >
        <div
          style={{
            fontSize: 'var(--vscode-font-size)',
            color: 'var(--vscode-descriptionForeground)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <img
            src="https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/mcp-hub.svg"
            style={{ width: '16px', height: '16px', display: 'block' }}
            alt={locale.header.tools}
            title={locale.header.tools}
          />
        </div>
      </VSCodeButton>
    </div>
  );
};

export default HeaderTools;
