.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item{
  line-height: 22px;
}

.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu {
  max-width: 250px;
  font-size: 11px;
  padding: 5px 12px 5px 10px;
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu_action {
  display: inline-flex;
  align-items: center;
  color: var(--vscode-editor-foreground， #72747C);
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu_content {
  display: inline-flex;
  flex-direction: column;
  margin-left: 5px;
  font-size: 11px;
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu_content_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu_content_header_icons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_btn {
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .custom-dropdown-menu-item .chatconfig_menu_content_tip {
  max-width: 200px;
  padding-right: 12px;
  font-size: 9px;
  line-height: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.55;
}
.joycoder-chatgpt-model-dropdown .joycoder-dark-dropdown-menu-title-content {
  flex: auto;
}
.joycoder-chatgpt-model-dropdown .model-icon{
  width: 12px;
  height: 12px;
  /* padding-bottom: 1px; */
  border-radius: 50%;
  overflow: hidden;
  vertical-align: middle;
  border-style: none;
}
.joycoder-chatgpt-model-dropdown .model-icon + span{
  font-size: 11px;
  /* padding-bottom: 1px; */
  max-width: 90%;
  margin-left: 4px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: var(--vscode-dropdown-foreground);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--recommend{
  color: #e36459 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(227, 100, 89, 0.15);
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--function {
  color: #55b467 !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(85, 180, 103, 0.15);
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--tokens {
  font-size: 9px !important;
  padding: 0px 2px;
  display: block;
  line-height: 12px;
  border-radius: 2px;
  background: #333;
  color: #999;
  margin-left: 4px;
}
.joycoder-chatgpt-model-dropdown .chatconfig_menu_content_header_icons--vision {
  margin-left: 4px;
  color: #369eff !important;
  padding: 2px;
  border-radius: 2px;
  font-size: 9px !important;
  background: rgba(54, 158, 255, 0.15);
}
.joycoder-chatgpt-input-icons .codicon-send.codicon[class*='codicon-'] {
  transform: rotate(-30deg);
  height: 17px;
}
.joycoder-chatgpt-input-icons .codicon-send:before {
  content: "\ec0f";
  background: linear-gradient(197.97deg, rgba(76, 213, 255, 1) 0%, rgba(54, 87, 255, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:hover {
  background-color: rgba(255,255,255,0.08);
  border-radius: 2px;
}
.joycoder-chatgpt-input-icons .codicon-device-camera:before {
  content: "";
}
.mention-context-textarea-highlight-layer-inner{
  width: 100%;
  border-radius: 6px !important;
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid var(--vscode-input-border, #72747C);
  background-color: var(--vscode-input-background, #72747C);
}
.mention-context-textarea-highlight-layer-inner::placeholder{
  color: var(--vscode-input-placeholderForeground);
}
.mention-context-textarea-highlight-layer-inner.focus {
  /* border-image: linear-gradient(109.95deg, rgba(88,177,242,1) 0%,rgba(255,38,61,1) 100%) 4 4; */
  border-radius: 6px !important;
  border: 1px solid transparent;
  background: linear-gradient(109.95deg, rgba(88, 177, 242, 1) 0%, rgba(255, 38, 61, 1) 100%) border-box;
  mask-composite: exclude;
}
.vscode-light .mention-context-textarea-highlight-layer-inner {
  background: #cacaca;
}
.mention-context-textarea-highlight-layer-inner:hover {
  /* border-image: linear-gradient(109.95deg, rgba(88,177,242,1) 0%,rgba(255,38,61,1) 100%) 4 4; */
  border-radius: 6px !important;
  border: 1px solid transparent;
  background: linear-gradient(109.95deg, rgba(88, 177, 242, 1) 0%, rgba(255, 38, 61, 1) 100%) border-box;
  mask-composite: exclude;
}
/* .vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: rgba(31, 31, 31, 0.1);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
} */
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu{
  background-color: #ffff;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item {
  color: #616161;
}
.vscode-light .joycoder-chatgpt-model-dropdown .ant-dropdown-placement-top .ant-dropdown-menu-item:hover{
  background-color: #0000000a;
}
.vscode-light .joycoder-coder-mode-dropdown-button {
  color: #151515 !important;
}
.joycoder-login-btn{
  color: rgba(76,213,255,1) !important;
  cursor: pointer;
}

.vscode-light .joycoder-termination {
  width: 20px;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/light-termination.svg) no-repeat bottom center;
  cursor: pointer;
}
.joycoder-termination {
  width: 20px;
  height: 20px;
  font-size: 15px;
  background: url(https://storage.360buyimg.com/dist-dev/joycoder/chatDialog/termination.svg) no-repeat bottom center;
  cursor: pointer;
  opacity: 0.8;
}


.joycoder-coder-mode-tip,
.joycoder-chatgpt-model-dropdown .ant-tooltip{
  transform: scale(0.7);
  transform-origin: inherit;
  background-color: #1f1f1f;
}
.joycoder-coder-mode-tip .ant-tooltip-inner,
.joycoder-chatgpt-model-dropdown .ant-tooltip .ant-tooltip-inner{
  min-height: auto !important;
}
.joycoder-coder-textarea::placeholder{
  font-size: 12px;
  text-align: left;
  line-height: 1.25;
}

.dragging-over {
  border: 1px dashed var(--vscode-input-border, #72747C) !important;
  border-radius: 4px;
  background: none !important;
}

.joycoder-context-box{
  user-select: none;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  z-index: 1;
  overflow: hidden;
  height: 5px;
}
/* .vscode-light .joycoder-context-box{
  background-color: rgba(255,255,255,1);
} */
.joycoder-context-box > div{
  display: inline-flex;
  font-size: 12px;
  background: rgba(168, 168, 168 , 0.26);
  margin: 4px 8px 0 8px;
  border-radius: 2px;
  overflow: hidden;
  height: 16px;
}
.joycoder-context-box > div.active{
  background: rgba(0, 135, 255 , 0.19);
}
.joycoder-context-box > div .pulse-chip{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: 100%;
}
.joycoder-context-box > div .close-pulse-chip,
.joycoder-context-box > div .pulse-chip-icon{
  padding: 0 4px;
  font-size: 12px;
  cursor: pointer;
  vertical-align: middle;
}
