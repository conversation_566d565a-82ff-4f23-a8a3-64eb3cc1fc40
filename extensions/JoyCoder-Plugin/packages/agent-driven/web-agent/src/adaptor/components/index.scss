.joycoder-coder-mode-dropdown {
  width: 160px !important;
}
.joycoder-chatgpt-input-icons{
  z-index: 20 !important;
  color: var(--vscode-dropdown-foreground);
}
.joycoder-coder-mode-dropdown-option {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 13px;
  font-family: PingFang SC;
  font-weight: normal;
  color: var(--vscode-editor-foreground);
  line-height: 28px;
  padding: 0 8px;

  >  i {
    display: inline-flex !important;
    font-size: 16px !important;
    vertical-align: text-bottom;
    margin-right: 8px;
  }

  &-title {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  &-action {
    // 特殊操作项样式
  }
}
.joycoder-chatgpt-model-dropdown {
  width: 250px !important;
}
.joycoder-coder-mode-dropdown-button > span {
  font-size: 11px;
  margin-left: 4px;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: var(--vscode-button-secondaryForeground, #72747C);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown-button > span {
  font-size: 11px;
  margin-left: 4px;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: var(--vscode-button-secondaryForeground, #72747C);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown-button:hover{
  opacity: 0.8;
}
.joycoder-coder-mode-dropdown-button:hover{
  opacity: 0.8;
}
.vscode-light {
  .joycoder-chatgpt-input-icons {
    background: var(--background) !important;
  }
}

::selection {
  background-color: #303035FF !important;
  color: white !important;
}


.joycoder-parent-task{
  overflow: hidden;
  position: relative;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  padding: 0 12px;
  border-top: 1px solid var(--vscode-dropdown-border);
  border-bottom: 1px solid var(--vscode-dropdown-border);
  &-header {
    display: flex;
    padding: 0px 4px;
    background-color: transparent;
    font-size: 14px;
    color: var(--vscode-sideBarSectionHeader-foreground);
    text-align: left;
    overflow: hidden;
    height: 28px;
    line-height: 28px;
    width: 100%;
  }
}
