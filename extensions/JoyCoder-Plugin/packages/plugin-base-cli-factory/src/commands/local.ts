import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { ICommandQuickPick } from '../model';
import { getTargetPath, queryCustomConfigSync, getCurrentWorkspaceUri } from '@joycoder/shared';
import { formatCommandList } from '../utils';

/**
 * 读取本地 joycoder.config.json 自定义命令行配置
 *
 * @export
 * @param {vscode.Uri} uri
 * @return {*}  {ICommandQuickPick[]}
 */
export function getCustomLocalCommands(uri: vscode.Uri): ICommandQuickPick[] {
  const commandInvokePath = getCurrentWorkspaceUri(uri, true).fsPath;
  const joycoderConfigPath = getTargetPath(commandInvokePath, {
    isTarget: (filePath: string) => /joycoder\.config\.json$/.test(filePath),
    isEnd: (filePath: string) => fs.existsSync(path.join(filePath, '.git')),
  });

  if (!joycoderConfigPath) return [];

  const config = queryCustomConfigSync(path.dirname(joycoderConfigPath));
  if (!config) return [];

  const { customCommand } = config;
  // 命令配置为空
  if (!customCommand) return [];
  // 命令配置格式不正确
  if (!(customCommand instanceof Array) || !customCommand.length) return [];

  return formatCommandList(customCommand, { commandInvokePath });
}
