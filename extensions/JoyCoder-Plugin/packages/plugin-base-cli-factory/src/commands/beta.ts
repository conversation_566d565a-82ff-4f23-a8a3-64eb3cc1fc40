import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { compareVersions } from 'compare-versions';
import { ICommandQuickPick } from '../model';
import { getTargetPath, safeExecSync, submitIssue, Logger } from '@joycoder/shared';
import { execSync } from 'child_process';
import { version } from 'os';

type IPackageJson = {
  name: string;
  version: string;
};

type IPackageJsonOptions = {
  npm?: 'npm' | 'legos';
  registry?: string;
  path: string;
  versions?: string[];
  content?: IPackageJson;
  contentStr?: string;
};

/**
 * 获取npm路径
 *
 * @return {string}
 */
async function readPublishInfos(filePath: string) {
  const packageJsonPath = getTargetPath(filePath, {
    isTarget: (filePath: string) => /package\.json$/.test(filePath),
    isEnd: (filePath: string) => fs.existsSync(path.join(filePath, '.git')),
  });
  const cwd = path.dirname(packageJsonPath);
  const result = { cwd, packageJsonPath };
  if (!packageJsonPath) {
    Logger.showErrorMessage('当前路径未匹配到package.json文件');
    return result;
  }
  return result;
}

/**
 * 发布前二次确认npm路径
 *
 * @return {string}
 */
async function checkPublishInfos(pkOptions: IPackageJsonOptions) {
  const cwd = path.dirname(pkOptions.path);
  const packageJson = pkOptions.content;
  if (!packageJson) return null;
  if (fs.existsSync(path.join(cwd, '.git'))) {
    const choose = await vscode.window.showWarningMessage(
      `⚠️警告：即将发布的 ${packageJson.name} 为 项目根目录，将发布整个项目`,
      { modal: true },
      '继续发布'
    );
    if (choose !== '继续发布') return null;
  }
  const result = safeExecSync(`${pkOptions.npm} info ${packageJson.name} versions ${pkOptions.registry}`);
  if (/npm ERR\! 40(1|3|4)/.test(result)) {
    if (!/npm ERR\! 404/.test(result)) {
      Logger.showErrorMessage(result);
      return null;
    }
    const choose = await vscode.window.showWarningMessage(
      `⚠️警告：即将发布的 ${packageJson.name} 在远程npm不存在`,
      { modal: true },
      '继续发布'
    );
    if (choose !== '继续发布') return null;
  }
  pkOptions.versions = eval(result);
  return pkOptions;
}

/**
 * 读取packagejson
 *
 * @param {string} packageJsonPath
 * @return {*}  {Promise<IPackageJSON>}
 */
async function getPackageJsonOptions(config: IPackageJsonOptions): Promise<IPackageJsonOptions> {
  const packageJsonDocument = await vscode.workspace.openTextDocument(config.path);
  config.contentStr = packageJsonDocument.getText();
  config.content = JSON.parse(config.contentStr);
  config.npm = config.npm || 'npm';
  return config;
}

/**
 * 获取semver版本信息
 *
 * @export
 * @param {string} str
 * @return {*}
 */
export function getSemver(str: string) {
  const result = {
    semver: '',
    version: '',
    major: '',
    minor: '',
    patch: '',
    beta: '',
    betaPrefix: '',
    betaLetter: '',
    betaVersion: '',
  };
  const match = str.match(/^((\d+)\.(\d+)\.(\d+))((.*?(alpha|beta).*?)(\d+)?)?$/);
  if (!match) {
    Logger.showErrorMessage(`无法识别版本号: [${str}]`, '提个issue吧~').then((choose: string) => {
      if (choose === '提个issue吧~') submitIssue();
    });
    return result;
  }
  const [semver, version, major, minor, patch, beta, betaPrefix, betaLetter, betaVersion] = match;
  return {
    semver,
    version,
    major,
    minor,
    patch,
    beta,
    betaPrefix,
    betaLetter,
    betaVersion,
  };
}

/**
 * 比较两个npm版本
 *
 * @param {string} v1
 * @param {string} v2
 * @return {*}  {(0 | 1 | -1)}
 */
function compareSemverVersion(v1: string, v2: string): 0 | 1 | -1 {
  if (!v2) return 1;
  if (!v1) return -1;
  const semver1 = getSemver(v1);
  const semver2 = getSemver(v2);
  if (!semver2.version) return 1;
  if (!semver1.version) return -1;
  const versionResult = compareVersions(semver1.version || '0', semver2.version || '0');
  if (versionResult) return versionResult;
  const betaVersionResult = compareVersions(semver1.betaVersion || '0', semver2.betaVersion || '0');
  return betaVersionResult;
}

/**
 * 更新beta版本号
 *
 * @param {string} version
 * @return {*}
 */
function updateBetaVersion(version: string) {
  const semver = getSemver(version);
  if (!semver.beta) {
    // 没有beta，需要patch+1，再加上-beta.1
    return `${semver.major}.${semver.minor}.${+semver.patch + 1}-beta.1`;
  }
  if (!semver.betaVersion) {
    // 没有beta版本号，再在beta后面加上.1
    return `${semver.version}${semver.betaPrefix}.1`;
  }
  // 有beta版本号，betaVersion+1即可
  return `${semver.version}${semver.betaPrefix}${+semver.betaVersion + 1}`;
}

/**
 * 更新npmbeta版本
 *
 * @param {string} packageJsonPath
 */
async function updatePackageVersion(pkOptions: IPackageJsonOptions) {
  const packageJson = pkOptions.content as IPackageJson;
  const localVersion = packageJson.version;

  const match = localVersion.match(/^((@\w+\/)?[^/]*)\/?.*$/);

  if (!match) {
    const choose = await Logger.showErrorMessage(`无法识别版本号: [${localVersion}]`, '提个issue吧~');
    if (choose === '提个issue吧~') submitIssue();
    return false;
  }
  const versions = pkOptions.versions as string[];
  if (!versions || !versions.length || !pkOptions.contentStr) return false;

  // 1. 取npm view xxx versions的最后1个版本
  const remoteVersion = versions.at(-1) as string;

  // 2. 判断即将发布的beta版本号
  let betaVersion = '';
  if (/(alpha|beta)/.test(localVersion) && compareSemverVersion(localVersion, remoteVersion) > 0) {
    // 如果本地版本是beta版本，且大于远程版本，则使用本地版本作为基线版本
    betaVersion = updateBetaVersion(localVersion);
  } else {
    // 否则使用远程版本作为基线版本
    betaVersion = updateBetaVersion(remoteVersion);
  }

  const publishResult = await vscode.window.withProgress(
    {
      cancellable: true,
      title: `正在发布 ${packageJson.name}@${betaVersion}`,
      location: vscode.ProgressLocation.Notification,
    },
    async (_, token) => {
      if (!pkOptions.contentStr) return '';
      token.onCancellationRequested(() => {});
      // 修改版本号到beta版本
      await vscode.workspace.fs.writeFile(
        vscode.Uri.from({ scheme: 'file', path: pkOptions.path }),
        Buffer.from(pkOptions.contentStr.replace(/\"version\":\s*\"[^\"]+\"/, `"version": "${betaVersion}"`))
      );
      try {
        return execSync(
          `cd "${path.dirname(pkOptions.path)}" \n ${pkOptions.npm} publish --tag beta ${pkOptions.registry || ''}`
        );
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        const message = (error?.stderr && error?.stderr.toString()) || error?.message || error?.output?.join('') || '';
        if (/npm ERR\! \d+/.test(message)) {
          const match = message.match(/(npm ERR\! \d+[\s\S]*?$)/);
          Logger.showErrorMessage(`发布失败: ${(match && match[1]) || message}`);
          return '';
        }
        Logger.showErrorMessage(message);
        return '';
      }
    }
  );
  if (!publishResult) return false;
  Logger.showInformationMessage(`发布成功，版本号为 ${packageJson.name}@${betaVersion}`);
  await vscode.workspace.fs.writeFile(
    vscode.Uri.from({ scheme: 'file', path: pkOptions.path }),
    Buffer.from(pkOptions.contentStr.replace(/\"version\":\s*\"[^\"]+\"/, `"version": "${localVersion}"`))
  );
  return true;
}

/**
 * 京喜团队从Legos发布
 *
 * @export
 * @param {string} filePath
 * @returns {string[]}
 */
export async function getLegosPublishBetaCommands(filePath: string): Promise<ICommandQuickPick> {
  const command: ICommandQuickPick = { label: 'legos 发布beta包（京东源）', list: [] };
  const { packageJsonPath } = await readPublishInfos(filePath);
  if (!packageJsonPath) return command;
  const pkOptions = await getPackageJsonOptions({ path: packageJsonPath, npm: 'legos' });
  command.onStart = async () => {
    const result = await checkPublishInfos(pkOptions);
    if (!result) return false;
    return await updatePackageVersion(pkOptions);
  };
  command.list.push({
    workDirectory: './',
    script: `npm info ${pkOptions.content?.name}`,
  });
  return command;
}

/**
 * 发布NPM包到京东源
 *
 * @export
 * @param {string} filePath
 * @returns {string[]}
 */
export async function getJdPublishBetaCommands(filePath: string): Promise<ICommandQuickPick> {
  const command: ICommandQuickPick = { label: 'npm 发布beta包（京东源）', list: [] };
  const { packageJsonPath } = await readPublishInfos(filePath);
  if (!packageJsonPath) return command;
  const pkOptions = await getPackageJsonOptions({
    path: packageJsonPath,
    registry: '--registry=http://registry.m.jd.com',
  });
  command.onStart = async () => {
    const result = await checkPublishInfos(pkOptions);
    if (!result) return false;
    return await updatePackageVersion(pkOptions);
  };
  command.list.push({
    workDirectory: './',
    script: `npm info ${pkOptions.content?.name} ${pkOptions.registry}`,
  });
  return command;
}

/**
 * 发布NPM包，依赖nrm和.npmrc设置
 *
 * @export
 * @param {string} filePath
 * @returns {string[]}
 */
export async function getPublishBetaCommands(filePath: string): Promise<ICommandQuickPick> {
  const command: ICommandQuickPick = { label: '发布NPM包beta版本', list: [] };
  const { packageJsonPath } = await readPublishInfos(filePath);
  if (!packageJsonPath) return command;
  const pkOptions = await getPackageJsonOptions({ path: packageJsonPath });
  command.onStart = async () => {
    const result = await checkPublishInfos(pkOptions);
    if (!result) return false;
    return await updatePackageVersion(pkOptions);
  };
  command.list.push({
    workDirectory: './',
    script: `npm info ${pkOptions.content?.name}`,
  });
  return command;
}
