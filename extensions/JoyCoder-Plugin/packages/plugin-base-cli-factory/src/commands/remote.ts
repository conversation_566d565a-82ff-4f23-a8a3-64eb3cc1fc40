import * as vscode from 'vscode';
import { getRemoteConfigSync, Logger, getCurrentWorkspaceUri } from '@joycoder/shared';
import { ICommandQuickPick } from '../model';
import { formatCommandList } from '../utils';

/**
 * 读取远程配置的命令行
 *
 * @export
 * @param {vscode.Uri} uri
 * @return {*}  {ICommandQuickPick[]}
 */
export function getCustomRemoteCommands(uri: vscode.Uri): ICommandQuickPick[] {
  const { commandConfig = [] } = getRemoteConfigSync() || {};
  const commandInvokePath = getCurrentWorkspaceUri(uri, true).fsPath;
  const start = Date.now();
  const commands = formatCommandList(commandConfig, { commandInvokePath });
  Logger.log(`远程命令解析耗时: ${Date.now() - start}`);
  return commands;
}
