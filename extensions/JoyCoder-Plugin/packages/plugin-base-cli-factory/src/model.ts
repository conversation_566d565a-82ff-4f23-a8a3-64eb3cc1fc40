export interface ICommandItem {
  workDirectory: string;
  script: string;
  timeout?: number;
  customCommand?: string;
}

export enum CommandMode {
  parallel = 'parallel',
  serial = 'serial',
}

export interface ICommand {
  label?: string;
  mode?: CommandMode;
  list: ICommandItem[];
  onStart?: () => Promise<boolean>;
  onEnd?: () => Promise<boolean>;
}

export interface ICommandQuickPick extends ICommand {
  label: string;
}
export interface ICommandRemoteConfig extends ICommandQuickPick {
  commandShowType: 'target' | 'anywhere';
  targetRegexp: string;
  targetEndType: '.git' | 'root';
}
