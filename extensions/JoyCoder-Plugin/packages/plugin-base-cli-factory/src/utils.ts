/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { getProjectRootDirByFilePath, getTargetPath, submitIssue, Logger as logger } from '@joycoder/shared';
import { ICommandItem, ICommandQuickPick, ICommandRemoteConfig } from './model';

const utils = { fs, path, vscode, logger, submitIssue };

interface ICommandPaths {
  commandInvokePath: string;
  targetFilePath?: string;
}

const targetEndGit = (filePath: string) => fs.existsSync(path.join(filePath, '.git'));
const targetEndRoot = (filePath: string) => filePath === '/';

/**
 * 处理配置文件中的workDirectory
 *
 * @param {ICommandRemoteConfig[]} commandList
 * @return {*}  {ICommandQuickPick[]}
 */
export function formatCommandList(
  commandList: ICommandRemoteConfig[],
  { targetFilePath, commandInvokePath }: ICommandPaths
): ICommandQuickPick[] {
  return commandList.filter((command) => {
    // targetFilePath处理
    if (command.commandShowType === 'target') {
      const { targetRegexp, targetEndType } = command;
      const targetRegexpIns = new RegExp(targetRegexp);
      const isEnd = targetEndType === '.git' ? targetEndGit : targetEndRoot;
      targetFilePath = getTargetPath(commandInvokePath, {
        isTarget: (filePath: string) => targetRegexpIns.test(filePath),
        isEnd,
      });
    }
    const list = command.list
      .map((commandItem) => {
        if (commandItem.customCommand) {
          const { workDirectory, script, timeout } = getCustomCommandString(commandItem.customCommand, {
            targetFilePath,
            commandInvokePath,
          }) as ICommandItem;
          Object.assign(commandItem, { workDirectory, script, timeout });
        }
        if (commandItem.workDirectory) {
          commandItem.workDirectory = getCommandCwd(commandItem.workDirectory, commandInvokePath);
        }
        return commandItem;
      })
      .filter((commandItem) => !!commandItem.script);

    if (!list.length) return false;
    return true;
  });
}

/**
 * 根据上下文获取绝对路径
 *
 * @private
 * @param {string} workDirectory
 * @return {*}
 */
function getCommandCwd(workDirectory: string, commandInvokePath: string) {
  if (/^\//.test(workDirectory)) return workDirectory;
  const projectPath = getProjectRootDirByFilePath(commandInvokePath);
  if (/^@\//.test(workDirectory)) {
    return path.join(projectPath, workDirectory.replace(/^@\//, './'));
  }
  return path.join(path.dirname(commandInvokePath), workDirectory);
}

/**
 * 适配commandFunction
 *
 * @export
 * @param {string} script 命令行配置函数
 * @return {*} string
 */
export function getCustomCommandString(
  commandFunction: string,
  domain: Record<string, unknown>
): Partial<ICommandItem> {
  try {
    if (!/function/.test(commandFunction)) return { script: commandFunction };
    const arg = { utils, ...domain };
    const scriptFn = eval(`(${commandFunction})`);
    if (typeof scriptFn !== 'function') return { script: commandFunction };
    return scriptFn(arg);
  } catch (error) {
    logger.showErrorMessage(error as string);
    return { script: '' };
  }
}

/**
 * 获取命令行脚本的函数例子
 *
 * @description getCustomCommandString中eval的函数例子
 *
 * @param {{
 *   utils: IUtils;
 *   commandInvokePath: string;
 *   targetFilePath: string;
 * }} {
 *   utils 工具函数，包含fs、path、vscode、logger、submitIssue等等,
 *   commandInvokePath 触发命令行的绝对路径，优先读取：右键菜单/文件路径 -> 工作区路径,
 *   targetFilePath 展示脚本模式为查找目标配置文件时，目标文件的路径，例如：/Users/<USER>/repos/jxapp/src/pages/jdreserve/shop/index.config.js,
 * }
 * @return {*} string
 */
// @ts-ignore
export function _customCommandFunction({ utils, commandInvokePath, targetFilePath }) {
  const { fs, path, vscode, logger, submitIssue } = utils;

  const match = targetFilePath.match(/^.*?src\/(.*?)\/legos.config.js$/);
  if (!match || ![3]) {
    // @ts-ignore
    logger.showErrorMessage(`无法识别Taro页面路径: [${targetFilePath}]`, '提个issue吧~').then((choose) => {
      if (choose === '提个issue吧~') submitIssue();
    });
    return {};
  }
  return {
    workDirectory: `@/`,
    script: `npm run dev:h5 -- --page ${match[1]}/index --watch`,
    timeout: 0,
  };
}
