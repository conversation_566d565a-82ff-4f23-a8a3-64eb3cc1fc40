import * as vscode from 'vscode';
import { getLegosPublishBetaCommands, getJdPublishBetaCommands } from './commands/beta';
import { getCustomLocalCommands } from './commands/local';
import { getCustomRemoteCommands } from './commands/remote';
import { reportRd, sleep, getVscodeConfig, Logger } from '@joycoder/shared';
import { CommandMode, ICommand, ICommandQuickPick } from './model';

const JOYCODER_TERMINAL_NAME = 'JoyCode';

// 1. 用户单页编译h5后，可能要同时单页编译小程序
// 2. 用户可能在不同页面不同目录多次执行单页编译h5
// 3. 用户可能存在串行和并行两种使用方式

// 综上，比较合理的缓存terminal，避免重复创建的判断，应该是单次执行编译的所有命令集合
class CliFactory {
  // 外部注册vscode命令
  public vscodeCommands: vscode.Disposable[] = [];
  private _isRunning = false;
  // 底部状态栏
  private commandBar: vscode.StatusBarItem;
  // 命令行缓存MAP
  private _terminalMap: Map<string, vscode.Terminal[]> = new Map();
  // 当前命令行
  private _terminal: vscode.Terminal | null = null;
  private _config: Record<string, string | number | boolean> = getVscodeConfig();

  constructor() {
    // 自定义命令行配置(本地)
    this.vscodeCommands.push(
      vscode.commands.registerCommand('JoyCode.cli.custom.command', async (fileUri: vscode.Uri) => {
        const localCommands: ICommandQuickPick[] = getCustomLocalCommands(fileUri);
        if (!localCommands.length) {
          Logger.showWarningMessage(`未发现可执行的命令行: ${fileUri.fsPath}`);
          return;
        }

        vscode.window.showQuickPick<ICommandQuickPick>(localCommands).then(async (item) => {
          if (!item) return;
          // wq.webmonitor.hibox.customCommand.business
          reportRd(17);
          if (item.onStart && (await item.onStart()) === false) return;
          this.executeCommand(item);
          if (item.onEnd && (await item.onEnd()) === false) return;
        });
      })
    );
    // 快捷命令行配置(内建+远程)
    this.vscodeCommands.push(
      vscode.commands.registerCommand('JoyCode.cli.quick.command', async (fileUri: vscode.Uri) => {
        const remoteCommands: ICommandQuickPick[] = getCustomRemoteCommands(fileUri);
        const buildInCommands = [await getJdPublishBetaCommands(fileUri.path)];
        const workspaceId = getVscodeConfig('JoyCode.config.workSpaceId');
        if (workspaceId === 'jxszfe') {
          buildInCommands.push(await getLegosPublishBetaCommands(fileUri.fsPath));
        }
        const commands = [...remoteCommands, ...buildInCommands];

        if (!commands.length) {
          Logger.showWarningMessage(`未发现可执行的命令行: ${fileUri.fsPath}`);
          return;
        }

        vscode.window.showQuickPick<ICommandQuickPick>(commands).then(async (item) => {
          if (!item) return;
          // wq.webmonitor.hibox.customCommand.business
          reportRd(17);
          if (item.onStart && (await item.onStart()) === false) return;
          this.executeCommand(item);
          if (item.onEnd && (await item.onEnd()) === false) return;
        });
      })
    );

    this.vscodeCommands.push(
      vscode.commands.registerCommand('JoyCode.cli.killall', async () => {
        // wq.webmonitor.hibox.killCommand.business
        reportRd(16);
        this.disposeCacheTerminal();
        this.commandBar.hide();
      })
    );

    // 状态栏初始化
    this.commandBar = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 200);
    this.commandBar.command = 'JoyCode.cli.killall';

    // 监听被关闭
    vscode.window.onDidCloseTerminal((terminal) => {
      if (terminal.name === JOYCODER_TERMINAL_NAME) {
        this.onDidCloseTerminal(terminal);
      }
    });
  }

  private onDidCloseTerminal(terminal: vscode.Terminal): void {
    if (this._terminal?.processId === terminal.processId) {
      this._terminal = null;
    }
    this._terminalMap.forEach((terminalList) => {
      terminalList.forEach((item) => {
        if (item.processId === terminal.processId) {
          terminalList.splice(terminalList.indexOf(item), 1);
        }
      });
    });
  }

  private stopRunning() {
    if (this._isRunning) {
      this._isRunning = false;
      vscode.commands.executeCommand('workbench.action.terminal.killall');
    }
  }

  /**
   * 关闭缓存终端
   *
   * @private
   * @param {string} [key]
   * @return {*}
   * @memberof CliFactory
   */
  private disposeCacheTerminal(key?: string) {
    if (!key) {
      this._terminalMap.forEach((terminalList) => {
        terminalList.forEach((item) => {
          item.dispose();
        });
      });
      this._terminalMap.clear();
      this._terminal = null;
      return;
    }
    const terminalList = this._terminalMap.get(key) || [];
    terminalList.forEach((item) => {
      if (item.processId === this._terminal?.processId) {
        this._terminal = null;
      }
      item.dispose();
    });
  }

  /**
   * 执行命令
   *
   * @private
   * @param {ICommand} command
   * @memberof CliFactory
   */
  private async executeCommand(command: ICommand) {
    if (!command.list.length) return;
    this._isRunning = true;
    // 告知用户开始执行了
    vscode.window.withProgress(
      {
        cancellable: true,
        title: '开始执行命令...',
        location: vscode.ProgressLocation.Notification,
      },
      async (_, token) => {
        token.onCancellationRequested(() => {
          this.stopRunning();
        });
        return new Promise((resolve) => setTimeout(resolve, 2000));
      }
    );
    const commandKey = JSON.stringify(command);
    // 尝试关闭之前执行同样命令的终端
    this.disposeCacheTerminal(commandKey);
    // 重置当前terminal为null
    this._terminal = null;
    for (const commandItem of command.list) {
      if (commandItem.timeout && commandItem.timeout > 0) {
        await sleep(commandItem.timeout * 1000);
      }
      const terminal = this.getTerminal(commandKey, command);
      await this.executeCommandInTerminal(terminal, [`cd "${commandItem.workDirectory}"`, commandItem.script]);
    }
    this.commandBar.text = 'JoyCoder命令行运行中...';
    this.commandBar.show();
  }

  /**
   * 获取终端
   *
   * @private
   * @param {string} key
   * @param {ICommand} command
   * @return {*}  {vscode.Terminal}
   */
  private getTerminal(key: string, command: ICommand): vscode.Terminal {
    const terminalList = this._terminalMap.get(key) || [];
    let terminal: vscode.Terminal | null = null;
    if (command.mode === CommandMode.parallel) {
      // 并行每次都重新创建
      this._terminal = terminal = vscode.window.createTerminal(JOYCODER_TERMINAL_NAME);
      terminalList.push(terminal);
    }
    if (!command.mode || command.mode === CommandMode.serial) {
      // 默认串行，采用上次缓存的this._terminal
      if (this._terminal) {
        terminal = this._terminal;
      } else {
        terminal = vscode.window.createTerminal(JOYCODER_TERMINAL_NAME);
        this._terminal = terminal;
        terminalList.push(terminal);
      }
    }
    this._terminalMap.set(key, terminalList);
    return terminal as vscode.Terminal;
  }

  /**
   * 在终端执行命令
   *
   * @private
   * @param {vscode.Terminal} terminal
   * @param {string[]} commands
   * @memberof CliFactory
   */
  private async executeCommandInTerminal(terminal: vscode.Terminal, commands: string[]) {
    terminal.show();
    if (this?._config['clearPreviousOutput']) {
      await vscode.commands.executeCommand('workbench.action.terminal.clear');
    }
    commands.forEach((command) => {
      terminal?.sendText(command);
    });
  }
}

export default function (context: vscode.ExtensionContext) {
  const cliFactory = new CliFactory();
  context.subscriptions.push(...cliFactory.vscodeCommands);
}
