// 库
import * as vscode from 'vscode';
import * as playwright from 'playwright-core';
import EventEmitterEnhancer, { EnhancedEventEmitter } from 'event-emitter-enhancer';
// 业务库
import { Clipboard } from '@joycoder/shared';
import CommonTool from '../shared/utils/CommonTool';
import WebviewPanelManager from '../webview-panel/WebviewPanelManager';
// 业务定义
import { IMessageRequestData, IMessageResponseData } from '../shared/interface';

declare global {
  interface Window {
    JoyCoderEnableCopyPaste: any;
    JoyCoderEmitCopy: any;
    JoyCoderGetPaste: any;
  }
}

/**
 * 无头浏览器内页面对象管理类
 */
export default class BrowserPage extends EnhancedEventEmitter {
  // 这里是用来操作无头浏览器的
  // CDP相关协议：https://chromedevtools.github.io/devtools-protocol/tot/Page/
  private client: playwright.CDPSession | null = null;
  private clipboard: Clipboard;
  private pageId: string;

  public readonly playwrightPage: playwright.Page;
  public getPageId() {
    return this.pageId;
  }

  constructor(playwrightPage: playwright.Page) {
    super();
    this.playwrightPage = playwrightPage;
    this.clipboard = new Clipboard();
  }

  public async launch() {
    // 在页面window对象上挂载复制粘贴相关的方法，用于和node端通信
    await Promise.allSettled([
      this.playwrightPage.exposeFunction('JoyCoderEnableCopyPaste', () => true),
      this.playwrightPage.exposeFunction('JoyCoderEmitCopy', (text: string) => this.clipboard.writeText(text)),
      this.playwrightPage.exposeFunction('JoyCoderGetPaste', () => this.clipboard.readText()),
    ]);

    // 给页面注入js代码，无法debugger，可通过alert调试
    this.playwrightPage.addInitScript(async () => {
      localStorage.setItem('screencastEnabled', 'false');
      localStorage.setItem('panel-selectedTab', 'console');

      // 复制粘贴能力
      if (await window.JoyCoderEnableCopyPaste?.()) {
        const copyHandler = (event: ClipboardEvent) => {
          const text = event.clipboardData?.getData('text/plain') || document.getSelection()?.toString();
          text && window.JoyCoderEmitCopy?.(text);
        };
        document.addEventListener('copy', copyHandler);
        document.addEventListener('cut', copyHandler);
        document.addEventListener('paste', async (event) => {
          event.preventDefault();
          const text = await window.JoyCoderGetPaste?.();
          text && document.execCommand('insertText', false, text);
        });
      }
    });

    // 设置主题
    await this.playwrightPage.emulateMedia({
      colorScheme: CommonTool.isDarkTheme() ? 'dark' : 'light',
    });
    // 设置语言
    await this.playwrightPage.setExtraHTTPHeaders({
      'Accept-Language': 'zh',
    });
    await this.playwrightPage.addInitScript(() => {
      Object.defineProperty(navigator, 'language', {
        get: function () {
          return 'zh-CN';
        },
      });
      Object.defineProperty(navigator, 'languages', {
        get: function () {
          return ['zh-CN', 'zh'];
        },
      });
    });
    // 获取 cdp 连接对象（核心是用来操无头浏览器的）
    this.client = await this.playwrightPage.context().newCDPSession(this.playwrightPage);
    // @ts-expect-error
    EventEmitterEnhancer.modifyInstance(this.client);
    this.pageId = await (await this.client.send('Target.getTargetInfo')).targetInfo.targetId;
    // 设置下载行为和路径为当前脚本所在目录
    this.client.send('Page.setDownloadBehavior', {
      behavior: 'allow', // 允许所有下载请求
      downloadPath: vscode.workspace.workspaceFolders?.[0]?.uri?.path, // 设置下载路径为当前目录
    });
    // @ts-expect-error
    this.client.else((event: string, data: Record<string, string>) => {
      // 如果没有事件标识则直接不处理
      if (typeof event === 'symbol') {
        return;
      }
      // 特殊处理 windowOpen 消息
      if (event === 'Page.windowOpen') {
        WebviewPanelManager.instance.create(data.url);
        return;
      }
      // 特殊处理原生弹框
      if (event === 'Page.javascriptDialogOpening') {
        const { type, message, defaultPrompt } = data;
        // 根据类型处理不同的弹框逻辑
        if (type === 'alert') {
          vscode.window.showInformationMessage(message);
          // 向 cdp 进程发送消息
          (this.client as playwright.CDPSession).send('Page.handleJavaScriptDialog', {
            accept: true,
          });
        } else if (type === 'prompt') {
          vscode.window
            .showInputBox({
              value: defaultPrompt || '',
              placeHolder: message,
            })
            .then((result) => {
              // 向 cdp 进程发送消息
              (this.client as playwright.CDPSession).send('Page.handleJavaScriptDialog', {
                accept: true,
                promptText: result,
              });
            });
        } else if (type === 'confirm') {
          vscode.window.showQuickPick(['确定', '取消']).then((result) => {
            // 向 cdp 进程发送消息
            (this.client as playwright.CDPSession).send('Page.handleJavaScriptDialog', {
              accept: result === '确定',
            });
          });
        }
        return;
      }
      const type = `cdp.${event}`;
      // 向WebviewPanel中的browserPage对象传递消息
      this.emit(type, {
        type,
        data,
      } as IMessageResponseData);
    });
  }

  /**
   * 处理 HTML 传递过来的消息转发给 CDP 进程
   */
  public async send(message: IMessageRequestData) {
    // 获取消息类型
    switch (message.type) {
      case 'client.goForward': {
        await this.playwrightPage.goForward();
        break;
      }
      case 'client.goBackward': {
        await this.playwrightPage.goBack();
        break;
      }
      // 向页面注入样式
      case 'client.insertStyle': {
        await this.playwrightPage.addStyleTag({ content: 'a, img {user-drag: none;-webkit-user-drag: none;}' });
        break;
      }
      default: {
        // 移除消息标识前缀
        const method = message.type.slice('client.'.length);
        // 获取数据和回调ID
        const { params, callbackId } = message;
        // 获取回调消息的标识
        const type = `cdp.callback.${method}`;
        // 向 cdp 发送消息
        (this.client as playwright.CDPSession)
          .send(method as any, params)
          .then((result: unknown) => {
            // 向WebviewPanel中的browserPage对象传递消息
            this.emit(type, {
              type,
              data: result,
              callbackId,
            } as IMessageResponseData);
          })
          .catch((error) => {
            // 向WebviewPanel中的browserPage对象传递消息
            this.emit(type, {
              type,
              callbackId,
              error: error.message,
            } as IMessageResponseData);
          });
      }
    }
  }

  /**
   * 销毁处理函数
   */
  public async dispose() {
    this.removeAllElseListeners();
    // @ts-ignore
    this.removeAllListeners();
    (this.client as playwright.CDPSession)?.detach();
    const context = this.playwrightPage.context();
    await Promise.allSettled([
      context.exposeFunction('JoyCoderEnableCopyPaste', () => {}),
      context.exposeFunction('JoyCoderEmitCopy', () => {}),
      context.exposeFunction('JoyCoderGetPaste', () => {}),
    ]);
    this.playwrightPage?.close();
  }
}
