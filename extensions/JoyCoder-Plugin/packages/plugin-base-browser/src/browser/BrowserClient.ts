// 库
import edge from '@chiragrupani/karma-chromium-edge-launcher';
import chrome from 'karma-chrome-launcher';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import * as playwright from 'playwright-core';
// 业务库
import { GlobalState } from '@joycoder/shared';
import BrowserPage from './BrowserPage';
import WebviewPanelManager from '../webview-panel/WebviewPanelManager';

export default class BrowserClient {
  // headless浏览器对象
  private _browser: playwright.BrowserContext | null = null;

  /**
   * 创建新的无核浏览器页面对象
   */
  public async createBrowserPage(): Promise<BrowserPage> {
    // 如果没有浏览器对象则新建一个
    if (!this._browser) {
      await this.launchBrowser();
    }
    // 新建页面（不能复用已有页面，因为存在多tab的情况，比如点击target="_blank"的链接）
    const playwrightPage = await (this._browser as playwright.BrowserContext).newPage();
    const page = new BrowserPage(playwrightPage);
    // 打开页面
    await page.launch();
    // 返回创建好的页面对象
    return page;
  }

  private async launchBrowser() {
    const chromeArgs: string[] = [];
    chromeArgs.push('--allow-file-access-from-files');
    chromeArgs.push('--remote-allow-origins=*');
    chromeArgs.push('--disable-gpu');
    chromeArgs.push(
      '--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
    );
    // 设置远程调试端口
    chromeArgs.push(`--remote-debugging-port=9222`);
    // 设置默认下载路径
    // chromeArgs.push(`--default-download-directory=${__dirname}`);
    // 设置代理
    const { proxy } = GlobalState.get('JoyCoderBrowseLiteConfig') || {};
    if (proxy) chromeArgs.push(`--proxy-server=${proxy}`);
    // 获取无核浏览器的本地路径
    const chromePath = BrowserClient.getChromiumPath();
    // 没有找到可用的浏览器执行路径
    if (!chromePath) {
      vscode.window.showErrorMessage('未安装chrome浏览器，或者未设置自定义浏览器路径');
    }
    // 如果是linux浏览器则追加额外参数
    if (os.platform() === 'linux') {
      chromeArgs.push('--no-sandbox');
    }
    // 获取插件上下文对象
    const { context } = WebviewPanelManager.instance;
    // https://chromium.googlesource.com/chromium/src/+/refs/heads/main/docs/user_data_dir.md
    const userDataDir = path.join(context.globalStorageUri.fsPath, 'playwrightUserData');
    // 直接删除加锁文件，避免浏览器唤起失败
    try {
      // TODO: 这里是替身文件，可以再看下怎么判断替身文件是否存在
      fs.unlinkSync(path.join(userDataDir, 'SingletonLock'));
    } catch {
      // do nothing
    }
    // 启动无核浏览器
    await playwright.chromium
      .launchPersistentContext(userDataDir, {
        args: [...chromeArgs, '--lang=zh-CN,zh'],
        // TODO: 新的无头模式存在问题（动态更新viewport后screencast会出现尺寸异常问题），因此先屏蔽
        // headless: 'new', // 这个是官方新推荐增加的参数（https://developer.chrome.com/articles/new-headless/）
        // 这个设置项有可能导致page.goto变慢至4-5秒
        executablePath: chromePath,
        ignoreDefaultArgs: ['--mute-audio'],
      })
      .then((context) => {
        this._browser = context;
      });
  }

  /**
   * 销毁无头浏览器
   */
  public dispose(): Promise<void> {
    return new Promise((resolve) => {
      // 执行销毁逻辑
      if (this._browser) {
        this._browser.close();
        this._browser = null;
      }
      resolve();
    });
  }
  /**
   * 获取用户客户端安装的chrome路径
   */
  public static getChromiumPath(): string | undefined {
    const knownChromiums = [...Object.entries(chrome), ...Object.entries(edge)];
    // 遍历获取本地可执行的chrome路径
    for (let i = 0; i < knownChromiums.length; i += 1) {
      const [key, info] = knownChromiums[i];
      if (key.startsWith('launcher')) {
        const chromiumPath = (info as any)?.[1]?.prototype?.DEFAULT_CMD?.[process.platform];
        if (chromiumPath && typeof chromiumPath === 'string' && fs.existsSync(chromiumPath)) {
          return chromiumPath;
        }
      }
    }
    return undefined;
  }
}
