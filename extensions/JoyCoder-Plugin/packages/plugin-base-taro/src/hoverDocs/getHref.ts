import axios from 'axios';
import to from 'await-to-js';
import { camelToDashCase } from '@joycoder/shared';

export enum CodeType {
  Components,
  API,
}

let _TaroSideBar = null;

async function getSideBarData() {
  if (_TaroSideBar) return _TaroSideBar;

  const [err, response] = await to(
    axios.get('https://raw.githubusercontent.com/NervJS/taro-docs/master/versioned_sidebars/version-3.x-sidebars.json')
  );
  if (err) return '';

  _TaroSideBar = response.data;
  return _TaroSideBar;
}

function matchStringByWordEnding(str, keyWord) {
  const regex = new RegExp(`"([^"]*\/${keyWord})"`, 'i');
  const matches = str.match(regex);
  return matches?.[1] || '';
}

/**
 * 通过关键字匹配出taro文档路径
 * @param type: CodeType
 * @param keyWord: string
 * @returns string
 */
export default async function getHref(type: CodeType, keyWord) {
  try {
    const data: any = await getSideBarData();
    const { components, API } = data || {};
    const jsonStr = JSON.stringify(type == CodeType.Components ? components : API);
    // taro文档中jsx的链接是中划线模式
    const word = type == CodeType.Components ? camelToDashCase(keyWord) : keyWord;
    return matchStringByWordEnding(jsonStr, word);
  } catch (error) {
    return '';
  }
}
