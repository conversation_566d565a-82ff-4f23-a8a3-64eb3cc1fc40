import * as vscode from 'vscode';
import * as jsonfile from 'jsonfile';
import * as path from 'path';
import { FE_FILE_TYPES, getFocusCodeInfo, reportRd } from '@joycoder/shared';
import getHref, { CodeType } from './getHref';
import to from 'await-to-js';

function getHover(url = 'https://docs.taro.zone/search?q=', word) {
  const mdStr = `[查看官方文档](command:JoyCode.browser.open?"${url}${word}")`;
  const md = new vscode.MarkdownString(mdStr);
  md.isTrusted = true;
  const hover = new vscode.Hover(md);

  reportRd(4);
  return hover;
}

/**
 * 悬浮Taro组件、Hooks、API时悬浮提示Taro文档
 */
async function provideHover(document, position) {
  const { line, word } = getFocusCodeInfo(document, position);
  const lineText = line.text || '';

  const hooksPattern = /^use\w+/i;
  if (hooksPattern.test(word)) {
    // taro hooks
    const snippetsObj = jsonfile.readFileSync(path.join(__dirname, '../snippets/src/js/taro/hooks.json'));
    if (snippetsObj[word]) return getHover('https://docs.taro.zone/docs/hooks%23', word.toLowerCase());

    // react hooks
    const reactHooksObj = jsonfile.readFileSync(path.join(__dirname, '../snippets/src/js/react/hooks.json'));
    if (reactHooksObj[word]) return getHover('https://zh-hans.react.dev/reference/react/', word);
  }

  // taro jsx
  const jsxStartPattern = new RegExp(`<${word}`);
  const jsxEndPattern = new RegExp(`<\\/${word}`);
  if (jsxStartPattern.test(lineText) || jsxEndPattern.test(lineText)) {
    const [err, href] = await to(getHref(CodeType.Components, word));
    if (err) return;

    if (href) return getHover('https://docs.taro.zone/docs/', href);
  }

  // taro api
  const apiStr = `Taro.${word}`;
  const apiPattern = new RegExp(apiStr, 'i');
  if (apiPattern.test(lineText)) {
    const [err, href] = await to(getHref(CodeType.API, word));
    if (err) return;

    if (href) {
      return getHover('https://docs.taro.zone/docs/', href);
    } else {
      return getHover(undefined, word);
    }
  }
}

export function initHoverDocs(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.languages.registerHoverProvider(FE_FILE_TYPES, {
      provideHover: provideHover,
    })
  );
}
