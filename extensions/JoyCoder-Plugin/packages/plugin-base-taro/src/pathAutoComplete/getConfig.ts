import * as fs from 'fs';
import * as parser from '@babel/parser';
import traverse from '@babel/traverse';

/**
 * 获取Taro项目app.config中配置的页面链接
 * @param appConfigPath
 * @returns
 */
export function getPages(appConfigPath: string) {
  let pages = [];
  let subPackages = [];

  try {
    const str = fs.readFileSync(appConfigPath, 'utf-8');
    const ast = parser.parse(str, {
      sourceType: 'module',
    });
    traverse(ast, {
      ObjectProperty(path) {
        const { node }: { node: any } = path;
        if (node.key.name === 'pages') {
          pages = node.value.elements.map((element) => element.value);
        } else if (node.key.name === 'subPackages') {
          subPackages = node.value.elements.map((element) => ({
            root: element.properties[0].value.value,
            pages: element.properties[1].value.elements.map((element) => element.value),
          }));
          path.stop();
        }
      },
    });
    return [...pages, ...subPackages2Paths(subPackages)];
  } catch (error) {
    console.error(error);
  }
  return [];
}

/**
 * subPackages转为pages/xxx/xxx
 * @param subPackages
 * @returns []
 */
function subPackages2Paths(subPackages = []) {
  const paths: any[] = [];

  subPackages.forEach((item: any) => {
    const { root, pages } = item;

    pages.forEach((page) => {
      paths.push(`${root}${page}`);
    });
  });

  return paths;
}
