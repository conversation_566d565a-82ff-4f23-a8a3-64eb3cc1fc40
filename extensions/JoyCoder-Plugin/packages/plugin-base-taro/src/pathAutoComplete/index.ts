import { FE_FILE_TYPES, getProjectRootDirByFilePath } from '@joycoder/shared';
import path from 'path';
import * as fse from 'fs-extra';
import * as vscode from 'vscode';
import { getPages } from './getConfig';

async function provideCompletionItems(
  document: vscode.TextDocument,
  position: vscode.Position
): Promise<vscode.CompletionItem[]> {
  const items: vscode.CompletionItem[] = [];

  const inputTextReg = /\/?pages\//g;
  const line = document.lineAt(position);
  // 当前行光标之前的字符
  const linePrefix = line.text.slice(0, position.character);

  const matches = linePrefix.match(inputTextReg);
  if (!matches) return [];

  const file = document.fileName;
  const projectRootPath = getProjectRootDirByFilePath(file);
  const extnameList = ['.js', '.ts'];
  const entryFileExt = extnameList.find((extname) => {
    return fse.pathExistsSync(path.join(projectRootPath, 'src/app.config') + extname);
  });

  const appConfigPath = path.join(projectRootPath, 'src/app.config') + entryFileExt;
  // 输入第二段路径并输入/时重新匹配过滤结果
  const inputUrl = linePrefix.match(/pages\/[-A-Za-z0-9+\/_]+[-A-Za-z0-9]/g)?.[0] || '';
  const pageList = getPages(appConfigPath).filter((item) => item.includes(inputUrl));

  pageList.map((url) => {
    const completionItem = new vscode.CompletionItem(url, vscode.CompletionItemKind.Variable);

    completionItem.detail = 'JoyCode';
    // 替换掉用户输入的字符
    completionItem.range = new vscode.Range(
      line.lineNumber,
      linePrefix.indexOf(matches[0]) + (matches[0].startsWith('/pages') ? 1 : 0),
      line.lineNumber,
      linePrefix.length
    );
    completionItem.insertText = new vscode.SnippetString(url);

    completionItem.command = {
      title: 'reportRd',
      command: 'JoyCode.reportRd',
      arguments: [43],
    };
    items.push(completionItem);
  });

  return items;
}

export const pathAutoComplete = (context: vscode.ExtensionContext) => {
  context.subscriptions.push(
    vscode.languages.registerCompletionItemProvider(
      FE_FILE_TYPES,
      {
        provideCompletionItems,
      },
      '/'
    )
  );
};
