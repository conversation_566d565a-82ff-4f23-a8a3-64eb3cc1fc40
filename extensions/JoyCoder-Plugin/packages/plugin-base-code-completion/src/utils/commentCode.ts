interface CommentSignal {
  blockLeft: string | null;
  blockRight: string | null;
  line: string | null;
}
const comment = '<|comment|>';
export function commentCode(input, lang, mode) {
  if (input.trim() === '') {
    return input;
  }
  const commentSignal: CommentSignal | null = getCommentSignal(lang);
  if (mode === 'block' && commentSignal.blockLeft && commentSignal.blockRight) {
    return commentSignal.blockLeft + input + commentSignal.blockRight;
  }
  if (mode === 'line' && commentSignal.line) {
    return comment + input.replaceAll('\n', '\n' + comment);
  }
  if (commentSignal.blockLeft && commentSignal.blockRight) {
    return commentSignal.blockLeft + input + commentSignal.blockRight;
  }
  if (commentSignal.line) {
    return comment + input.replaceAll('\n', '\n' + comment);
  }
  return input;
}
export function getCommentSignal(lang) {
  let commentSignal: CommentSignal | null = null;
  switch (lang) {
    case 'javascript':
    case 'javascriptreact':
    case 'typescriptreact':
    case 'typescript':
    case 'java':
    case 'c':
    case 'csharp':
    case 'cpp':
    case 'cuda-cpp':
    case 'objective-c':
    case 'objective-cpp':
    case 'rust':
    case 'go':
    case 'dart':
    case 'groovy':
    case 'json':
    case 'swift':
    case 'kotlin':
      commentSignal = {
        blockLeft: '/*',
        blockRight: '*/',
        line: '//',
      };
      break;
    case 'css':
    case 'less':
    case 'sass':
    case 'scss':
      commentSignal = {
        blockLeft: '/*',
        blockRight: '*/',
        line: null,
      };
      break;
    case 'python':
      commentSignal = {
        blockLeft: '"""',
        blockRight: '"""',
        line: '#',
      };
      break;
    case 'tex':
    case 'latex':
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: '%',
      };
      break;
    case 'shellscript':
    case 'r':
    case 'compose':
    case 'docker':
    case 'makefile':
    case 'perl':
    case 'properties':
    case 'yaml':
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: '#',
      };
      break;
    case 'sql':
      commentSignal = {
        blockLeft: '/*',
        blockRight: '*/',
        line: '--',
      };
      break;
    case 'html':
    case 'php':
    case 'razor':
    case 'xsl':
    case 'xml':
    case 'svg':
      commentSignal = {
        blockLeft: '<!--',
        blockRight: '-->',
        line: null,
      };
      break;
    case 'coffeescript':
      commentSignal = {
        blockLeft: '###',
        blockRight: '###',
        line: '#',
      };
      break;
    case 'batch':
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: '@REM ',
      };
      break;
    case 'clojure':
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: ';;',
      };
      break;
    case 'fsharp':
      commentSignal = {
        blockLeft: '(*',
        blockRight: '*)',
        line: '//',
      };
      break;
    case 'handlebars':
      commentSignal = {
        blockLeft: '{{!--',
        blockRight: '--}}',
        line: null,
      };
      break;
    case 'julia':
      commentSignal = {
        blockLeft: '#=',
        blockRight: '=#',
        line: '#',
      };
      break;
    case 'lua':
      commentSignal = {
        blockLeft: '--[[',
        blockRight: ']]',
        line: '--',
      };
      break;
    case 'powershell':
      commentSignal = {
        blockLeft: '<#',
        blockRight: '#>',
        line: '#',
      };
      break;
    case 'jade':
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: '//-',
      };
      break;
    case 'ruby':
      commentSignal = {
        blockLeft: '=begin',
        blockRight: '=end',
        line: '#',
      };
      break;
    default:
      commentSignal = {
        blockLeft: null,
        blockRight: null,
        line: null,
      };
  }
  return commentSignal;
}
export function isComment(str, lang) {
  const signal = getCommentSignal(lang);
  const strArr = str.split('\n');
  const lastLine = strArr.at(-1) || '';
  if (signal.blockLeft === null && signal.blockRight === null) {
    if (!signal.line) return { isComment: false, signal: null };
    if (lastLine.includes(signal.line)) {
      return { isComment: true, signal: signal.line };
    }
    return { isComment: false, signal: null };
  } else {
    if (lang === 'python') {
      if (str.indexOf("'''") === -1 && str.indexOf('"""') === -1) {
        if (lastLine.includes('#')) return { isComment: true, signal: '#' };
        return { isComment: false, signal: null };
      }
      let i = 0;
      while (i < str.length) {
        let commentBlock = '';
        const indexS = str.substring(i).indexOf("'''");
        const indexD = str.substring(i).indexOf('"""');
        if (indexS === -1 && indexD === -1) break;
        let indexL = 0;
        if (indexS === -1) {
          commentBlock = '"""';
          indexL = indexD;
        } else {
          if (indexD === -1) {
            commentBlock = "'''";
            indexL = indexS;
          } else {
            if (indexS < indexD) {
              commentBlock = "'''";
              indexL = indexS;
            } else {
              commentBlock = '"""';
              indexL = indexD;
            }
          }
        }
        const indexR = str.substring(i + indexL + 3).indexOf(commentBlock);
        if (indexR === -1) return { isComment: true, signal: commentBlock };
        i += indexL + 3 + indexR + 3;
      }
      if (str.substring(i).includes('#') && lastLine.includes('#')) return { isComment: true, signal: '#' };
      return { isComment: false, signal: null };
    }
    if (!str.includes(signal.blockLeft)) {
      if (signal.line && lastLine.includes(signal.line)) return { isComment: true, signal: signal.line };
      return { isComment: false, signal: null };
    } else {
      let i = 0;
      while (i < str.length) {
        const index = str.substring(i).indexOf(signal.blockLeft);
        if (index === -1) break;
        const indexR = str.substring(i + index + signal?.blockLeft?.length).indexOf(signal.blockRight);
        if (indexR === -1) return { isComment: true, signal: signal.blockLeft };
        i += index + signal?.blockLeft?.length + indexR + signal?.blockRight?.length;
      }
      if (signal.line && str.substring(i).includes(signal.line) && lastLine.includes(signal.line))
        return { isComment: true, signal: signal.line };
      return { isComment: false, signal: null };
    }
  }
}
