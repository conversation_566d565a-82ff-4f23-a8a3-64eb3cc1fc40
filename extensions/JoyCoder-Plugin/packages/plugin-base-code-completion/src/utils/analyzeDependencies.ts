import * as vscode from 'vscode';
import natural from 'natural';
import * as path from 'path';
import * as fs from 'fs/promises';
import { comment, parseTreeSitter, queryExports, queryFunctions } from './elidableText';
import { PathLike } from 'fs';

const localImportRegex = /^\s*import\s*(type|)\s*\{[^}]*\}\s*from\s*(['"](?:@\/|\.\/)[^'"]+['"])/gm;

interface ImportName {
  name: string;
  alias?: string;
}

/**
 * 分析代码文件中的依赖项。
 * @param {Document} doc - 包含代码的文档对象。
 * @param {string} folderPath - 文件夹路径。
 * @returns {Promise<Array>} 如果是TypeScript或TypeScriptReact文件，则返回本地导入的上下文；否则返回空数组。
 */
export default async function analyzeDependencies(doc, folderPath: string) {
  const { languageId } = doc;
  const source = doc.getText();
  const uri = vscode.Uri.parse(doc.uri);
  return (languageId === 'typescript' || 'typescriptreact') && uri.scheme === 'file'
    ? await extractTypeScriptLocalImportContext(languageId, source, uri, folderPath)
    : [];
}

export async function extractTypeScriptLocalImportContext(
  languageId: string,
  source: string,
  uri: vscode.Uri,
  folderPath: string
): Promise<{ fileName: string; fileContent: string[] }[]> {
  const localImportContext: any = [];
  const lastImportOffset = await getLastTypeScriptLocalImportOffset(source);
  if (languageId === 'typescriptreact') {
    languageId = 'typescript';
  }
  if (lastImportOffset === -1) return localImportContext;
  source = source.substring(0, lastImportOffset);
  const tree = await parseTreeSitter(languageId, source);
  try {
    for (const imp of getTypeScriptImports(tree.rootNode)) {
      const srcUri = (await resolveLocalTypeScriptImport(uri.fsPath, imp, folderPath)) || '';
      if (!srcUri) continue;
      const importedNames = getTypescriptImportedNames(imp);
      if (importedNames.length === 0) continue;
      const exports = await getExportedDeclarations(srcUri, languageId);
      const fileName = comment(srcUri.replace(folderPath, ''), languageId) + '\n';
      // localImportContext.push(fileName);
      const impText: string[] = [];
      for (const importedName of importedNames) {
        if (exports.has(importedName.name)) {
          impText.push(...exports.get(importedName.name));
        }
      }
      localImportContext.push({ fileName, fileContent: impText.join('\n') });
      // localImportContext.push('\n');
    }
  } finally {
    tree.delete();
  }
  return localImportContext;
}

/**
 * 获取TypeScript源码中最后一个本地import语句之后的偏移量。
 * @param source TypeScript源码字符串。
 * @returns 返回最后一个本地import语句之后的字符偏移量。如果没有import语句，返回-1。
 */
async function getLastTypeScriptLocalImportOffset(source: string): Promise<number> {
  let lastImport = -1;
  localImportRegex.lastIndex = 0;
  let match: any = null;
  do {
    match = localImportRegex.exec(source);
    if (match) {
      lastImport = localImportRegex.lastIndex;
    }
  } while (match);
  if (lastImport === -1) return -1;
  const newlineAfterLastImport = source.indexOf('\n', lastImport);
  return newlineAfterLastImport !== -1 ? newlineAfterLastImport : source.length;
}

function getTypeScriptImports(root: any): any[] {
  const imports: any = [];
  for (const toplevelStmt of root.namedChildren) {
    if (toplevelStmt.type === 'import_statement') {
      imports.push(toplevelStmt);
    }
  }
  return imports;
}

async function checkFileExists(filePath: PathLike) {
  try {
    const stats = await fs.stat(filePath);
    return stats.isFile();
  } catch {
    return false;
  }
}

/**
 * 解析本地TypeScript导入路径。 可能不包含后缀
 * @param importerPath 导入者文件路径
 * @param imp 导入语句节点
 * @param folderPath 基础文件夹路径
 * @returns 完整的导入路径或null
 */
async function resolveLocalTypeScriptImport(
  importerPath: string,
  imp: any,
  folderPath: string
): Promise<string | null> {
  const src = imp.namedChild(1)?.text.slice(1, -1);
  let fullPath = '';
  if (!src || (!src.startsWith('.') && !src.startsWith('@/'))) return null;

  // 检查导入路径是否为TypeScript文件
  if (path.extname(src) !== '' && path.extname(src) !== '.ts' && path.extname(src) !== '.tsx') {
    return null;
  }

  if (src.startsWith('.')) {
    fullPath = path.join(path.dirname(importerPath), src);
  } else if (src.startsWith('@/')) {
    // 处理 @/ 情况
    fullPath = path.join(folderPath, 'src', src.slice(2));
  }

  // 补充文件后缀
  if (path.extname(fullPath) === '') {
    const isTs = await checkFileExists(fullPath + '.ts');
    const isIndexTs = await checkFileExists(path.join(fullPath, 'index.ts'));
    const isTsx = await checkFileExists(fullPath + '.tsx');
    const isIndexTsx = await checkFileExists(path.join(fullPath, 'index.tsx'));
    if (isTs) {
      fullPath = fullPath + '.ts';
    } else if (isIndexTs) {
      fullPath = path.join(fullPath, 'index.ts');
    } else if (isTsx) {
      fullPath = fullPath + '.tsx';
    } else if (isIndexTsx) {
      fullPath = path.join(fullPath, 'index.tsx');
    } else {
      return null;
    }
  }
  return fullPath;
}

function getTypescriptImportedNames(imp: any): ImportName[] {
  // 直接通过链式调用尝试获取到 namedImports
  const namedImports = imp.namedChild(0)?.namedChild(0);

  // 如果当前节点不是 named_imports，直接返回空数组
  if (namedImports?.type !== 'named_imports') {
    return [];
  }

  // 使用 map 和 filter 组合来简化循环和条件判断
  return namedImports.namedChildren
    .filter((namedImport: { type: string }) => namedImport.type === 'import_specifier')
    .map((namedImport: { childForFieldName: (arg0: string) => { (): any; new (): any; text: any } }) => {
      const name = namedImport.childForFieldName('name')?.text;
      const alias = namedImport.childForFieldName('alias')?.text;
      return { name, alias };
    })
    .filter((importName: { name: any }) => importName.name); // 确保 name 存在
}

// 假设有一个全局的exportsCache变量和相关的高水位标记常量
const exportsCache = new Map<string, any>();
const EXPORTS_CACHE_HIGH_WATER_MARK = 100;
const EXPORTS_CACHE_LOW_WATER_MARK = 50;

function getDocComment(srcString: string, node: { startIndex: any }) {
  const docCommentNode = getFirstPrecedingComment(node);
  return docCommentNode ? srcString.substring(docCommentNode.startIndex, node.startIndex) : '';
}

function getFirstPrecedingComment(nd: any) {
  let cur = nd;
  for (; cur.previousSibling?.type === 'comment'; ) {
    const prev = cur.previousSibling;
    if (prev.endPosition.row < cur.startPosition.row - 1) break;
    cur = prev;
  }
  return cur?.type === 'comment' ? cur : null;
}

async function getExportedDeclarations(uri: string, lang: string): Promise<Map<string, any>> {
  const exports = new Map<string, any>();

  let mtime = -1;
  try {
    mtime = (await fs.stat(uri)).mtime.getTime();
  } catch {
    return exports;
  }

  const entry = exportsCache.get(uri);
  if (entry && entry.mtime === mtime) return entry.exports;

  if (lang === 'typescript') {
    let tree: any = null;
    try {
      const srcString = (await fs.readFile(uri)).toString();
      tree = await parseTreeSitter(lang, srcString);
      for (const em of queryExports(lang, tree.rootNode))
        for (const ec of em.captures) {
          const exp = ec.node;
          if (exp.type === 'export_statement') {
            const decl = exp.childForFieldName('declaration');
            if (decl?.hasError()) continue;
            //eslint-disable-next-line
            let { name, decl: exportedDecl } = extractTypeScriptDeclaration(srcString, decl);
            if (name) {
              exportedDecl = getDocComment(srcString, exp) + exportedDecl;
              let exportedDecls = exports.get(name);
              if (!exportedDecls) {
                exportedDecls = [];
                exports.set(name, exportedDecls);
              }
              exportedDecls.push(exportedDecl);
            }
          }
        }
    } finally {
      if (tree) tree.delete();
    }
  }

  // 缓存管理
  if (exportsCache.size > EXPORTS_CACHE_HIGH_WATER_MARK) {
    const keysToRemove: any = [];
    for (const key of exportsCache.keys()) {
      keysToRemove.push(key);
      if (exportsCache.size <= EXPORTS_CACHE_LOW_WATER_MARK) break;
    }
    for (const key of keysToRemove) {
      exportsCache.delete(key);
    }
  }

  exportsCache.set(uri, { mtime, exports });
  return exports;
}

/**
 * 从TypeScript声明节点中提取声明的名称和声明文本。
 * @param srcString 源代码字符串。
 * @param declNode 声明节点。
 * @returns 返回一个对象，包含声明的名称和声明文本。
 */
function extractTypeScriptDeclaration(srcString: string, declNode: any): { name: string; decl: string } {
  let name = '';
  let decl = '';
  if (!declNode) return { name, decl };

  // 根据不同的声明类型提取名称和声明文本
  switch (declNode.type) {
    case 'variable_declaration':
      // 变量声明可能包含多个变量
      const variableDeclarations = declNode.namedChildren.filter(
        (child: { type: string }) => child.type === 'variable_declarator'
      );
      name = variableDeclarations
        .map((declaration: { childForFieldName: (arg0: string) => any }, index: number) => {
          const identifier = declaration.childForFieldName('name');
          return identifier ? (index > 0 ? ', ' : '') + identifier.text : '';
        })
        .join('');
      decl = srcString.substring(declNode.startIndex, declNode.endIndex);
      break;
    case 'function_declaration':
    case 'class_declaration':
    case 'interface_declaration':
    case 'type_alias_declaration':
    case 'enum_declaration':
      // 这些声明类型的名称位于'name'字段
      const identifier = declNode.childForFieldName('name');
      if (identifier) {
        name = identifier.text;
      }
      decl = srcString.substring(declNode.startIndex, declNode.endIndex);
      break;
    // 可以根据需要添加更多的case来处理其他类型的声明
    default:
      // 对于未知的声明类型，不提取名称和声明文本
      console.warn(`Unsupported declaration type: ${declNode.type}`);
      break;
  }

  return { name, decl };
}

/**
 * 获取所有打开的同类型文件，排除当前活动的文件
 * @returns 其他打开的文件数组
 */
export function getALLOpenDocuments() {
  // 获取当前活动的编辑器
  const activeEditor = vscode.window.activeTextEditor;
  // 获取所有打开的文件
  const allOpenDocuments = vscode.workspace.textDocuments;
  // 创建一个集合来存储所有文档，避免重复
  const documentSet = new Set(allOpenDocuments);
  // 将可见文档添加到集合中
  const openDocuments = Array.from(documentSet);
  // 如果有活动的编辑器，过滤掉当前活动的文件
  if (activeEditor) {
    const otherDocuments = openDocuments.filter((doc) => {
      const fileName = activeEditor.document.fileName;
      const fileType = '.' + fileName.split('.').pop();
      return doc.fileName !== activeEditor.document.fileName && doc.fileName.endsWith(fileType);
    });
    return otherDocuments;
  }
  // 如果没有活动的编辑器，返回所有打开的文件
  return openDocuments;
}

/**
 * 获取与目标文本相关的文档片段及其相关性得分。
 * @param documentText - 文档文本，表示要搜索的文档文本。
 * @param targetText - 目标文本，想要在 documentText 中找到的内容。
 * @returns 包含片段和得分的Promise对象。
 */
export function getRelevantSnippet(documentText: string, targetText: string) {
  return new Promise<{ snippetText: string; score: number }>((resolve, reject) => {
    try {
      const TfIdf = natural.TfIdf;
      // 计目标代码片段的TF-IDF向量
      const targetTfidf = new TfIdf();
      targetTfidf.addDocument(documentText);
      // 计算TF-IDF相关性
      targetTfidf.tfidfs(targetText, (_i: number, measure: number) => {
        resolve({ snippetText: documentText, score: isNaN(measure) ? 0 : measure });
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 获取所有打开文档中与目标文本相关的片段及其评分。
 * @param targetText - 目标文本。
 * @returns 包含片段文本和评分的数组。
 */
export async function getALLOpenDocumentsRelevantSnippet(targetText: string) {
  try {
    // 获取所有文档是异步完成的
    const allDocuments = getALLOpenDocuments();
    // 使用Promise.all并行处理所有文档，提升性能
    const snippetScores = await Promise.all(
      allDocuments.map(async (doc) => {
        const docText: string = doc.getText();
        const folderPath = vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
        const fileName = doc.fileName.replace(folderPath, '');
        try {
          // 获取相关片段和评分
          const { snippetText, score } = await getRelevantSnippet(docText, targetText);
          return { snippetText, score, fileName };
        } catch (error) {
          // 捕获错误并返回默认值
          return { snippetText: docText, score: 0 };
        }
      })
    );
    // 对结果进行排序
    snippetScores.sort((a, b) => b.score - a.score);
    // 返回结果
    return snippetScores;
  } catch (error) {
    // 捕获并处理获取所有文档时的错误
    return [];
  }
}

/**
 * 获取与指定文档在同一文件夹中的文件列表。
 * @param document 包含文件URI的文档对象。
 * @returns 文件列表数组。
 */
export async function getSameFolderFiles(document) {
  const fileUri = document.uri;
  const folderPath = path.dirname(fileUri.fsPath);
  try {
    const files = await fs.readdir(folderPath);
    const filesList = files.filter((file) => file !== path.basename(fileUri.fsPath));
    const fileList = await getFileList(filesList, folderPath);
    const needFiles = fileList.filter((item) => {
      const fileName = document.fileName;
      const fileType = '.' + fileName.split('.').pop();
      return item.fileName.endsWith(fileType);
    });
    return needFiles;
  } catch (err) {
    return [];
  }
}

/**
 * 异步获取文件列表及其内容。
 * @param files 文件名数组。
 * @param folderPath 文件夹路径。
 * @returns 包含文件名和文件内容的对象数组。
 */
async function getFileList(files: string[], folderPath: string) {
  const fileList = await Promise.all(
    files.map(async (fileName) => {
      const filePath = path.join(folderPath, fileName);
      try {
        const fileContent = await fs.readFile(filePath, 'utf8');
        return { fileName, fileContent };
      } catch (err) {
        return { fileName, fileContent: '' };
      }
    })
  );
  return fileList;
}

/**
 * 异步获取同一文件夹中与目标文本相关的片段。
 * @param document - 当前文档对象。
 * @param targetText - 目标文本字符串。
 * @returns 包含相关片段和评分的数组，按评分降序排列。
 */
export async function getSameFolderRelevantSnippet(document, targetText: string) {
  try {
    // 获取所有文档是异步完成的
    const allDocuments = await getSameFolderFiles(document);
    // 使用Promise.all并行处理所有文档，提升性能
    const snippetScores = await Promise.all(
      allDocuments.map(async (file) => {
        const docText: string = file.fileContent;
        const fileName: string = file.fileName;
        try {
          // 获取相关片段和评分
          const { snippetText, score } = await getRelevantSnippet(docText, targetText);
          return { snippetText, score, fileName };
        } catch (error) {
          // 捕获错误并返回默认值
          return { snippetText: docText, score: 0, fileName };
        }
      })
    );
    // 对结果进行排序
    snippetScores.sort((a, b) => b.score - a.score);
    // 返回结果
    return snippetScores;
  } catch (error) {
    // 捕获并处理获取所有文档时的错误
    return [];
  }
}
/**
 * 获取相关代码片段函数
 * @param language - 编程语言
 * @param targetText - 目标文本
 * @param docText - 文档文本
 * @returns 排序后的相关代码片段数组
 */
export async function getRelevantSnippetFunctions({ language, targetText, docText }) {
  const tree = await parseTreeSitter(language, docText);
  const functionNodes = queryFunctions(language, tree.rootNode).map((res) => {
    // 检查是否闭合
    const functionNode = res.captures.find((c) => c.name === 'function').node;
    if (functionNode.type == 'comment') return null;
    return functionNode;
  });
  const targetFunctionNodes: { snippetText: string; score: number }[] = [];
  for (let i = 0; i < functionNodes.length; i++) {
    const functionNode = functionNodes[i];
    if (functionNode) {
      const textCursor = functionNode.text;
      const { snippetText, score } = await getRelevantSnippet(textCursor, targetText);
      if (score > 0) {
        targetFunctionNodes.push({ snippetText, score });
      }
    }
  }
  return targetFunctionNodes.sort((a, b) => b.score - a.score);
}
