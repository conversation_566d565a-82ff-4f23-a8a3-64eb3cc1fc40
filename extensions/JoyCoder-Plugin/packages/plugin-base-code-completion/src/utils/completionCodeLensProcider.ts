import * as vscode from 'vscode';

export default class CompletionCodeLensProvider {
  async provideCodeLenses(document, token) {
    // 在这里你可以决定在哪些行上显示Code Lens
    const codeLenses: vscode.CodeLens[] = [];
    try {
      const editor = vscode.window.activeTextEditor;
      if (editor) {
        const position = editor.selection.active;
        const lineNumber = position.line; // 行号是从 0 开始的，所以加 1
        // const lineText = document.lineAt(lineNumber).text;
        // const endPosition = lineText.length;
        const range = new vscode.Range(lineNumber, position.character, lineNumber, position.character);
        const codeLens = new vscode.CodeLens(range, {
          title: '按【TAB】键采纳 /【ESC】键取消', // 显示在Code Lens上的文本
          command: '', // 点击Code Lens时执行的命令
          arguments: [position], // 命令的参数
        });
        codeLenses.push(codeLens);
      }
    } catch (error) {}
    return codeLenses;
  }
}
