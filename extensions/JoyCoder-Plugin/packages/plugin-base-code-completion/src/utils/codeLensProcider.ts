import * as vscode from 'vscode';
import { isFunctionOrClassDefinition } from './util';
import { CODELENS_MENU } from './constant';
import { getFunctionPositions } from '../utils/elidableText';

export default class CompletionCodeLensProvider {
  private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
  public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;
  private fireLines: vscode.TextLine[] = [];
  private vsCodeLensDisposable: vscode.Disposable | null;
  public static vsCodeLensInstance: CompletionCodeLensProvider;

  async removeCodeLensFromLine(line) {
    this.fireLines.push(line);
    this.rerenderCodeLenses();
    this._onDidChangeCodeLenses.fire();
  }
  disposeCodeLenses() {
    this.vsCodeLensDisposable && this.vsCodeLensDisposable.dispose();
    this.vsCodeLensDisposable = null;
  }
  rerenderCodeLenses() {
    try {
      // 如果已经注册过，则不再重新注册
      if (this.vsCodeLensDisposable) {
        return this.vsCodeLensDisposable;
      }
      this.vsCodeLensDisposable = vscode.languages.registerCodeLensProvider({ language: '*', scheme: '*' }, this);
    } catch (error) {}
    return this.vsCodeLensDisposable;
  }

  async provideCodeLenses(document, token) {
    // 在这里你可以决定在哪些行上显示Code Lens
    const codeLenses: vscode.CodeLens[] = [];
    try {
      const lines = await this.getPositionsLines(document);
      // const lines = this.getRegLines(document);
      for (let index = 0; index < lines.length; index++) {
        const line: vscode.TextLine = lines[index].line;
        if (this.fireLines.findIndex((l) => l.lineNumber === line.lineNumber && l.text == line.text) > -1) {
          continue;
        }
        const endLine: vscode.TextLine = lines[index].endLine;
        const lineNumber = line.lineNumber;
        const range = new vscode.Range(lineNumber, 0, lineNumber, 0);
        const commands = CODELENS_MENU(line, endLine);
        for (let index = 0; index < commands.length; index++) {
          const command = commands[index];
          const codeLens = new vscode.CodeLens(range, command);
          codeLenses.push(codeLens);
        }
      }
    } catch (error) {}
    return codeLenses;
  }

  async getPositionsLines(document) {
    const editor = vscode.window.activeTextEditor;
    const functionLines: { line: vscode.TextLine; endLine: vscode.TextLine }[] = [];
    try {
      if (editor) {
        const languageId = getSwitchLanguageId(document);
        const fnPositions = await getFunctionPositions(languageId, document.getText());
        for (let i = 0; i < fnPositions.length; i++) {
          const position = fnPositions[i]?.startPosition;
          const ensPosition = fnPositions[i]?.endPosition;
          const row = position.row;
          const endRow = ensPosition.row;
          const line = document.lineAt(row);
          const endLine = document.lineAt(endRow);
          if (line && !fnPositions[i]?.builtInFunction) {
            line['functionContent'] = fnPositions[i]?.functionContent;
            functionLines.push({ line, endLine });
          }
        }
      }
    } catch (error) {
      return [];
    }
    return functionLines;
  }
  getRegLines(document) {
    const editor = vscode.window.activeTextEditor;
    const functionLines: vscode.TextLine[] = [];
    if (editor) {
      const functionRegex = isFunctionOrClassDefinition(editor);
      for (let i = 0; i < document.lineCount; i++) {
        const line = document.lineAt(i);
        if (functionRegex?.exec(line.text)) {
          functionLines.push(line);
        }
      }
    }
    return functionLines;
  }
  //  官方提供的 但是不支持箭头函数,对一些后端语言支持不好
  async markSymbolsInDocument() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }

    const document = editor.document;
    const symbols: vscode.SymbolInformation[] | vscode.DocumentSymbol[] = await vscode.commands.executeCommand(
      'vscode.executeDocumentSymbolProvider',
      document.uri
    );
    const functionLines: vscode.TextLine[] = [];
    if (symbols) {
      symbols.forEach((symbol) => {
        const symbolKind = symbol.kind;
        const symbolRange = symbol.range;
        // 判断符号类型
        if (
          symbolKind === vscode.SymbolKind.Function ||
          symbolKind === vscode.SymbolKind.Class ||
          symbolKind === vscode.SymbolKind.Interface ||
          // symbolKind === vscode.SymbolKind.Constructor ||
          symbolKind === vscode.SymbolKind.Event ||
          symbolKind === vscode.SymbolKind.Struct ||
          symbolKind === vscode.SymbolKind.Object ||
          symbolKind === vscode.SymbolKind.Method
        ) {
          // 标记行号
          const line = symbolRange.start.line;
          functionLines.push(line);
          // 这里可以根据需要进行进一步的操作，比如标记行号等
        }
      });
    }
  }
}
export function getSwitchLanguageId(document) {
  try {
    const codeText = document.getText();
    // const isTs = codeText.match(/<script.*?lang=["'](ts|typescript)["'].*?>/i);
    const isTs = codeText.match(/<script.*?lang=["'](ts|typescript)["'].*?(setup)?.*?>/i);
    const languages = {
      vue: isTs ? 'typescript' : 'javascript',
      // vue: isTs ? 'typescriptreact' : 'jsx',
      jue: 'javascript',
    };
    return languages[document.languageId] || document.languageId;
  } catch (error) {
    return document.languageId;
  }
}
