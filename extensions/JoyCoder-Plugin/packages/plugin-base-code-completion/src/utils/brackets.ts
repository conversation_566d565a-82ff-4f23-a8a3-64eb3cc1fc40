import { isComment } from './commentCode';

/**
 * 简化字符串中的括号。
 * @param str - 输入的字符串。
 * @param lang - 语言类型，用于判断注释。
 * @returns 剩余未匹配的括号数组。
 */
export function simplifyStringBracket(str, lang) {
  let brackets: string[] = [];
  for (let i = 0; i < str.length; i++) {
    if (!'{[()]}'.includes(str[i]) || isComment(str.substring(0, i), lang).isComment) continue;
    if ('{[('.includes(str[i])) {
      brackets.push(str[i]);
      continue;
    }
    if (
      (str[i] === '}' && brackets[brackets.length - 1] === '{') ||
      (str[i] === ']' && brackets[brackets.length - 1] === '[') ||
      (str[i] === ')' && brackets[brackets.length - 1] === '(')
    ) {
      brackets = brackets.slice(0, brackets.length - 1);
      continue;
    }
    brackets.push(str[i]);
  }
  return brackets;
}
/**
 * 查找缺失的括号
 * @param prefix 前缀字符串
 * @param suffix 后缀字符串
 * @param lang 语言类型
 * @returns 缺失的括号数组或null
 */
export function findMissingBracket(prefix, suffix, lang) {
  let prefixBracket = simplifyStringBracket(prefix, lang);
  let suffixBracket = simplifyStringBracket(suffix, lang);
  if (prefixBracket.filter((item) => ']})'.includes(item)).length > 0) return null;
  if (suffixBracket.filter((item) => '([{'.includes(item)).length > 0) return null;
  if (prefixBracket.length >= suffixBracket.length) {
    while (suffixBracket.length > 0) {
      if (
        (prefixBracket[0] === '[' && suffixBracket[suffixBracket.length - 1] === ']') ||
        (prefixBracket[0] === '(' && suffixBracket[suffixBracket.length - 1] === ')') ||
        (prefixBracket[0] === '{' && suffixBracket[suffixBracket.length - 1] === '}')
      ) {
        suffixBracket = suffixBracket.slice(0, suffixBracket.length - 1);
        prefixBracket = prefixBracket.slice(1);
      } else {
        return null;
      }
    }
    const arr: string[] = [];
    while (prefixBracket.length > 0) {
      const currentBracket = prefixBracket.pop();
      if (currentBracket === '{') arr.push('}');
      if (currentBracket === '(') arr.push(')');
      if (currentBracket === '[') arr.push(']');
    }
    return arr;
  } else {
    while (prefixBracket.length > 0) {
      if (
        (prefixBracket[0] === '[' && suffixBracket[suffixBracket.length - 1] === ']') ||
        (prefixBracket[0] === '(' && suffixBracket[suffixBracket.length - 1] === ')') ||
        (prefixBracket[0] === '{' && suffixBracket[suffixBracket.length - 1] === '}')
      ) {
        suffixBracket = suffixBracket.slice(0, suffixBracket.length - 1);
        prefixBracket = prefixBracket.slice(1);
      } else {
        return null;
      }
    }
    const arr: string[] = [];
    while (suffixBracket.length > 0) {
      const currentBracket = suffixBracket.pop();
      if (currentBracket === '}') arr.push('{');
      if (currentBracket === ')') arr.push('(');
      if (currentBracket === ']') arr.push('[');
    }
    return arr;
  }
}
/**
 * 查找字符串中不匹配的括号索引。
 * @param str - 要检查的字符串。
 * @param lang - 代码语言，用于注释判断。
 * @returns 第一个不匹配的括号索引，如果所有括号匹配则返回字符串长度。
 */
export function findImpairIndexBrackets(str, lang) {
  let brackets: string[] = [];
  for (let i = 0; i < str.length; i++) {
    if (isComment(str.substring(0, i), lang).isComment) continue;
    if ('{[('.includes(str[i])) {
      brackets.push(str[i]);
    }
    if (']})'.includes(str[i])) {
      const item = brackets.at(-1);
      if (!item) return i;
      if ((item === '[' && str[i] === ']') || (item === '{' && str[i] === '}') || (item === '(' && str[i] === ')')) {
        brackets = brackets.slice(0, brackets.length - 1);
        continue;
      } else {
        return i;
      }
    }
    continue;
  }
  return str.length;
}
/**
 * 移除字符串中无用的括号。
 * @param prefix - 前缀字符串。
 * @param suffix - 后缀字符串。
 * @param str - 需要处理的字符串。
 * @param lang - 语言类型。
 * @returns 处理后的字符串。
 */
export function removeUselessBrackets(prefix, suffix, str, lang) {
  const bracketsNeeded = findMissingBracket(prefix, suffix, lang);
  if (!bracketsNeeded) return str.substring(0, findImpairIndexBrackets(str, lang)).trimEnd();
  const bracketsNotPaired = simplifyStringBracket(str, lang);
  if (bracketsNotPaired.length === 0 || bracketsNotPaired.filter((item) => '}])'.includes(item)).length === 0)
    return str;
  let p = 0,
    q = 0;
  while (p < bracketsNeeded.length && q < bracketsNotPaired.length) {
    if (bracketsNeeded[p] === bracketsNotPaired[q]) {
      p++;
      q++;
    } else {
      break;
    }
  }
  let brackets = bracketsNotPaired.slice(q);
  for (let i = 0; i < str.length; i++) {
    if (str[str.length - 1 - i] === brackets[brackets.length - 1]) {
      brackets = brackets.slice(0, brackets.length - 1);
      if (brackets.length === 0) {
        return str.substring(0, str.length - i - 1).trimEnd();
      }
    }
  }
  return str;
}
/**
 * 检查文档是否需要括号配对检查。
 * @param document - 包含语言ID的文档对象。
 * @returns 如果需要检查返回true，否则返回false。
 */
export function needCheckBracketPair(document) {
  const languageId = document.languageId;
  const languagesNotNeedCheck = [
    'python',
    'html',
    'matlab',
    'shellscript',
    'fortran',
    'lua',
    'lisp',
    'pascal',
    'sql',
    'perl',
    'prolog',
    'ruby',
    'tex',
    'vb',
  ];
  if (languagesNotNeedCheck.includes(languageId)) {
    return false;
  } else {
    return true;
  }
}
