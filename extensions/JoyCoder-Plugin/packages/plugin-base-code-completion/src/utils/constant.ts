import * as vscode from 'vscode';
import { hasCommentMarker } from './elidableText';
import { GlobalState } from '@joycoder/shared';
export const COMMENTS = ['//', '#', '<!--', '/*', '--'];
// 判断注释
export const isEqualCommentFlag = (text) => {
  if (text) {
    const editor = vscode.window.activeTextEditor;
    const languageId: string | undefined = editor?.document?.languageId;
    return (
      COMMENTS.includes(text.trim()) ||
      hasCommentMarker({
        source: text,
        languageId,
      })
    );
  }
};

// '解释代码', '逐行注释', '函数注释', '代码重构', '单元测试'
export const CODELENS_MENU = (line: vscode.TextLine, endLine?: vscode.TextLine) => {
  const codeLensList = [
    {
      title: '函数注释', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.functionComment', // 点击Code Lens时执行的命令
      arguments: [line], // 命令的参数
    },
    {
      title: '代码评审', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.codeReview', // 点击Code Lens时执行的命令
      arguments: [line], // 命令的参数
    },
    {
      title: '代码重构', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.reconstruction', // 点击Code Lens时执行的命令
      arguments: [line, endLine], // 命令的参数
    },
    {
      title: '逐行注释', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.comment', // 点击Code Lens时执行的命令
      arguments: [line, endLine], // 命令的参数
    },
    {
      title: '单元测试', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.test', // 点击Code Lens时执行的命令
      arguments: [line], // 命令的参数
    },
    // {
    //   title: '解释代码', // 显示在Code Lens上的文本
    //   command: 'JoyCode.codelens.explain', // 点击Code Lens时执行的命令
    //   arguments: [line], // 命令的参数
    // },
    // {
    //   title: '代码优化', // 显示在Code Lens上的文本
    //   command: 'JoyCode.codelens.optimization', // 点击Code Lens时执行的命令
    //   arguments: [line], // 命令的参数
    // },
  ];
  // 进判断menuList不存在的情况
  let menuList = GlobalState.get('JoyCode.codeLens.menuList');
  menuList = !menuList ? ['functionComment', 'codeReview', 'reconstruction', 'comment', 'test'] : menuList;
  const intersection = codeLensList.filter((codeLensItem) =>
    menuList.some((menuItem) => codeLensItem.command.includes(menuItem))
  );
  if (intersection.length > 0) {
    intersection.push({
      title: '×', // 显示在Code Lens上的文本
      command: 'JoyCode.codelens.del', // 点击Code Lens时执行的命令
      arguments: [line], // 命令的参数
    });
  }
  return intersection;
};
export const completionModel = [
  {
    label: 'JoyCode-Base-Lite',
    description: 'JoyCoder代码模型，此模型在注释续写和生成代码场景速度较快',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'llm', //聊天模型
      url: 'http://deepseek-chat.jd.local/v1/chat/completions', //聊天模型地址
      // url: 'http://deepseek-chat.jdcloud.com/v1/chat/completions', //聊天模型地址
      // url: 'http://code-chat.jdcloud.com/v1/chat/completions', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'llm', //聊天模型
      url: 'http://deepseek-complete.jd.local/v1/code/generate', //续写模型地址
      // url: 'http://deepseek-complete.jdcloud.com/v1/code/generate', //续写模型地址
      // url: 'http://code-complete.jdcloud.com/v1/code/generate', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
  {
    label: 'JoyCode-Base-Pro',
    description: 'JoyCoder代码模型，此模型在注释续写和生成代码场景效果较好',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'llm', //聊天模型
      url: 'http://deepseek-chat.jd.local/v1/chat/completions', //聊天模型地址
      // url: 'http://deepseek-chat.jdcloud.com/v1/chat/completions', //聊天模型地址
      // url: 'http://code-chat.jdcloud.com/v1/chat/completions', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'llm', //聊天模型
      url: 'http://deepseek-complete-v2.jd.local/v1/code/generate', //续写模型地址
      // url: 'http://deepseek-complete.jdcloud.com/v1/code/generate', //续写模型地址
      // url: 'http://code-complete.jdcloud.com/v1/code/generate', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
];
export const modelLabels = ['JoyCode-Base-Lite', 'JoyCode-Base-Pro'];
