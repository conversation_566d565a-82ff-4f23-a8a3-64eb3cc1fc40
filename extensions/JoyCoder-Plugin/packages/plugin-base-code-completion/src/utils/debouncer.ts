/**
一个实现防抖功能的类，可以延迟函数的执行，直到一定时间内没有进一步的调用。 
*/
class Debouncer {
  private state:
    | {
        timer: NodeJS.Timeout | number;
        reject: (reason?: unknown) => void;
      }
    | undefined;

  async debounce(ms: number): Promise<void> {
    if (this.state) {
      clearTimeout(this.state.timer);
      this.state.reject(new Error('Debounce'));
      this.state = undefined;
    }
    return new Promise((resolve, reject) => {
      this.state = {
        timer: setTimeout(() => {
          resolve();
          this.state = undefined;
        }, ms),
        reject: reject,
      };
    });
  }
}

export default Debouncer;
