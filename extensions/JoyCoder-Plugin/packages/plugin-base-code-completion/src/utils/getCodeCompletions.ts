import * as vscode from 'vscode';
import {
  GetCodeCompletions,
  askClientCode,
  askClientChat,
} from '@joycoder/plugin-base-ai/src/proxy/codeCompletionsAsk';
import { isEqualCommentFlag } from './constant';
import { hasCommentMarker, isCursorInFunctionOrClass } from './elidableText';
import { getInnerModelNameToBusiness } from './util';
import { isBusinessLocal } from '@joycoder/shared';
// import { GlobalState } from '@joycoder/shared';

/**
 * 扩展默认的 VS Code 触发类型，以区分通过键盘快捷键手动调用完成和通过悬停在幽灵文本上调用完成。
 */
export enum TriggerKind {
  /** 通过用户悬停在幽灵文本上显式触发完成。 */
  Hover = 'Hover',
  /** 在编辑时自动触发完成。 */
  Automatic = 'Automatic',
  /** 通过用户调用键盘快捷键手动触发完成。 */
  Manual = 'Manual',
  /** 当用户使用建议小部件在不同完成项之间循环时。 */
  SuggestWidget = 'SuggestWidget',
}

export function getCodeCompletions(
  prompt: string,
  lang: string,
  options,
  gptCompletionModel, //配置的模型，如果未配置，就是空的
  gptCompletionOption // 配置的模型详细信息
): Promise<GetCodeCompletions> {
  return new Promise(async (resolve, reject) => {
    if (!lang) {
      // 如果未识别出来语言 直接返回
      reject({ completions: [] });
      return;
    }
    const prevCursor = options?.prevCursor || '';
    const nextCursor = options?.nextCursor || '';
    const maxLines = options?.maxLines || 1;
    const codeCompletionOption: any = gptCompletionOption?.completion || {};
    try {
      const isCommentEnter = checkCodeCommentAndEnter(prompt);
      const isComment = checkCodeComment(prompt, lang) && isCommentEnter;
      const promptString = await getPromptStr(prompt, lang, prevCursor, nextCursor);
      //deepseek
      const modelName = getInnerModelNameToBusiness('JoyCode-Base');
      if (`${gptCompletionModel}`.toLowerCase().startsWith(modelName.toLowerCase())) {
        const prevCodeLines = (options?.prevCode ?? '').split('\n').length;
        if ((isComment || (options.isPrevRowComment && prevCodeLines < 6)) && !isBusinessLocal()) {
          //注释生成代码
          const resGPT: GetCodeCompletions = await askClientChat(promptString, gptCompletionOption);
          resolve(resGPT);
        } else {
          //代码续写
          const resGPT: GetCodeCompletions = await askClientCode(
            options?.prevCode || '',
            options?.nextCode || '',
            lang,
            options?.filePath || '',
            options?.projectName || '',
            options?.related_files || '',
            maxLines,
            'llm',
            codeCompletionOption
          );
          resolve(resGPT);
        }
      } else {
        //其他配置的模型，从客户端发起
        const resGPT: GetCodeCompletions = await askClientChat(promptString, gptCompletionOption);
        resolve(resGPT);
      }
    } catch (err) {
      console.error(err);
      reject(err);
    }
  });
}

/**
code llama 检查场景，是否需要使用chat接口，注释生成代码、生成注释
@param {string} inputText - 输入文本
@returns {bollen}
 */
export const checkCodeComment = (inputText, languageId?: string) => {
  if (!inputText) {
    return false;
  }
  let isComment = false;
  const editor: any = vscode.window.activeTextEditor;
  languageId = editor.document.languageId;
  const isCommentMarker = hasCommentMarker({
    source: inputText,
    languageId,
  });
  if (isCommentMarker) {
    isComment = isCommentMarker;
  } else {
    if (inputText.trim() === '//') {
      isComment = true;
    } else if (isCommentsToCode(inputText)) {
      isComment = true;
    } else if (isEqualCommentFlag(inputText.trim())) {
      isComment = true;
    }
  }
  return isComment;
};
/**
 * 检查输入文本是否以注释标记结尾
 * @param inputText - 输入文本
 * @param languageId - 可选的语言标识符
 * @returns 如果输入文本以注释标记结尾，则返回true，否则返回false
 */
export const checkCodeCommentAndEnter = (inputText, languageId?: string) => {
  if (!inputText) {
    return false;
  }
  const editor: any = vscode.window.activeTextEditor;
  languageId = editor.document.languageId;
  const isCommentMarker = hasCommentMarker({
    source: inputText,
    languageId,
  });
  const end = inputText.endsWith(`\n`);
  return isCommentMarker && end;
};

/**
根据提供的代码生成对应注释
@param {string} inputText - 输入文本
@param {string} lang - 语言
@param {number} prevCursor - 前一个光标位置的内容
@param {number} nextCursor - 下一个光标位置的内容
@param {string} gptCompletionModel - 预测补全配置的模型，默认为空字符串
@returns {string} - 提示字符串
 */
export const getPromptStr = async (inputText, lang, prevCursor, nextCursor) => {
  const isFunctionBody = await isCursorInFunctionOrClass();
  const promptMarkDown = isFunctionBody ? `，请仅提供函数体或代码块的内容，不包括函数定义部分` : '';
  let promptStr = `你是一个软件开发专家，请使用${
    lang || 'javascript'
  }语言语法根据当前行${inputText}代码预测，输出预测代码内容${promptMarkDown}，不输出解释或其他信息，直接以文本形式输出`;
  const editor: any = vscode.window.activeTextEditor;
  const isCommentsCode = isCommentsToCode(inputText, lang);
  const isCommentMarker = hasCommentMarker({
    source: inputText,
    languageId: editor.document.languageId,
  });
  const isCommentText = isCommentMarker && !checkCodeCommentAndEnter(inputText, lang);
  if (inputText.trim() === '//' || isCommentText) {
    promptStr = `你是一个软件开发专家，请根据提供的 ${
      lang || 'javascript'
    } 代码的第一行 ${inputText}，生成简洁明了的中文注释。请注意，已存在的注释部分不需要输出，只需补充新的注释内容即可。无需添加注释符号，直接以文本形式输出。
`;
  } else if (isCommentsCode) {
    promptStr = `你是一个软件开发专家，请根据提供的内容\n${inputText}\n使用\n${
      lang || 'javascript'
    }\n语言语法生成代码。请提供尽量完整的代码，并使用markdown代码块格式化${promptMarkDown}，不需要其他说明，代码中不附加注释。`;
  }
  if (nextCursor && prevCursor) {
    promptStr = `你是一个软件开发专家，根据上文\n${prevCursor}\n和下文\n${nextCursor}\n，使用\n${
      lang || 'javascript'
    }\n语言补全当前行代码\n${inputText}\n，只输出预测的新代码，避免输出上文代码中已有的重复部分${promptMarkDown}，不需要其他信息，直接以文本形式输出`;
    if (isCommentText) {
      //  生产注释
      promptStr = `你是一个软件开发专家，请使用\n${
        lang || 'javascript'
      }\n的语法，根据提供的代码上文\n${prevCursor}\n和下文\n${nextCursor}\n提示或联系当前行代码\n${inputText}\n之后，仅提供注释文本，尽量简洁字数不要超过20个，不需要输出其他信息或代码，请注意，已存在的注释部分不需要输出，只需补充新的注释内容即可。直接使用中文以文本形式输出`;
    } else if (isCommentsCode) {
      promptStr = `你是一个软件开发专家，请使用\n${
        lang || 'javascript'
      }\n的语法，根据提供的代码上文\n${prevCursor}\n和下文\n${nextCursor}\n提示或联系当前行代码\n${inputText}\n之后，仅提供生成的代码，输出内容尽量简洁${promptMarkDown}，不需要输出其他信息，直接以文本形式输出`;
    }
  } else if (nextCursor) {
    promptStr = `你是一个软件开发专家，请使用\n${
      lang || 'javascript'
    }\n，根据提供的代码下文\n${nextCursor}\n提示或联系当前行\n${inputText}\n之后，主要根据代码下文，仅输出预测的代码内容${promptMarkDown}，下文代码中已存在的重复代码一定不要输出，不需要输出其他信息，直接以文本形式输出`;
    if (isCommentText) {
      promptStr = `你是一个软件开发专家，请使用\n${
        lang || 'javascript'
      }\n，根据提供的代码下文\n${nextCursor}\n提示或联系当前行\n${inputText}\n之后，仅提供注释文本，尽量简洁字数不要超过20个，不需要输出其他信息或代码，请注意，已存在的注释部分不需要输出，只需补充新的注释内容即可。直接使用中文以文本形式输出`;
    } else if (isCommentsCode) {
      promptStr = `你是一个软件开发专家，请使用\n${
        lang || 'javascript'
      }\n，根据提供的代码下文\n${nextCursor}\n提示或联系当前行\n${inputText}\n之后，仅提供生成的代码，输出内容尽量简洁${promptMarkDown}，不需要输出其他信息，直接以文本形式输出`;
    }
  }
  return promptStr;
};

// 判断输入文本是否为注释，并且长度大于 3
function isCommentsToCode(inputText, languageId?: string) {
  inputText = inputText.trim();
  // const start = inputText.startsWith('//');
  const editor: any = vscode.window.activeTextEditor;
  languageId = editor.document.languageId;
  const isCommentMarker = hasCommentMarker({
    source: inputText,
    languageId,
  });
  return isCommentMarker && inputText.length > 3;
}
