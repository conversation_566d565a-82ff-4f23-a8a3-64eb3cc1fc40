import * as vscode from 'vscode';
import { getEditorInsertSpaces, getEditorTabSize } from '../utils/shared';
import { lines } from './completionUntil';

/**
 * 格式化插入的文本
 * @param document - 当前文档
 * @param position - 插入位置
 * @param currentLinePrefix - 当前行前缀
 * @param insertText - 要插入的文本
 * @returns Promise<void>
 */
export async function formatCompletion({ document, position, currentLinePrefix, insertText }): Promise<void> {
  try {
    const insertedLines = lines(insertText);
    const endPosition =
      insertedLines.length <= 1
        ? new vscode.Position(position.line, currentLinePrefix.length + insertedLines[0].length)
        : new vscode.Position(position.line + insertedLines.length, 0);

    // 从行首开始，以便在需要时格式化整行。
    const rangeToFormat = new vscode.Range(new vscode.Position(position.line, 0), endPosition);

    const formattingChanges = await vscode.commands.executeCommand<vscode.TextEdit[] | undefined>(
      'vscode.executeFormatDocumentProvider',
      document.uri,
      {
        tabSize: getEditorTabSize(document.uri, vscode.workspace, vscode.window),
        insertSpaces: getEditorInsertSpaces(document.uri, vscode.workspace, vscode.window),
      }
    );

    const formattingChangesInRange = (formattingChanges || []).filter((change) => rangeToFormat.contains(change.range));

    if (formattingChangesInRange.length !== 0) {
      await vscode.window.activeTextEditor?.edit(
        (edit) => {
          for (const change of formattingChangesInRange) {
            edit.replace(change.range, change.newText);
          }
        },
        { undoStopBefore: false, undoStopAfter: true }
      );
    }
  } catch (unknownError) {}
}

/**
 * 获取指定语言的格式化器
 * @param languageId 语言ID
 * @returns 格式化器ID或undefined
 */
export function getFormatter(languageId: string): string | undefined {
  // Access the configuration for the specific languageId
  const config = vscode.workspace.getConfiguration(`[${languageId}]`);

  // Get the default formatter setting
  const defaultFormatter = config.get('editor.defaultFormatter');

  if (defaultFormatter) {
    return defaultFormatter as string;
  }

  // Fallback: Check the global default formatter if specific language formatter is not set
  const globalConfig = vscode.workspace.getConfiguration();
  return globalConfig.get('editor.defaultFormatter');
}
