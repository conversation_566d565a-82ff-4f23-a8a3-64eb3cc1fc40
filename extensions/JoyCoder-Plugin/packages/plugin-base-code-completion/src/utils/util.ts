import * as vscode from 'vscode';
import { getRemoteConfigSync, getVscodeConfig, isBusiness, Logger } from '@joycoder/shared';
import AdoptResultCache from '../stats/adoptResultCache';
import { formatCompletion } from './format-completion';
import { lines } from './completionUntil';
import { findLast } from 'lodash';
import { getLanguageConfig } from './elidableText';

let acceptDisposable: vscode.Disposable = new vscode.Disposable(() => {});

/**
 * 检查编辑器中的代码是否为函数或类定义
 * @param {any} editor - 编辑器对象
 * @returns {RegExp | undefined} 匹配模式或未定义
 */
export function isFunctionOrClassDefinition(editor) {
  const language = editor.document.languageId;
  const patterns = {
    // javascript: /((function|class)\s+\w*\s*([^)])|\w\s*=>\s*){/,
    javascript: /(?:^|\s)(class|function)\s+[\w$]+/,
    vue: /(?:^|\s)(class|function)\s+[\w$]+/,
    javascriptreact: /(?:^|\s)(class|function)\s+[\w$]+/,
    typescript: /(?:^|\s)(class|function)\s+[\w$]+/,
    typescriptreact: /(?:^|\s)(class|function)\s+[\w$]+/,
    arkts: /(?:^|\s)(class|function)\s+[\w$]+/,
  };

  const pattern = patterns[language.toLowerCase()];
  return pattern;
}

/**
 * 创建一个新的信号，这个信号是从一个父信号分叉出来的。当父信号被中止时，分叉出的信号也会被中止。
 * 这允许在异步操作中传播中止信号。
 * 然而，中止分叉出的控制器不会影响父控制器。
 * Forks an AbortSignal into a new AbortController.
 * @param signal The AbortSignal to fork.
 * @returns A new AbortController whose signal is aborted if the input signal is aborted.
 */
export function forkSignal(signal: AbortSignal): AbortController {
  const controller = new AbortController();
  if (signal.aborted) {
    controller.abort();
  }
  signal.addEventListener('abort', () => controller.abort());
  return controller;
}

/**
 * 暂停执行指定的毫秒数。
 * @param ms 暂停的毫秒数。
 * @returns 返回一个Promise，在指定的毫秒数后解析。
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 获取业务模型名称。
 * @param modelName - 模型名称。
 * @returns 业务模型名称或原模型名称。
 */
export function getInnerModelNameToBusiness(modelName) {
  try {
    if (isBusiness()) {
      return modelName.replace('-Base', '');
    }
  } catch (error) {}
  return modelName;
}
/**
 * 处理代码补全接受逻辑。
 * @param {Object} params - 参数对象。
 * @param {Object} params.reporter - 上报工具。
 * @param {Object} params.completionAccept - 补全接受对象。
 * @param {string} params.insertText - 插入的文本。
 * @param {Object} params.vsContext - VS Code 上下文。
 * @param {string} params.completionAcceptText - 补全接受文本。
 * @param {string} params.textBeforeCursor - 光标前的文本。
 */
export function setCompletionAccept({
  reporter,
  completionAccept,
  insertText,
  vsContext,
  textBeforeCursor,
  isChat,
  chatModel,
}) {
  const editor = vscode.window.activeTextEditor;
  if (!editor) return;
  const cursorPosition = editor.selection.active;
  acceptDisposable && acceptDisposable.dispose();
  acceptDisposable = vscode.commands.registerCommand('JoyCode.completion.formatAccept', () => {
    vscode.commands.executeCommand('JoyCode.completion.get-new-completion');
    const codeFormatting = getRemoteConfigSync().codeFormatting;
    const userSetFormat = getVscodeConfig('JoyCode.config.codeCompletionsFormat');
    if (codeFormatting || userSetFormat) {
      formatCompletion({
        document: editor.document,
        position: cursorPosition,
        currentLinePrefix: textBeforeCursor,
        insertText,
      });
    }
    // TODO: 根据用户选择的index，上报对应的acceptIndex（暂时仅返回一个推荐代码，默认0）
    vsContext.globalState.update('JoyCode.hideTip', true);
    vsContext.globalState.update('JoyCode.rejectInlineCompletion.number', 0); // 采纳后将不采纳次数重置
    insertText &&
      reporter.success({ result: insertText, acceptIndex: 0 }, async () => {
        completionAccept.acceptTotal += 1;
        completionAccept.requestList.push(1);
        completionAccept.requestList = completionAccept.requestList.slice(-10);
        completionAccept.aiLength += `${insertText}`.length;
        if (!isBusiness()) {
          Logger.info('completionAccept', JSON.stringify(completionAccept));
        }
        const gptCompletionModel: string = getInnerModelNameToBusiness(
          (await vsContext.globalState.get('JoyCode.gptCompletionModel')) || 'JoyCode-Base-Lite'
        );
        const model = isChat ? chatModel : gptCompletionModel;
        AdoptResultCache.setRemote(insertText, model, AdoptResultCache.ADOPT_CODE_SOURCE.COMPLETION);
      });
  });
}

interface ShouldCancelBasedOnCurrentLineParams {
  currentLinePrefix: string;
  currentLineSuffix: string;
  position: vscode.Position;
  document: vscode.TextDocument;
}

/**
 * 判断是否应基于当前行取消补全。
 * @param params - 包含当前行前缀、后缀、光标位置和文档的参数。
 * @returns 如果应取消补全则返回 true，否则返回 false。
 */
export function shouldCancelBasedOnCurrentLine(params: ShouldCancelBasedOnCurrentLineParams): boolean {
  const { currentLinePrefix, currentLineSuffix, position, document } = params;

  // 如果在光标所在的同一行有后缀，并且后缀包含任何单词字符，则不要尝试进行补全。
  // 这意味着我们只在同一行的后缀是特殊字符（如 `)]}` 等）时才进行补全。
  // VS Code 会尝试通过字符合并当前行的剩余部分，但对于单词来说，这很容易变得非常混乱。
  if (/\w/.test(currentLineSuffix)) {
    return true;
  }

  // 当最后一个字符是闭合符号时不要触发补全。
  if (/[);\]}]$/.test(currentLinePrefix.trim())) {
    return true;
  }

  // 当光标位于文件末尾行的开头且上一行为空时，不要触发
  if (position.line !== 0 && position.line === document.lineCount - 1) {
    const lineAbove = Math.max(position.line - 1, 0);

    if (document.lineAt(lineAbove).isEmptyOrWhitespace && !position.character) {
      return true;
    }
  }

  return false;
}

/**
 * 将插入字符串修剪到与后缀字符串匹配的第一行。
 * 这是为了将 Claude 的补全“适配”回我们正在修改的代码中。
 * 通常情况下，补全的最后几行可能会与后缀（光标后面的代码）匹配。
 * 根据后缀修剪插入字符串。
 * @param insertion 要插入的字符串
 * @param prefix 前缀字符串
 * @param suffix 后缀字符串
 * @param languageId 语言ID
 * @returns 修剪后的字符串
 */
export function trimUntilSuffix(insertion: string, prefix: string, suffix: string, languageId: string): string {
  const config = getLanguageConfig(languageId);

  insertion = insertion.trimEnd();

  const firstNonEmptySuffixLine = getFirstNonEmptyLine(suffix);

  // 处理内联后缀的情况 - 如果后缀中已经有相同的序列，则从插入中删除相同的尾随序列
  if (firstNonEmptySuffixLine.length === 0) {
    return insertion;
  }

  const prefixLastNewLine = prefix.lastIndexOf('\n');
  const prefixIndentationWithFirstCompletionLine = prefix.slice(prefixLastNewLine + 1);
  const suffixIndent = indentation(firstNonEmptySuffixLine);
  const startIndent = indentation(prefixIndentationWithFirstCompletionLine);
  const hasEmptyCompletionLine = prefixIndentationWithFirstCompletionLine.trim() === '';

  const insertionLines = insertion.split('\n');
  let cutOffIndex = insertionLines.length;

  for (let i = insertionLines.length - 1; i >= 0; i--) {
    let line = insertionLines[i];

    if (line.length === 0) {
      continue;
    }

    // 在第一行中包含前缀的当前缩进
    if (i === 0) {
      line = prefixIndentationWithFirstCompletionLine + line;
    }

    const lineIndentation = indentation(line);
    const isSameIndentation = lineIndentation <= suffixIndent;

    if (
      hasEmptyCompletionLine &&
      config?.blockEnd &&
      line.trim().startsWith(config.blockEnd) &&
      startIndent === lineIndentation &&
      insertionLines.length === 1
    ) {
      cutOffIndex = i;
      break;
    }

    if (isSameIndentation && firstNonEmptySuffixLine.startsWith(line)) {
      cutOffIndex = i;
      break;
    }
  }

  return insertionLines.slice(0, cutOffIndex).join('\n');
}

/**
 * 获取字符串中第一个非空行。
 * @param suffix - 输入字符串。
 * @returns 第一个非空行，若无则返回空字符串。
 */
function getFirstNonEmptyLine(suffix: string): string {
  const nextLineSuffix = suffix.slice(suffix.indexOf('\n'));

  for (const line of nextLineSuffix.split('\n')) {
    if (line.trim().length > 0) {
      return line;
    }
  }

  return '';
}

/**
 * 在第一个换行符之前去除空白字符（如果存在的话）。
 * 去除字符串中直到换行符的前导空白字符
 * @param str - 输入字符串
 * @returns 去除前导空白字符后的字符串
 */
export function trimLeadingWhitespaceUntilNewline(str: string): string {
  return str.replace(/^\s+?(\r?\n)/, '$1');
}

/**
 * 折叠出现在前缀末尾和补全开始处的空白。
 * 例如，如果前缀是 `const isLocalhost = window.location.host `，补全是 ` === 'localhost'`，
 * 它会修剪补全中的前导空格以避免重复空格。
 * 这里需要特定语言的自定义以获得更高的准确性。
 * 删除重复的前导空白字符。
 * @param prefix - 前缀字符串。
 * @param completion - 需要处理的字符串。
 * @returns 处理后的字符串。
 */
export function collapseDuplicativeWhitespace(prefix: string, completion: string): string {
  if (prefix.endsWith(' ') || prefix.endsWith('\t')) {
    completion = completion.replace(/^[\t ]+/, '');
  }
  return completion;
}

/**
 * 移除文本中每行末尾的空白字符。
 * @param text - 输入的文本字符串。
 * @returns 处理后的文本字符串。
 */
export function removeTrailingWhitespace(text: string): string {
  return text
    .split('\n')
    .map((l) => l.trimEnd())
    .join('\n');
}

const INDENTATION_REGEX = /^[\t ]*/;
export const OPENING_BRACKET_REGEX = /([([{])$/;
export const FUNCTION_OR_METHOD_INVOCATION_REGEX = /\b[^()]+\((.*)\)$/g;
export const FUNCTION_KEYWORDS = /^(function|def|fn)/g;

export const BRACKET_PAIR = {
  '(': ')',
  '[': ']',
  '{': '}',
  '<': '>',
} as const;
export type OpeningBracket = keyof typeof BRACKET_PAIR;

/**
 * 获取编辑器的Tab大小。
 * @returns {number} 编辑器的Tab大小，如果未激活编辑器则返回2。
 */
export function getEditorTabSize(): number {
  return vscode.window.activeTextEditor ? (vscode.window.activeTextEditor.options.tabSize as number) : 2;
}

/**
 * 计算行首的空格或制表符数量。
 * 由于Cody有时会混合使用制表符和空格进行响应，此函数首先使用当前启用的tabSize选项来规范化空白字符。
 * 计算行的缩进级别。
 * @param line - 要计算缩进的行。
 * @returns 缩进级别的数值。
 */
export function indentation(line: string): number {
  const tabSize = getEditorTabSize();

  const regex = line.match(INDENTATION_REGEX);
  if (regex) {
    const whitespace = regex[0];
    return [...whitespace].reduce((p, c) => p + (c === '\t' ? tabSize : 1), 0);
  }

  return 0;
}

/**
 * 如果补全以一个开括号开始，并且后缀以对应的闭括号开始，
 * 我们会包含补全的最后一个闭括号。
 * 例如：function foo(__CURSOR__)
 * 我们可以这样做，因为我们知道现有的代码块已经闭合，
 * 这意味着新的代码块需要单独闭合。
 * 例如：function foo() { console.log('hello') }
 * 根据括号判断是否应包含结束行。
 * @param prefixIndentationWithFirstCompletionLine - 前缀缩进和首行完成。
 * @param suffix - 后缀字符串。
 * @returns 是否应包含结束行。
 */
function shouldIncludeClosingLineBasedOnBrackets(
  prefixIndentationWithFirstCompletionLine: string,
  suffix: string
): boolean {
  const matches = prefixIndentationWithFirstCompletionLine.match(OPENING_BRACKET_REGEX);

  if (matches && matches.length > 0) {
    const openingBracket = matches[0] as keyof typeof BRACKET_PAIR;
    const closingBracket = BRACKET_PAIR[openingBracket];

    return Boolean(openingBracket) && suffix.startsWith(closingBracket);
  }

  return false;
}

/**
 * 仅在块为空且块已经关闭时才包含结束行（例如 `}`）。
 * 我们通过查看下一个非空行的缩进来检测这一点。
 * 判断是否应包含结束行。
 * @param prefixIndentationWithFirstCompletionLine - 前缀缩进和第一行完成行。
 * @param suffix - 后缀内容。
 * @returns 是否应包含结束行。
 */
export function shouldIncludeClosingLine(prefixIndentationWithFirstCompletionLine: string, suffix: string): boolean {
  const includeClosingLineBasedOnBrackets = shouldIncludeClosingLineBasedOnBrackets(
    prefixIndentationWithFirstCompletionLine,
    suffix
  );

  const startIndent = indentation(prefixIndentationWithFirstCompletionLine);

  const nextNonEmptyLine = getNextNonEmptyLine(suffix);

  return indentation(nextNonEmptyLine) < startIndent || includeClosingLineBasedOnBrackets;
}

/**
 * 获取文本的第一行。
 * @param text - 输入的文本字符串。
 * @returns 文本的第一行内容。
 */
export function getFirstLine(text: string): string {
  const firstLf = text.indexOf('\n');
  const firstCrLf = text.indexOf('\r\n');
  // 没有换行符
  if (firstLf === -1 && firstCrLf === -1) {
    return text;
  }

  return text.slice(0, firstCrLf >= 0 ? firstCrLf : firstLf);
}

/**
 * 获取文本的最后一行。
 * @param text - 输入的文本字符串。
 * @returns 文本的最后一行。
 */
export function getLastLine(text: string): string {
  const lastLf = text.lastIndexOf('\n');
  const lastCrLf = text.lastIndexOf('\r\n');
  // 没有换行符
  if (lastLf === -1 && lastCrLf === -1) {
    return text;
  }

  return text.slice(lastCrLf >= 0 ? lastCrLf + 2 : lastLf + 1);
}

/**
 * 获取字符串中下一行非空行。
 * @param suffix - 输入字符串。
 * @returns 下一行非空行，若无则返回空字符串。
 */
export function getNextNonEmptyLine(suffix: string): string {
  const nextLf = suffix.indexOf('\n');
  const nextCrLf = suffix.indexOf('\r\n');
  // 当前没有下一行
  if (nextLf === -1 && nextCrLf === -1) {
    return '';
  }
  return lines(suffix.slice(nextCrLf >= 0 ? nextCrLf + 2 : nextLf + 1)).find((line) => line.trim().length > 0) ?? '';
}

/**
 * 获取前一个非空行的索引。
 * @param prefix - 输入字符串。
 * @returns 前一个非空行的索引或null。
 */
export function getPrevNonEmptyLineIndex(prefix: string): number | null {
  const prevLf = prefix.lastIndexOf('\n');
  const prevCrLf = prefix.lastIndexOf('\r\n');
  // 当前没有上一行
  if (prevLf === -1 && prevCrLf === -1) {
    return null;
  }

  return prevCrLf >= 0 ? prevCrLf : prevLf;
}

/**
 * 获取前一个非空行的内容。
 * @param prefix - 输入的字符串前缀。
 * @returns 前一个非空行的内容，如果没有则返回空字符串。
 */
export function getPrevNonEmptyLine(prefix: string): string {
  const prevNonEmptyLineIndex = getPrevNonEmptyLineIndex(prefix);
  if (prevNonEmptyLineIndex === null) {
    return '';
  }

  return findLast(lines(prefix.slice(0, prevNonEmptyLineIndex)), (line) => line.trim().length > 0) ?? '';
}
/**
 * 查找两个字符串之间的最大后缀前缀重叠部分
 * @param left - 左侧字符串
 * @param right - 右侧字符串
 * @returns 最大重叠部分或null
 */
export function findLargestSuffixPrefixOverlap(left: string, right: string): string | null {
  let overlap = '';

  for (let i = 1; i <= Math.min(left.length, right.length); i++) {
    const suffix = left.slice(left.length - i);
    const prefix = right.slice(0, i);

    if (suffix === prefix) {
      overlap = suffix;
    }
  }

  if (overlap.length === 0) {
    return null;
  }

  return overlap;
}

/**
 * 在补全文本中插入缺失的闭合括号。
 * 这处理了缺失括号导致不完整解析树中断的情况。
 * 插入缺失的括号
 * @param text - 输入的字符串
 * @returns 补全括号后的字符串
 */
export function insertMissingBrackets(text: string): string {
  const openingStack: OpeningBracket[] = [];
  const bracketPairs = Object.entries(BRACKET_PAIR);

  for (const char of text) {
    const bracketPair = bracketPairs.find(([_, closingBracket]) => closingBracket === char);

    if (bracketPair) {
      if (openingStack.length > 0 && openingStack.at(-1) === bracketPair[0]) {
        openingStack.pop();
      }
    } else if (Object.keys(BRACKET_PAIR).includes(char)) {
      openingStack.push(char as OpeningBracket);
    }
  }

  return (
    text +
    openingStack
      .reverse()
      .map((openBracket) => BRACKET_PAIR[openBracket])
      .join('')
  );
}
