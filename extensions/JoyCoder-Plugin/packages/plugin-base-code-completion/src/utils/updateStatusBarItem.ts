import * as vscode from 'vscode';
import * as os from 'os';
import { GlobalState, isBusiness } from '@joycoder/shared';

let statusbartimer: NodeJS.Timeout = null as unknown as NodeJS.Timeout;
export const statusBarText = isBusiness() ? 'JoyCode' : '代码补全';

export async function updateStatusBarItem(
  myStatusBarItem: vscode.StatusBarItem,
  g_isLoading: boolean,
  isLoading: boolean,
  info: string
): Promise<void> {
  myStatusBarItem.show();
  if (statusbartimer) {
    clearTimeout(statusbartimer);
  }
  if (isLoading) {
    g_isLoading = true;
    myStatusBarItem.text = `$(sync~spin)` + info;
  } else {
    g_isLoading = false;
    // 开启预测补全-命令模式
    const enableCompletionCommand = GlobalState.get('enableCompletionCommand');
    const text = enableCompletionCommand ? '$(completion-icon)' : '$(completion-disabled-icon)';
    const platform = os.platform();
    if (enableCompletionCommand) {
      myStatusBarItem.text = `${text} ` + info;
    } else {
      myStatusBarItem.text = `${text} ⇧ + ${platform == 'win32' ? '⎇ ' : '⌥'} + → 手动`;
    }
    statusbartimer = setTimeout(() => {
      myStatusBarItem.text = `${text} ${
        enableCompletionCommand ? statusBarText : `⇧ + ${platform == 'win32' ? '⎇ ' : '⌥'} + → 手动`
      }`;
    }, 20000);
  }
}
