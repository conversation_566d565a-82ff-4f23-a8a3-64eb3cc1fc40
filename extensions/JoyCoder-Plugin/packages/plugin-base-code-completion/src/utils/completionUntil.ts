import * as vscode from 'vscode';
import { Logger, getVscodeConfig } from '@joycoder/shared';
import analyzeDependencies, {
  getALLOpenDocumentsRelevantSnippet,
  getRelevantSnippetFunctions,
  getSameFolderRelevantSnippet,
} from './analyzeDependencies';
import { hasCommentMarker, parseImportLines } from './elidableText';
import { getSwitchLanguageId } from './codeLensProcider';
import { getImportedFiles } from './codeClipboard';
const maxFilesNumber = 3;
const supportLanguage = ['typescript', 'typescriptreact', 'python', 'javascript', 'typescriptreact', 'go', 'java'];
const INDENTATION_REGEX = /^[\t ]*/;
export const OPENING_BRACKET_REGEX = /([([{])$/;
export const FUNCTION_OR_METHOD_INVOCATION_REGEX = /\b[^()]+\((.*)\)$/g;
export const FUNCTION_KEYWORDS = /^(function|def|fn)/g;

export const BRACKET_PAIR = {
  '(': ')',
  '[': ']',
  '{': '}',
  '<': '>',
} as const;

// 判断当前光标所在行是否为空行
export function middleOfLineWontComplete(editor, document) {
  const cursorPosition = editor.selection.active;
  const currentLine = document?.lineAt(cursorPosition.line);
  const lineEndPosition = currentLine?.range.end;
  const selectionTrailingString: vscode.Selection = new vscode.Selection(
    cursorPosition.line,
    cursorPosition.character,
    cursorPosition.line,
    lineEndPosition.character + 1
  );
  const trailingString = document.getText(selectionTrailingString);
  const re = /^[\]\{\}\); \n\r\t\'\"]*$/;
  const reg = /^\s*[)}\]"'`]*\s*[:{;,]?\s*$/.test(trailingString);
  if (re.test(trailingString) || reg) {
    return false;
  } else {
    return true;
  }
}

// 判断光标是否在当前行的中间位置
export function isAtTheMiddleOfLine(editor: any, document: any) {
  const cursorPosition = editor.selection.active;
  const currentLine = document?.lineAt(cursorPosition.line);
  const lineEndPosition = currentLine?.range.end;

  const selectionTrailingString: vscode.Selection = new vscode.Selection(
    cursorPosition.line,
    cursorPosition.character,
    cursorPosition.line,
    lineEndPosition.character + 1
  );
  const trailingString = document.getText(selectionTrailingString);
  const trimmed = trailingString.trim();
  return trimmed.length !== 0;
}
// 根据给定的字符串和替换字符串，移除字符串末尾的字符，直到字符串末尾的字符与替换字符串中的字符匹配为止。
export function removeTrailingCharsByReplacement(completion: string, replacement: string) {
  for (let x = 0; x < replacement.length; x++) {
    const ch = replacement[x];
    if (!isBracketBalanced(completion, ch)) {
      completion = replaceLast(completion, ch, '');
    }
  }
  return completion;
}
// 替换字符串中最后一个指定子串为新的子串
export function replaceLast(str: string, toReplace: string, replacement: string) {
  const pos = str.lastIndexOf(toReplace);
  if (pos > -1) {
    return str.substring(0, pos) + replacement + str.substring(pos + toReplace.length);
  } else {
    return str;
  }
}
// 判断字符串中是否有匹配的括号
export function isBracketBalanced(str: string, character: string) {
  let count = 0;
  for (let x = 0; x < str.length; x++) {
    const ch = str[x];
    if (ch === character) {
      count++;
    }
    if (
      (character === '{' && ch === '}') ||
      (character === '[' && ch === ']') ||
      (character === '(' && ch === ')') ||
      (character === '}' && ch === '{') ||
      (character === ']' && ch === '[') ||
      (character === ')' && ch === '(')
    ) {
      count--;
    }
  }
  return count === 0;
}

/**
 * 获取上下文信息
 * @param document 当前文档
 * @param prevCursor 上一个光标位置
 * @param nextCursor 下一个光标位置
 * @returns 包含代码片段文本、导入上下文文本、同文件夹文件文本的对象
 */
export async function getContextInfo({ document, prevCursor, nextCursor }) {
  const codeCompletionsMoreContext = getVscodeConfig('JoyCode.config.codeCompletionsMoreContext');
  let relatedFiles: { file: string; content: string; score?: number }[] = [];
  if (!codeCompletionsMoreContext) {
    return relatedFiles;
  }
  //分析上文依赖
  const languageId = getSwitchLanguageId(document);
  const folderPath = vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
  let importFiles: any = [];
  importFiles = await analyzeDependencies(document, folderPath);
  if (Array.isArray(importFiles) && importFiles.length > 0) {
    importFiles = importFiles.slice(0, maxFilesNumber);
    relatedFiles = relatedFiles.concat(importFiles);
  } else {
    try {
      const documentText = document.getText();
      const filePath = document.uri.fsPath;
      importFiles = await getImportedFiles({ documentText, language: document.languageId, filePath });
      if (importFiles?.length > 0) {
        importFiles = importFiles.map((item) => {
          return {
            file: item.fileName,
            content: item.fileContent,
            score: 0,
          };
        });
        relatedFiles = relatedFiles.concat(importFiles);
      }
    } catch (error) {}
  }
  let snippetsOpenFiles: any = [];
  let sameFolderFiles: any = [];
  const targetText = prevCursor + nextCursor;
  if (supportLanguage.includes(document.languageId)) {
    // 算法判定这些文件和当前光标上下文的关联，从而给到模型进行排名和相似度计算。
    const snippetsList = await getALLOpenDocumentsRelevantSnippet(targetText);
    const firstThreeSnippets =
      snippetsList.length <= maxFilesNumber ? snippetsList : snippetsList.slice(0, maxFilesNumber);
    snippetsOpenFiles = await Promise.all(
      firstThreeSnippets.map(async (snippet) => {
        if (snippet.score <= 0) {
          return null;
        }
        let fnNodes = await getRelevantSnippetFunctions({
          language: languageId,
          targetText,
          docText: snippet.snippetText,
        });
        const fnNodesLength = fnNodes.length > maxFilesNumber ? maxFilesNumber : fnNodes.length;
        fnNodes = fnNodes.sort((a, b) => (b?.score ?? 0) - (a?.score ?? 0)).slice(0, fnNodesLength);
        const content = fnNodes.map((item) => item.snippetText).join('\n');
        const file = {
          file: snippet.fileName,
          content: content,
          score: snippet.score,
        };
        return file;
      })
    );
    snippetsOpenFiles = snippetsOpenFiles.filter((item) => item && item?.score > 0);
    const snippetsFileList = await getSameFolderRelevantSnippet(document, targetText);
    const firstThreeFileSnippets =
      snippetsFileList.length <= maxFilesNumber ? snippetsFileList : snippetsFileList.slice(0, maxFilesNumber);
    sameFolderFiles = await Promise.all(
      firstThreeFileSnippets.map(async (snippet) => {
        if (snippet.score <= 0) {
          return null;
        }
        let fnNodes = await getRelevantSnippetFunctions({
          language: languageId,
          targetText,
          docText: snippet.snippetText,
        });
        const fnNodesLength = fnNodes.length > maxFilesNumber ? maxFilesNumber : fnNodes.length;
        fnNodes = fnNodes.sort((a, b) => (b?.score ?? 0) - (a?.score ?? 0)).slice(0, fnNodesLength);
        const content = fnNodes.map((item) => item.snippetText).join('\n');
        const file = {
          file: snippet.fileName,
          content,
          score: snippet.score,
        };
        return file;
      })
    );
    sameFolderFiles = sameFolderFiles.filter((item) => item && item?.score > 0);
    relatedFiles = relatedFiles.concat(snippetsOpenFiles);
    relatedFiles = relatedFiles.concat(sameFolderFiles);
  }
  relatedFiles = relatedFiles
    .sort((a, b) => (b?.score ?? 0) - (a?.score ?? 0))
    .map((item) => {
      if (item.file) {
        return {
          file: item.file,
          content: item.content,
        };
      }
      return {} as {
        file: string;
        content: string;
      };
    })
    .filter((item) => {
      const keys = Object.keys(item);
      return keys.length > 0;
    });
  return relatedFiles;
}

export function clearDecoration(decorationFn, activeEditor?: vscode.TextEditor) {
  activeEditor = activeEditor ?? vscode.window.activeTextEditor;
  if (decorationFn && activeEditor) {
    activeEditor.setDecorations(decorationFn, []);
    decorationFn.dispose();
  }
}

/**
 * 获取当前光标所在行的文本。
 * @param editor - 编辑器实例。
 * @param document - 文档实例。
 * @returns 当前行的文本。
 */
export function getCurrentLineText(editor, document) {
  const cursorPosition = editor.selection.active;
  const thisNextChar: vscode.Selection = new vscode.Selection(
    cursorPosition.line,
    0,
    cursorPosition.line,
    cursorPosition.character
  );
  const thisLine = document.getText(thisNextChar);
  return thisLine;
}

/**
 * 获取光标所在位置前的上下文文本。 默认取前6行
 * @param editor - 编辑器实例。
 * @param document - 文档实例。
 * @param prevNum - 向前获取的行数，默认为6。
 * @returns 光标前的文本内容。
 */
export function getPrevContext(editor, document, prevNum = 6) {
  const cursorPosition = editor.selection.active;
  const startLine = cursorPosition.line - prevNum > 0 ? cursorPosition.line - prevNum : 0; // 当前行的前6行
  const selection: vscode.Selection = new vscode.Selection(startLine, 0, cursorPosition.line, cursorPosition.character);
  const textBeforeCursor = document.getText(selection);
  return textBeforeCursor;
}

/**
 * 获取光标位置后指定行数的文本内容。取后6行
 * @param editor - 编辑器实例。
 * @param document - 文档实例。
 * @param prevNum - 获取的行数，默认为6。
 * @returns 光标位置后指定行数的文本内容。
 */
export function getNextContext(editor, document, prevNum = 6) {
  const lineCount = editor.document.lineCount;
  const cursorPosition = editor.selection.active;
  if (cursorPosition.line === lineCount) {
    return '';
  }
  const endLine = cursorPosition.line + prevNum <= lineCount ? cursorPosition.line + prevNum : lineCount; // 当前行的前6行
  const selection: vscode.Selection = new vscode.Selection(
    cursorPosition.line + 1,
    0,
    endLine,
    cursorPosition.character
  );
  const textBeforeCursor = document.getText(selection);
  return textBeforeCursor;
}
/**
 * 获取当前光标所在行的下一行数据。
 * 如果当前行是最后一行，则返回null。
 *
 * @returns {string | null} 下一行的文本数据或null
 */
export function getNextLineData() {
  const editor = vscode.window.activeTextEditor;
  if (!editor) {
    return '';
  }
  const document = editor.document;
  const cursorPosition = editor.selection.active;
  const currentLine = cursorPosition.line;
  if (currentLine < document.lineCount - 1) {
    const nextLine = document.lineAt(currentLine + 1);
    const nextLineText = nextLine.text;
    return nextLineText;
  } else {
    return '';
  }
}
// 根据出参设置补全
export function getInsertText(insertText, nextCursor, textBeforeCursor, thisLine, languageId) {
  // const containsChinese = /[\u4E00-\u9FA5]/; // 匹配包含中文字符的正则表达式
  if (insertText.includes(thisLine.trim())) {
    insertText = insertText.replace(thisLine.trim(), '');
    if (insertText.split('\n').length <= 2) {
      insertText = nextCursor ? insertText.trimEnd() : insertText;
    }
  } else if (hasCommentMarker({ source: textBeforeCursor.trimStart(), languageId })) {
    // &&containsChinese.test(textBeforeCursor)
    insertText = insertText.replace(thisLine.trim(), '');
    const startChinese = /^[\u4E00-\u9FA5]/; // 匹配以中文字符开始的正则表达式
    if (startChinese.test(insertText)) {
      insertText = insertText.trimEnd();
      // insertText = insertText.trim();
    }
    //   else {
    //     insertText = '\n' + insertText + '\n';
    //   }
    // } else if (insertText[insertText.length - 1] != '\n') {
    //   insertText = insertText + '\n';
  } else if (nextCursor && !hasCommentMarker({ source: thisLine.trimStart(), languageId })) {
    insertText = insertText.trimEnd();
  }
  return insertText;
}

/**
 * 检查是否应显示文本
 * @param insertText - 要检查的文本
 * @returns 是否应显示文本
 */
export function shouldDisplayText(insertText) {
  const chineseRegex = /^[\u4E00-\u9FA5\uFF00-\uFFEF\u3000-\u303F\uFF01-\uFF20\uFF3B-\uFF40\uFFE0-\uFFE5]+$/; // 匹配值只有中文
  const cleanedText = insertText.replace(/\n/g, '');

  // 检查是否包含特定的中文短语
  const containsApology = insertText.includes('抱歉') && insertText.includes('无法');
  // const startsWithChinese = chineseRegex.test(insertText.substring(0, 5));
  const containsGatewayError = insertText.includes('集团网关错误信息') || insertText.includes('1039');

  if (chineseRegex.test(cleanedText)) {
    if (containsApology) {
      return false;
    }
  } else if (containsGatewayError) {
    return false;
  }

  return true;
}

/**
 * 处理插入文本的函数。
 * @param {Object} params - 参数对象。
 * @param {string} params.insertText - 要插入的文本。
 * @param {string} params.textBeforeCursor - 光标前的文本。
 * @param {string} params.thisLine - 当前行的文本。
 * @param {number} params.nextCursor - 下一个光标位置。
 * @param {Object} params.document - 文档对象。
 * @param {Object} params.cursorPosition - 光标位置对象。
 * @returns {string} 处理后的插入文本。
 */
export async function processInsertText({
  insertText,
  textBeforeCursor,
  thisLine,
  nextCursor,
  document,
  cursorPosition,
}) {
  if (!insertText) return '';
  if (!shouldDisplayText(insertText)) {
    return '';
  }
  insertText = getInsertText(insertText, nextCursor, textBeforeCursor, thisLine, document.languageId);
  Logger.log('%c [ insertText ]-392', 'font-size:13px; background:pink; color:#bf2c9f;', insertText);
  const currentLine = cursorPosition.line;
  if (currentLine > 10 && parseImportLines(insertText).length > 0) {
    return '';
  }
  try {
    const completionLines = insertText.split('\n')?.length;
    if (hasCommentMarker({ source: insertText.trim(), languageId: document.languageId }) && completionLines <= 2) {
      return '';
    }
  } catch (error) {
    return '';
  }

  return insertText;
}

/**
 * 将文本按行分割成字符串数组。
 * @param text - 输入的文本字符串。
 * @returns 分割后的字符串数组。
 */
export function lines(text: string): string[] {
  return text.split(/\r?\n/);
}

export function hasCompleteFirstLine(text: string): boolean {
  const lastNewlineIndex = text.indexOf('\n');
  return lastNewlineIndex !== -1;
}

export function lastNLines(text: string, n: number): string {
  const lines = text.split('\n');
  return lines.slice(Math.max(0, lines.length - n)).join('\n');
}

export function removeIndentation(text: string): string {
  const lines = text.split('\n');
  return lines.map((line) => line.replace(INDENTATION_REGEX, '')).join('\n');
}

/**
 * 检查当前编辑器内容是否匹配弹出项
 * 检查编辑器中当前的文本是否与补全中当前选中的项目重叠。
   如果不重叠，VS Code 将永远不会显示内联补全。
    以下是如何触发这种情况的示例：
   1. 在 TypeScript 文件中输入文本 `console.l`。
   2. 使用方向键导航到以不同字母开头的建议方法，比如 `console.dir`。
   3. 由于在编辑器中已有 `.l` 的情况下不可能渲染以 `.dir` 开头的建议，VS Code 永远不会渲染它。
 * @param document - VS Code文本文档
 * @param context - 内联补全上下文
 * @returns 是否匹配
 */
export function currentEditorContentMatchesPopupItem(
  document: vscode.TextDocument,
  context: vscode.InlineCompletionContext
): boolean {
  if (context.selectedCompletionInfo) {
    const currentText = document.getText(context.selectedCompletionInfo.range);
    const selectedText = context.selectedCompletionInfo.text;

    if (!selectedText.startsWith(currentText)) {
      return false;
    }
  }
  return true;
}
