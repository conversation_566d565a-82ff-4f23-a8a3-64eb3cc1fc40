import * as vscode from 'vscode';
import path from 'path';
import * as fs from 'fs';
import analyzeDependencies, { getRelevantSnippet } from './analyzeDependencies';
import { parseTreeSitter, queryFunctions } from './elidableText';
import { getVscodeConfig } from '@joycoder/shared';

const maxLines = 140;
/**
 * 检测代码语言
 * @param code - 要检测的代码字符串
 * @returns 代码语言或null
 */
export function detectCodeLanguage(code: string): string | null {
  // 移除前后空白字符
  code = code.trim();
  // 定义各种语言的特征
  const languagePatterns: { [key: string]: RegExp } = {
    JavaScript: /^(var|let|const|function|class|import|export|async|await|document\.|window\.)/m,
    TypeScript: /^(interface|type|enum|namespace|abstract|implements|declare|import .* from '.*';)/m,
    Python: /^(def |class |import |from |if __name__ == ['"]__main__['"]:|print\()/m,
    Java: /^(public |private |protected |class |interface |enum |package |import )/m,
    C: /^(#include <.*>|int main\(|void main\()/m,
    'C++': /^(#include <iostream>|using namespace std;|class |template <)/m,
    'C#': /^(namespace \w+|class \w+ \{|using \w+;)/m,
    HTML: /^(<!DOCTYPE html>|<html|<head|<body|<div|<span|<a )/i,
    CSS: /^(@media|@keyframes|@font-face|[a-z-]+\s*{)/i,
    SQL: /^(SELECT |INSERT |UPDATE |DELETE |CREATE TABLE |ALTER TABLE )/i,
    PHP: /^(<\?php|namespace |use |class |function )/i,
    Ruby: /^(require |class |def |module |if __FILE__ == \\$0)/m,
    Swift: /^(import |class |struct |enum |protocol |extension |func )/m,
    Kotlin: /^(package |import |class |fun |val |var |object )/m,
    Go: /^(package |import |func |type |const |var )/m,
    Rust: /^(use |fn |struct |enum |impl |pub |mod )/m,
    JSX: /import React from 'react'|<\w+ \w+=/m,
    TSX: /import React from 'react'|import \{ \w+ \} from 'react'.*:\s*\w+\s*;/m,
  };
  // 遍历语言特征并检查匹配
  for (const [language, pattern] of Object.entries(languagePatterns)) {
    if (pattern.test(code)) {
      return language;
    }
  }
  // 如果没有匹配到任何语言，返回 null
  return null;
}

/**
 * 异步获取剪切板内容，如果内容是代码则返回，否则返回空字符串。
 * @returns 剪切板内容或空字符串
 */
export async function getClipboardContent() {
  const codeCompletionsMoreContext = getVscodeConfig('JoyCode.config.codeCompletionsMoreContext');
  if (!codeCompletionsMoreContext) {
    return '';
  }
  // 获取剪切板内容
  const clipboardContent = await vscode.env.clipboard.readText();
  if (!!detectCodeLanguage(clipboardContent)) {
    const lines = clipboardContent.split('\n');
    // 获取前XXX行
    const code = lines.slice(0, maxLines).join('\n').substring(0, 800);
    return `// Clipboard content：\n${code}\n // Clipboard content end.`;
  }
  return '';
}

/**
 * 获取修改文件的内容。
 * @param {string} completionContext - 补全上下文。
 * @returns {Promise<string>} 修改文件的内容。
 */
export async function getChangedFileContent(complementContext: string, document) {
  const codeCompletionsMoreContext = getVscodeConfig('JoyCode.config.codeCompletionsMoreContext');
  if (!codeCompletionsMoreContext) {
    return { changeFileContent: '', importFileContent: '' };
  }
  try {
    const changeFiles = await getChangedFiles(complementContext);
    const folderPath = vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
    let importFiles: any = (await analyzeDependencies(document, folderPath)) || [];
    const changeFileContent = changeFiles.map((file) => `\n// ${file.fileName}: \n ${file.fileContent} \n`).join('\n');
    let fileContent = `// Refer to the modified file:\n ${changeFileContent}`; // 打开过并修改的文件
    // // 将文件内容按行分割
    const lines = fileContent.split('\n');
    // 获取前XXX行
    fileContent = lines.slice(0, maxLines).join('\n');
    fileContent = fileContent.substring(0, 1000) + '\n//Refer to the modified file end.';
    // fileContent = fileContent + '\n//Refer to the modified file end.';
    try {
      if (importFiles.length === 0) {
        const documentText = document.getText();
        const language = document.languageId;
        const filePath = document.uri.fsPath;
        importFiles = await getImportedFiles({ documentText, language, filePath });
      }
    } catch (error) {}
    let importFileContent = importFiles.map((file) => `\n// ${file.fileName}: \n ${file.fileContent} \n`).join('\n');
    importFileContent = importFiles.length > 0 ? `// The imported files are as follows:\n ${importFileContent} \n` : '';
    // 将文件内容按行分割
    // const importLines = importFileContent.split('\n');
    // 获取前XXX行
    // importFileContent = importLines.slice(0, maxLines).join('\n') + '\n//The imported files end.';
    importFileContent = importFileContent + '\n//The imported files end.';
    return { changeFileContent: fileContent, importFileContent };
  } catch (error) {}
  return { changeFileContent: '', importFileContent: '' };
}
export function removeDuplicates(str1: string, str2: string, str3: string): string[] {
  // 将三个字符串合并成一个数组
  const combinedArray = [...str1, ...str2, ...str3];

  // 使用 Set 去重
  const uniqueSet = new Set(combinedArray);

  // 将 Set 转换回数组
  const uniqueArray = Array.from(uniqueSet);

  return uniqueArray;
}
/**
 * 获取所有打开的编辑器中有未保存更改的文件列表
 * @returns 包含未保存更改文件路径的字符串数组
 */
export async function getChangedFiles(complementContext: string): Promise<
  {
    fileName: string;
    fileContent: string;
    filPath: string;
    score: number;
  }[]
> {
  // 获取所有打开的文本编辑器
  const editors = vscode.window.visibleTextEditors;
  // 获取所有打开的文本编辑器
  const changedFiles = await Promise.all(
    editors
      .filter((editor) => editor.document.isDirty)
      .map(async (editor) => {
        const fileContent = editor.document.getText();
        let score = 0;
        try {
          const snippet = await getRelevantSnippet(fileContent, complementContext);
          score = snippet.score;
        } catch (error) {
          score = 0;
        }
        return {
          // 缺相似度算法
          fileName: editor.document.fileName,
          fileContent: editor.document.getText(),
          filPath: editor.document.uri.fsPath,
          score,
        };
      })
  );
  return changedFiles.sort((a, b) => b.score - a.score);
}

/**
 * 异步获取导入文件信息
 * @param {Object} params - 参数对象
 * @param {string} params.documentText - 当前文档内容
 * @param {string} params.targetText - 目标文本
 * @param {string} params.language - 编程语言
 * @param {string} params.filePath - 当前文件路径
 * @returns {Promise<Array>} 导入文件信息数组
 */
export async function getImportedFiles({ documentText, language, filePath }) {
  const tree = await parseTreeSitter(language, documentText);
  const imports: { fileName: string; fileContent: string; filePath: string; score: number }[] = [];
  // 遍历语法树，提取导入语句
  // 注意：这里需要根据不同语言的语法树结构来编写提取逻辑
  let captures: any = [];
  let captureNames: any = [];
  try {
    const queryNames = getImportQueryName(language, tree.getLanguage());
    const query = getImportQuery(language, tree.getLanguage());
    if (!query) {
      return [];
    }
    captures = query.captures(tree.rootNode);
    captureNames = queryNames.captures(tree.rootNode);
  } catch (error) {
    console.error('%c [ error ]-166', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
  const importFnNames: string[] = [];
  for (const capture of captureNames) {
    if (capture.name === 'importName') {
      importFnNames.push(capture.node.text);
    }
  }
  for (const capture of captures) {
    if (capture.name === 'importPath') {
      const importPath = capture.node.text.replace(/['"`]/g, ''); // 去除引号
      console.log('capture----------------》', importPath, capture);
      // 过滤非本地项目文件和第三方包引入文件
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        continue;
      }

      // 获取导入文件的绝对路径
      let importedFilePath = '';
      const fileDir = path.dirname(filePath);
      const fileExtension = path.extname(filePath).toLowerCase();
      try {
        importedFilePath = path.resolve(fileDir, importPath + fileExtension);
        if (!fs.existsSync(importedFilePath)) {
          // 尝试解析为目录下的 index 文件
          const newPath = path.join(importPath, 'index' + fileExtension);
          importedFilePath = path.resolve(fileDir, newPath);
          if (!fs.existsSync(importedFilePath)) continue;
        }
        console.log('[ importedFilePath ]-181---》', fs.existsSync(importedFilePath), importedFilePath);
      } catch (err) {
        console.warn(`无法解析导入路径: ${importPath} in ${filePath}-------->${importedFilePath}`);
        continue;
      }
      // 获取文件名
      const importedFileName = path.basename(importedFilePath);
      // 读取导入文件内容
      let importContent = '';
      try {
        importContent = fs.readFileSync(importedFilePath, 'utf8');
      } catch (err) {
        console.warn(`无法读取导入文件: ${importedFilePath}`);
        continue;
      }
      let snippetText = '';
      try {
        // const snippet = await getRelevantSnippet(importContent, targetText);
        if (importContent) {
          const tree = await parseTreeSitter(language, importContent);
          const fns = queryFunctions(language, tree.rootNode);
          for (let i = 0; i < fns.length; i++) {
            const fnCaptures = fns[i].captures.filter((node) => node.name === 'function');
            for (let j = 0; j < fnCaptures.length; j++) {
              const fnCode = fnCaptures[j].node;
              const functionName = fnCode.childForFieldName('name')?.text;
              if (importFnNames.includes(functionName)) {
                snippetText += `\n${fnCode.text}\n`;
              }
            }
          }
        }
        // snippetText = snippet?.snippetText ?? importContent;
      } catch (error) {}
      imports.push({
        fileName: importedFileName,
        fileContent: snippetText,
        filePath: importedFilePath,
        score: 0,
      });
    }
  }
  // 对结果进行排序
  imports.sort((a, b) => b.score - a.score);
  return imports;
}

function getImportQueryName(languageKey: string, LanguageModule: any): any {
  switch (languageKey) {
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
    case 'javascript':
    case 'javascriptreact':
    case 'typescript':
    case 'typescriptreact':
      return LanguageModule.query(`
        (
          import_statement
          (import_clause
            (named_imports
              (import_specifier
                name: (identifier) @importName
              )
            )
          )
        )
        (
          import_statement
          (import_clause
            (namespace_import
              (identifier) @importName
            )
          )
        )
        (
          import_statement
          (import_clause
            (identifier) @importName
          )
        )
        (
          import_statement
          source: (string) @importName
        )
        (
          call_expression
          function: (identifier) @function
          (#match? @function "^(require|import)$")
          arguments: (arguments (string) @importName)
        )
        (
          lexical_declaration
          (variable_declarator
            name: (identifier) @importName
            value: (call_expression
              function: (identifier) @function
              (#match? @function "^(require|import)$")
            )
          )
        )
      `);

    case 'py':
    case 'python':
      return LanguageModule.query(`
        (
          import_from_statement
          module_name: (dotted_name) @importName
        )
        (
          import_statement
          name: (dotted_name) @importName
        )
        (
          import_from_statement
          module_name: (relative_import) @importName
        )
        (
          import_from_statement
          name: (dotted_name) @importName
          (import_as_names
            (import_as_name
              name: (identifier) @importName
            )
          )
        )
        (
          import_from_statement
          name: (aliased_import
            name: (identifier) @importName
          )
        )
      `);

    case 'java':
      return LanguageModule.query(`
        (
          import_declaration
          name: (scoped_identifier) @importName
        )
        (
          package_declaration
          name: (identifier) @importName
        )
      `);

    case 'go':
      return LanguageModule.query(`
        (
          import_spec
          name: (identifier) @importName
        )
        (
          import_spec
          path: (interpreted_string_literal) @importName
        )
        (
          package_clause
          name: (package_identifier) @importName
        )
      `);

    case 'rs':
    case 'rust':
      return LanguageModule.query(`
        (
          use_declaration
          path: (use_path
            (identifier) @importName
          )
        )
        (
          use_declaration
          path: (scoped_use_list
            (identifier) @importName
          )
        )
        (
          use_declaration
          path: (use_path
            (scoped_identifier
              name: (identifier) @importName
            )
          )
        )
        (
          mod_item
          name: (identifier) @importName
        )
      `);

    default:
      console.warn(`未定义查询模式的语言：${languageKey}`);
      return null;
  }
}

function getImportQuery(languageKey: string, LanguageModule): any {
  // 根据语言定义查询模式
  let query: any = [];

  switch (languageKey) {
    case 'js':
    case 'jsx':
    case 'javascript':
    case 'javascriptreact':
    case 'ts':
    case 'tsx':
    case 'typescript':
    case 'typescriptreact':
      query = LanguageModule.query(`
        (import_statement
          (string) @importPath)
        (call_expression
          function: (identifier) @funcName
          arguments: (arguments (string (string_fragment) @importPath))
        )
      `);
      break;

    case 'py':
    case 'python':
      query = LanguageModule.query(`
        (import_from_statement
          module_name: (dotted_name) @importPath)
        (import_statement
          names: (dotted_as_names
            (dotted_as_name
              name: (dotted_name) @importPath)))
      `);
      break;

    case 'java':
      query = LanguageModule.query(`
        (import_declaration
          (scoped_identifier) @importPath)
      `);
      break;

    case 'rb':
    case 'ruby':
      query = LanguageModule.query(`
        (require_statement
          argument: (string) @importPath)
        (load_statement
          argument: (string) @importPath)
      `);
      break;

    case 'go':
      query = LanguageModule.query(`
        (import_spec
          path: (interpreted_string_literal) @importPath)
      `);
      break;

    case 'cpp':
    case 'c':
      query = LanguageModule.query(`
        (preproc_include
          (string_literal) @importPath)
        (preproc_include
          (system_lib_string) @importPath)
      `);
      break;

    case 'cs':
    case 'csharp':
      query = LanguageModule.query(`
        (using_directive
          (namespace_or_type_name) @importPath)
      `);
      break;

    case 'dart':
      query = LanguageModule.query(`
        (import_or_export
          uri: (string_literal) @importPath)
      `);
      break;

    case 'rs':
    case 'rust':
      query = LanguageModule.query(`
        (use_declaration
          (scoped_use_list
            (use_path) @importPath))
        (use_declaration
          (use_path) @importPath)
      `);
      break;

    // ...添加其他语言的查询模式

    default:
      console.warn(`未定义查询模式的语言：.${languageKey}`);
      return null; // 或者根据需要返回其他类型
  }

  return query;
}
