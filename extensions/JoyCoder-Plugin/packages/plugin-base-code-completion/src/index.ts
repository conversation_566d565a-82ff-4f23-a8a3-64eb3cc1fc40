import * as vscode from 'vscode';
import codeCompletionProvider from './codeCompletionProvider';
import CompletionCodeLensProvider from './utils/codeLensProcider';
import { updateStatusBarItem } from './utils/updateStatusBarItem';
import { completionModel } from './utils/constant';
import {
  getCompletionConfig,
  startReportAction,
  ActionType,
  isBusiness,
  codeModelConfigs,
  codeModelConfigsLocal,
  Logger,
  isBusinessLocal,
  ImmediateChangeConfiguration,
  setVscodeConfig,
  getPluginRunBaseConfig,
  getVscodeConfig,
  updateCodeCompletionsMoreContext,
} from '@joycoder/shared';
import { getInnerModelNameToBusiness } from './utils/util';
import { hookCodeStatistics } from './stats/codeStatManager';
export { stopCodeStatService } from './stats/codeStatService';

let vsDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let removeCodeLensDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let vsCodeLensDisposable: vscode.Disposable | null = new vscode.Disposable(() => {});
let commandDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let manuallyDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let newCompletionDisposable: vscode.Disposable = new vscode.Disposable(() => {});
const g_isLoading = false;
const defaultModel = getInnerModelNameToBusiness('JoyCode-Base-Lite');
let completionsStatusBarItem: vscode.StatusBarItem = null as unknown as vscode.StatusBarItem;
const COMPLETION_STATUS_TEXT = isBusiness() ? 'JoyCode' : '预测补全';

export default function (context: vscode.ExtensionContext) {
  try {
    commandDisposable && commandDisposable.dispose();
    commandDisposable = vscode.commands.registerCommand('JoyCode.code-completions', () => {
      if (vsDisposable) {
        vsDisposable.dispose();
      }
      context.globalState.update('enableCompletionCommand', true);
      updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
      context.globalState.update('JoyCode.rejectInlineCompletion.number', '0');
      setCompletionsInit(context);
      setGlobalState(context, 'JoyCode.enableCompletionCommand', '1');
      setDefaultModel(context);
    });
    const statusBar = setStatusBar(context, 'JoyCode.code-completions-setting', 100, COMPLETION_STATUS_TEXT);
    completionsStatusBarItem = statusBar;
    const statusCommand = vscode.commands.registerCommand('JoyCode.code-completions-setting', () => {
      setCompletionsPopup(context);
    });
    context.subscriptions.push(statusCommand);
    context.subscriptions.push(commandDisposable);
    manuallyTriggerCompletion(context); // 设置手动触发
    setStatusBarData(context);
    registerCodeLensProvider(context);
    setDefaultModel(context);
    hookCodeStatistics(context);
    switchConfigStatus(context);
    updateCodeCompletionsMoreContext(); // 跨文件感知同步后端数据
  } catch (error) {
    Logger.error('%c [ error ]-47', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

function setStatusBar(context: vscode.ExtensionContext, command?: string, right?: number, barText?: string) {
  const barItem: vscode.StatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, right);
  barItem.command = command; // 预测状态
  context.subscriptions.push(barItem);
  updateStatusBarItem(barItem, g_isLoading, false, barText || '');
  return barItem;
}
// 设置全局开启关闭
async function setGlobalState(context: vscode.ExtensionContext, key: string, status: string | number) {
  await context.globalState.update(key, status);
}
// 判断全局开启
async function setStatusBarData(context: vscode.ExtensionContext) {
  const enable: string | unknown = context.globalState.get('JoyCode.enableCompletionCommand');
  if (enable === '1') {
    vscode.commands.executeCommand('JoyCode.code-completions');
    updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    // "editor.suggest.insertMode": 默认值：insert；'insert'表示补全将在光标后插入文本，而不会覆盖光标后的文本。'replace'表示补全将替换光标后的文本。'smart'表示补全将根据上下文自动选择插入模式，根据光标位置和周围文本的情况来决定是插入文本还是替换文本。"
  } else {
    const remoteCompletionConfig = await getCompletionConfig();
    const switchFlag = remoteCompletionConfig?.switchFlag;
    if (switchFlag) {
      switchFlag && context.globalState.update('enableCompletionCommand', !!switchFlag);
      await setGlobalState(context, 'JoyCode.enableCompletionCommand', !!switchFlag ? '1' : '0');
      setCompletionsStatus(context, !!switchFlag);
    } else {
      setCompletionsInit(context);
    }
  }
}

// 设置开启状态
async function setCompletionsStatus(context: vscode.ExtensionContext, enableCompletionCommand: boolean) {
  // 接口 开启状态 上报
  const reporter = startReportAction({
    actionCate: 'ai',
    actionType: enableCompletionCommand ? ActionType.completionOpen : ActionType.completionClose,
  });
  // 开启
  if (completionsStatusBarItem && enableCompletionCommand) {
    vscode.commands.executeCommand('JoyCode.code-completions');
    updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    reporter.success({});
  } else if (completionsStatusBarItem && !enableCompletionCommand) {
    context.globalState.update('enableCompletionCommand', false);
    setCompletionsInit(context);
    updateStatusBarItem(completionsStatusBarItem, g_isLoading, false, COMPLETION_STATUS_TEXT);
    setGlobalState(context, 'JoyCode.enableCompletionCommand', '0');
    reporter.success({});
  }
}

async function setCompletionsPopup(context: vscode.ExtensionContext) {
  const enableCompletionCommand = await context.globalState.get('enableCompletionCommand');
  const completionStatus = enableCompletionCommand ? '开启预测' : '关闭预测';
  const items = [
    {
      label: '开启预测',
      description: '开启代码自动补全及注释转代码等功能',
    },
    {
      label: '关闭预测',
      description: '关闭代码自动补全及注释转代码等功能',
    },
    // {
    //   label: '模型选择',
    //   description: '模型选择设置，可以切换预测补全的大模型',
    // },
  ];
  const newCurrentItem = await getQuickPick(items, completionStatus);
  if (!newCurrentItem) return;
  if (newCurrentItem === '模型选择') {
    let codeModelConfig = isBusinessLocal() ? codeModelConfigsLocal : codeModelConfigs;
    if (!isBusiness()) {
      const codeModelRemote = await getPluginRunBaseConfig();
      // const codeModelRemote = getRemoteConfigSync().codeModelConfigs;
      codeModelConfig = codeModelRemote && codeModelRemote.length > 0 ? codeModelRemote : completionModel;
    }
    //默认使用模型
    const label = (await context.globalState.get('JoyCode.gptCompletionModel')) || defaultModel;
    const newCurrentGPT = (await getQuickPick(codeModelConfig, label)) || label;
    const newCurrentOptionArr = codeModelConfig.filter(
      (codeModelConfigItem) => codeModelConfigItem.label === newCurrentGPT
    );
    const newCurrentOption = Array.isArray(newCurrentOptionArr) ? newCurrentOptionArr[0] : null;
    context.globalState.update('JoyCode.gptCompletionModel', newCurrentGPT);
    //当前预测补全模型配置选项
    context.globalState.update('JoyCode.gptCompletionOption', newCurrentOption);
  } else if (newCurrentItem === '开启预测' || newCurrentItem === '关闭预测') {
    setCompletionsStatus(context, newCurrentItem === '开启预测');
  }
}
async function setDefaultModel(context) {
  setGlobalState(context, 'JoyCode.rejectInlineCompletion.number', '0'); // 每次重载重置初始化不采纳次数
  const cacheModel = await context.globalState.get('JoyCode.gptCompletionOption');
  // 所有的续写模型都要用JoyCoder-Base开头配置label
  let codeModelConfig = isBusinessLocal() ? codeModelConfigsLocal : codeModelConfigs;
  if (!isBusiness()) {
    const codeModelRemote = await getPluginRunBaseConfig();
    // const codeModelRemote = getRemoteConfigSync().codeModelConfigs;
    codeModelConfig = codeModelRemote && codeModelRemote.length > 0 ? codeModelRemote : completionModel;
  }
  if (cacheModel?.chat?.model === codeModelConfig[0]?.chat?.model) {
    return;
  }
  const modelOptionArr = codeModelConfig.filter((codeModelConfigItem) => codeModelConfigItem.label === defaultModel);
  const defaultModelOption = modelOptionArr[0] || completionModel[0];
  context.globalState.update('JoyCode.gptCompletionModel', defaultModelOption.label);
  context.globalState.update('JoyCode.gptCompletionOption', defaultModelOption);
}
async function getQuickPick(items, label, cb?: any) {
  const quickPick = vscode.window.createQuickPick();
  quickPick.items = items;
  quickPick.activeItems = quickPick.items.filter((item) => item.label == label);
  quickPick.placeholder = '预测补全开关和模型选择';
  quickPick.show();

  const newCurrentItem: string | undefined = await new Promise((resolve) => {
    quickPick.onDidAccept(() => resolve(quickPick.selectedItems[0].label));
    quickPick.onDidHide(() => resolve(undefined));
  });
  cb?.(quickPick, newCurrentItem);
  quickPick.dispose();
  return newCurrentItem;
}
// 添加装饰器
async function registerCodeLensProvider(context) {
  const enableMenus = getVscodeConfig('JoyCode.config.codeLens-row-menus');
  if (enableMenus) {
    vsCodeLensDisposable && vsCodeLensDisposable.dispose();
    CompletionCodeLensProvider.vsCodeLensInstance && CompletionCodeLensProvider.vsCodeLensInstance.disposeCodeLenses();
    const provider = new CompletionCodeLensProvider();
    vsCodeLensDisposable = provider.rerenderCodeLenses();
    CompletionCodeLensProvider.vsCodeLensInstance = provider;
    context.subscriptions.push(vsCodeLensDisposable);
    try {
      removeCodeLensDisposable && removeCodeLensDisposable.dispose();
      removeCodeLensDisposable = vscode.commands.registerCommand('JoyCode.codelens.del', (data) => {
        provider.removeCodeLensFromLine(data);
      });
      context.subscriptions.push(removeCodeLensDisposable);
    } catch (error) {}
    return vsCodeLensDisposable;
  } else {
    vsCodeLensDisposable && vsCodeLensDisposable.dispose();
  }
  return null;
}

//  添加手动触发补全
function manuallyTriggerCompletion(context: vscode.ExtensionContext) {
  manuallyDisposable && manuallyDisposable.dispose();
  manuallyDisposable = vscode.commands.registerCommand('JoyCode.code-completion.manually', async () => {
    // 获取当前活动的文本编辑器
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      // 触发系统命令
      await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
      await setGlobalState(context, 'JoyCode.lastManualCompletionTimestamp', +new Date());
      await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
    }
  });
  context.subscriptions.push(manuallyDisposable);
  newCompletionDisposable && newCompletionDisposable.dispose();
  newCompletionDisposable = vscode.commands.registerCommand('JoyCode.completion.get-new-completion', async () => {
    // 获取当前活动的文本编辑器
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      // 触发系统命令
      await vscode.commands.executeCommand('editor.action.inlineSuggest.hide');
      await vscode.commands.executeCommand('editor.action.inlineSuggest.trigger');
    }
  });
  context.subscriptions.push(newCompletionDisposable);
  return manuallyDisposable;
}

/**
 * 初始化代码补全功能。
 * @param context - VS Code 扩展上下文。
 */
function setCompletionsInit(context) {
  vsDisposable && vsDisposable.dispose();
  const codeProvider = codeCompletionProvider(g_isLoading, completionsStatusBarItem, false, context);
  vsDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, codeProvider);
}
/**
 * 配置代码补全最大行数。
 *
 * 当配置发生变化时，立即更新VSCode配置。
 *
 * @function setCompletionsMaxLines
 */
function switchConfigStatus(context) {
  try {
    new ImmediateChangeConfiguration({
      commandId: 'JoyCode.config.codeCompletionsGenTask',
      onChange: (config) => {
        try {
          if (config == 'FUNCTION') {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 58);
          } else if (config == 'BLOCK') {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 24);
          } else {
            setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 1);
          }
        } catch (error) {
          setVscodeConfig('JoyCode.config.codeCompletionsMaxLines', 1);
        }
      },
    });
    // 行间菜单开关设置
    new ImmediateChangeConfiguration({
      commandId: 'JoyCode.config.codeLens-row-menus',
      onChange: () => {
        try {
          registerCodeLensProvider(context);
        } catch (error) {}
      },
    });
  } catch (error) {}
}
