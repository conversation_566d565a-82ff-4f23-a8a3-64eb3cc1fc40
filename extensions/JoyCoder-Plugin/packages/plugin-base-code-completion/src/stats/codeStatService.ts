import os from 'os';
import * as vscode from 'vscode';
import { constants, accessSync } from 'fs';
import * as child_process from 'child_process';
import { getBaseUrl, isIDE } from '@joycoder/shared';

import { Logger, ensureRemoteBinaryFile, getJdhCgiUrl, makeExecutable, pluginVersion, isDebug } from '@joycoder/shared';
/**
 * 检查并启动统计服务
 */
class CodeStatService {
  // 是否已启动
  started = false;
  stat_process: child_process.ChildProcessWithoutNullStreams | null = null;
  isSupport = true;
  isDebug = false;
  cacheRequests: Array<{ func: string; args: any[] }> = [];

  constructor() {
    this.isDebug = isDebug();
  }

  /**
   * 发送文档变更到统计程序
   *
   * @param codeChange 文档变更
   */
  public async sendCodeChangeToStat(codeChange: object) {
    if (this.started && this.stat_process) {
      // 线上不要让用户看到具体统计的日志，避免用户反感
      this.isDebug && Logger.info('statistics data : ' + JSON.stringify(codeChange));
      // 将数据发送给统计程序
      this.stat_process.stdin.write(JSON.stringify(codeChange) + os.EOL);
      Logger.info('statistics data : end ');
    } else if (this.isSupport) {
      this.cacheRequests.push({ func: 'sendCodeChangeToStat', args: [codeChange] });
    }
  }

  /**
   * 立即上报
   */
  public async sendReportImmediate() {
    if (this.started && this.stat_process) {
      this.stat_process.stdin.write('reportImmediate' + os.EOL);
    } else if (this.isSupport) {
      this.cacheRequests.push({ func: 'sendReportImmediate', args: [] });
    }
  }

  /**
   * 立即上报
   */
  public async updatePtk(ptk: string) {
    if (this.started && this.stat_process) {
      console.log('已同步go程序登录态');
      this.stat_process.stdin.write(ptk + os.EOL);
    }
  }
  /**
   * 检查并启动服务
   */
  public async checkAndStartService() {
    if (this.started) {
      return;
    }
    const currentExecPath = await ensureRemoteBinaryFile('statCode');
    if (!currentExecPath) {
      this.isSupport = false;
      return;
    }
    // 程序已放置完毕
    try {
      accessSync(currentExecPath, constants.R_OK | constants.X_OK);
    } catch (err) {
      // 设置文件可执行
      await makeExecutable(currentExecPath);
    }

    // 启动服务
    const envInfo = parseEnvInfo();
    this.stat_process = child_process.spawn(
      currentExecPath,
      [
        '-listener=true',
        '-upload=true',
        '-serverUrl=' + (isIDE() ? getBaseUrl() : getJdhCgiUrl('http://jdhgpt.jd.com/bdData')),
        '-intervalTime=300',
        '-ideType=vscode ' + vscode.version,
        '-pluginVersion=' + pluginVersion,
        '-aixDebug=true',
      ],
      { env: envInfo }
    );

    this.started = true;

    // 监听输出
    this.listenStdout();

    // 积压的请求统一处理掉
    if (this.cacheRequests.length > 0) {
      this.cacheRequests.forEach((req) => {
        this[req.func](...req.args);
      });
      this.cacheRequests = [];
    }
  }

  /**
   * 监听子线程输出
   */
  private async listenStdout() {
    if (this.stat_process) {
      this.stat_process.stdout.on('data', (data) => {
        Logger.info(`stat code stdout: ${data}`);
      });

      this.stat_process.stderr.on('data', (data) => {
        Logger.error(`stat code stderr: ${data}`);
      });
      this.stat_process.on('error', (error) => {
        Logger.error(`stat code 发生错误: ${error}`);
      });
    }
  }

  /**
   * 停止服务
   */
  public async stopService() {
    if (this.started && this.stat_process) {
      this.stat_process.stdin.write('exit' + os.EOL);
      this.stat_process.kill();
      this.started = false;
    }
  }
}

/**
 * 解析代理字符串并返回环境变量信息。
 * @returns 包含插件版本、主目录和代理配置的环境变量对象。
 */
function parseEnvInfo(): NodeJS.ProcessEnv {
  const proxyString = vscode.workspace.getConfiguration('http').get('proxy') as string;
  // 获取当前用户的主目录
  const homeDir = os.homedir();

  const envPath = global.process.env.PATH;
  const envInfo = { PLUGIN_VER: 'business', HOME: homeDir, PATH: envPath };
  const regex = /^(https?):\/\/((?:([^:]*):([^@]*)@)?([^:\/]*)(?::(\d+))?)$/;
  const match = proxyString.match(regex);

  if (match) {
    Object.assign(envInfo, {
      'AIX-PROXY-USER': match[3] || '',
      'AIX-PROXY-PASSWORD': match[4] || '',
      'AIX-PROXY-HOST': match[5] || '',
      'AIX-PROXY-PORT': match[6] || '',
    });
  }

  return envInfo;
}
// 单例模式
export const codeStatService = new CodeStatService();

export function stopCodeStatService() {
  codeStatService.stopService();
}
