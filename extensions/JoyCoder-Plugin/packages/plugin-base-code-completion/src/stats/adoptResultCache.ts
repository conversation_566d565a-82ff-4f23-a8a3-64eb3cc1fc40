/**
 * 补全的类型
 */
enum AdoptType {
  None = 0,
  LOCAL_SYSTEM = 1,
  LOCAL_JOY_CODER = 11,
  REMOTE_JOY_CODER = 21,
}
// 代码来源
enum AdoptCodeSource {
  CHAT_CLICK_COPY = 'chatClickCopy', //, "点击复制"
  CHAT_COPY = 'chatCopy', //"复制"),
  CHAT_INSERT = 'chatInsert', //"插入"),
  CHAT_NEW_FILE = 'chatNewFile', //"新建文件"),
  COMPLETION = 'completion', //"代码补全"),
  // TODO: 和聊天框粘贴复制一致
  UNIT_TEST = 'unitTest', //"单元测试"),
  // TODO:
  LINE_COMMENT = 'lineComment', //"行注释"),
  COMMENT = 'comment', //"一键注释"),
  // TODO:
  INLINE_CHAT_EDIT = 'InLineChatEdit', //"内联聊天编辑"),
  INLINE_CHAT_NEW = 'InLineChatNew', //"内联聊天新建"),
  // TODO:
  CODE_REVIEW = 'codeReview', //"代码审查");
  CODE_FIX = 'codeFix', // "代码修复");
  CODE_RECONSTRUCTION = 'codeReconstruction', // 代码重构;
  BATCH_CODE_TRANSLATE = 'BatchCodeTranslateEnum', // 批量翻译;
  AUTO_CODE = 'autoCode', // 自动化编程
}

interface AdoptResultItem {
  text: string;
  type: AdoptType;
  model: string;
  codeSource: AdoptCodeSource;
  expiredCount: number;
  conversationId?: string;
}

/**
 * 预测结果缓存类
 *
 * @class AdoptResultCache
 */
class AdoptResultCache {
  // 类型枚举
  public static TYPE = AdoptType;
  // 代码来源枚举
  public static ADOPT_CODE_SOURCE = AdoptCodeSource;
  // 缓存的数组，由于复制操作的存在，可能存在需要缓存多个结果的情况
  // 1. 用户从聊天窗复制了代码片段A 2. 随后用户在编辑器内触发了补全 3. 用户粘贴聊天窗内的代码
  private static items: AdoptResultItem[] = [];
  // 缓存在数组最长的次数
  private static expiredMaxCount = 10;

  // 更新JoyCoder远程模型预测的代码
  public static setRemote(text: string, model: string, codeSource: AdoptCodeSource, conversationId?: string) {
    AdoptResultCache.items.push({
      text,
      type: AdoptType.REMOTE_JOY_CODER,
      model,
      codeSource,
      expiredCount: AdoptResultCache.expiredMaxCount,
      conversationId,
    });
  }

  // 读取最新的预测结果并清空
  public static getAndClearAdopt(changeText: string) {
    let result = { text: '', type: AdoptType.None, model: '', codeSource: '', conversationId: '' };

    if (!AdoptResultCache.items.length) return result;

    let copyIndex = -1;
    let otherIndex = -1;
    AdoptResultCache.items.some((item, index) => {
      const isCopyType = [AdoptCodeSource.CHAT_CLICK_COPY, AdoptCodeSource.CHAT_COPY].indexOf(item.codeSource) >= 0;
      const isAutoType = [AdoptCodeSource.AUTO_CODE].indexOf(item.codeSource) >= 0;
      const isEqualText = changeText === item.text;
      const isContainText = item.text.indexOf(changeText.trimEnd()) >= 0;
      if (isCopyType && isEqualText) {
        copyIndex = index;
        return true;
      }
      if (isEqualText || (isAutoType && isContainText)) {
        otherIndex = index;
      }
      return false;
    });

    const adoptIndex = copyIndex >= 0 ? copyIndex : otherIndex;

    // 有命中的Adopt信息，更新result
    if (adoptIndex >= 0) {
      result = AdoptResultCache.items[adoptIndex] as {
        text: string;
        type: AdoptType;
        model: string;
        codeSource: string;
        conversationId: string;
      };
      if (result.codeSource !== AdoptCodeSource.AUTO_CODE) {
        AdoptResultCache.items.splice(adoptIndex, 1);
      }
      // 清理掉过期的Adopt信息
      AdoptResultCache.items = AdoptResultCache.items.filter((item) => {
        if (item.codeSource === AdoptCodeSource.AUTO_CODE) return true;
        item.expiredCount--;
        return item.expiredCount >= 0;
      });
    }

    return result;
  }

  public static clearAutoCodeAdopt() {
    AdoptResultCache.items = AdoptResultCache.items.filter((item) => {
      return item.codeSource !== AdoptCodeSource.AUTO_CODE;
    });
  }
}

export default AdoptResultCache;
