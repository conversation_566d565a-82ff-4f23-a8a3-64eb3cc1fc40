import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface FileChangeInfo {
  afterFileName: string;
  changeType: string;
  beforeFileName: string;
}

export function activate(context: vscode.ExtensionContext) {
  let reportCallback: ((changeInfo: FileChangeInfo) => void) | undefined = () => {};
  const recentlyModifiedFiles: string[] = [];

  const fileSystemWatcher = vscode.workspace.createFileSystemWatcher('**/*');

  // 监听重命名事件
  context.subscriptions.push(
    vscode.workspace.onDidRenameFiles((e: vscode.FileRenameEvent) => {
      e.files.forEach(({ oldUri, newUri }) => {
        const changeInfo: FileChangeInfo = {
          afterFileName: newUri.fsPath,
          changeType: '2',
          beforeFileName: oldUri.fsPath,
        };
        handleFileChange(changeInfo);
      });
    })
  );

  // 监听创建文件事件（可能是粘贴操作）
  context.subscriptions.push(
    vscode.workspace.onDidCreateFiles((e: vscode.FileCreateEvent) => {
      e.files.forEach(async (file) => {
        if (fs.existsSync(file.fsPath)) {
          const beforeFileName = await findSourceFile(file.fsPath);
          const changeInfo: FileChangeInfo = {
            afterFileName: file.fsPath,
            changeType: '3',
            beforeFileName: beforeFileName || '',
          };
          handleFileChange(changeInfo);
        }
      });
    })
  );

  // 监听文件修改事件
  context.subscriptions.push(
    vscode.workspace.onDidSaveTextDocument((document: vscode.TextDocument) => {
      recentlyModifiedFiles.unshift(document.uri.fsPath);
      if (recentlyModifiedFiles.length > 10) {
        recentlyModifiedFiles.pop();
      }
    })
  );

  context.subscriptions.push(fileSystemWatcher);

  async function findSourceFile(destPath: string): Promise<string | undefined> {
    // 首先检查最近修改的文件
    for (const modifiedFile of recentlyModifiedFiles) {
      if (
        path.basename(modifiedFile) === path.basename(destPath) &&
        fs.existsSync(modifiedFile) &&
        (await compareFiles(modifiedFile, destPath))
      ) {
        return modifiedFile;
      }
    }

    // 如果没有匹配的最近修改文件，搜索整个工作区
    const files = await vscode.workspace.findFiles(
      '**/*',
      '{**/node_modules/**,**/dist/**,**/build/**,**/.git/**,**/vendor/**,**/.vscode/**,**/bin/**,**/obj/**,**/target/**,**/.idea/**,**/.*/**}',
      1000
    );
    for (const file of files) {
      if (
        file.fsPath !== destPath &&
        path.basename(file.fsPath) === path.basename(destPath) &&
        (await compareFiles(file.fsPath, destPath))
      ) {
        return file.fsPath;
      }
    }

    return undefined;
  }

  async function compareFiles(file1: string, file2: string): Promise<boolean> {
    const [stat1, stat2] = await Promise.all([fs.promises.stat(file1), fs.promises.stat(file2)]);
    return stat1.size === stat2.size && stat1.mtime.getTime() <= stat2.mtime.getTime();
  }

  function handleFileChange(changeInfo: FileChangeInfo) {
    if (reportCallback) {
      reportCallback(changeInfo);
    }
  }

  return {
    setReportCallback: (callback: (changeInfo: FileChangeInfo) => void) => {
      reportCallback = callback;
    },
  };
}

export default function (context: vscode.ExtensionContext, callback?: (param: any) => void) {
  const { setReportCallback } = activate(context);
  setReportCallback((stats) => {
    if (typeof callback !== 'function') {
      return;
    }
    if (typeof stats === 'object') {
      callback(stats);
    } else {
      callback({});
    }
  });
}
