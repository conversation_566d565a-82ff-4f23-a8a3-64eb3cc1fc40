{"name": "@joycoder/plugin-base-code-completion", "version": "3.1.2", "description": "", "author": "JoyCoder", "homepage": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git", "private": true, "main": "src/index.ts", "module": "src/index.ts", "files": ["src"], "repository": {"type": "git", "url": "https://coding.jd.com/JoyCoder/JoyCoder-VSCode.git"}, "scripts": {"lint": "eslint src --ext ts", "tsc": "tsc --noEmit --allowJs --esModuleInterop --resolveJsonModule --skipLibCheck"}, "bugs": {"url": "https://coding.jd.com/JoyCoder/JoyCoder/issues/"}, "publishConfig": {"access": "public"}, "dependencies": {"@joycoder/plugin-base-ai": "workspace:*", "@joycoder/shared": "workspace:*", "node-fetch": "^3.3.0", "natural": "^7.0.6", "uuid": "^9.0.1", "web-tree-sitter": "^0.22.6"}}