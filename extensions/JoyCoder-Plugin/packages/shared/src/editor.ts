/**
 * vscode编辑器相关方法
 */
import * as path from 'path';
import * as vscode from 'vscode';
import { ISelectionInfo } from './types';

let activeTextEditorId = '';
const { window } = vscode;

/**
 * 获取光标最后一次所在的编辑区，一般用于光标失焦后插入代码
 * @returns
 */
export function getLastAcitveTextEditor() {
  const { visibleTextEditors } = window;
  const activeTextEditor = visibleTextEditors.find((item: any) => item.id === activeTextEditorId);
  return activeTextEditor;
}

export function setLastActiveTextEditorId(id: string) {
  activeTextEditorId = id;
}

export function onChangeActiveTextEditor(context: vscode.ExtensionContext) {
  vscode.window.onDidChangeActiveTextEditor(
    (editor) => {
      if (editor) {
        const { id } = editor as any;
        setLastActiveTextEditorId(id);
      }
    },
    null,
    context.subscriptions
  );
}

/**
 * 获取当前编辑区中鼠标选中的信息
 */
export function getSelectionInfo(): ISelectionInfo | null {
  const editor = vscode.window.activeTextEditor;

  if (!editor) return null;

  const document = editor.document;
  const selection = editor.selection;

  const start = selection.start;
  const end = selection.end;
  const filePath = document.uri.fsPath;
  const fileName = path.basename(document.fileName);
  const startLine = start.line + 1;
  const startCharacter = start.character + 1;
  const endLine = end.line + 1;
  const endCharacter = end.character + 1;

  return {
    fileName,
    filePath,
    startLine,
    startCharacter,
    endLine,
    endCharacter,
    selection,
    selectedText: getSelectedText(),
  };
}

/**
 * 获取当前编辑区中鼠标选中的字符串
 */
export function getSelectedText() {
  const editor = vscode.window.activeTextEditor;

  if (!editor) return '';

  // 当前被选中文本的位置信息数组（实际上就是 range 组成的数组）
  const Ranges = editor.selections;
  // 通过位置信息拿到所有被选中的文本
  return Ranges && Ranges.reduce((all, cur) => all + editor.document.getText(cur), '');
}

/**
 * 获取当前激活的文件窗口
 */
export function getCurrentFileEditor(): vscode.TextEditor | null {
  // 获取所有可见的编辑器
  const editors = vscode.window.visibleTextEditors;

  // 本地和远程文件scheme
  const schemes = ['file', 'vscode-remote'];

  // 从中找出源代码编辑器，主要是为了解决comment组件出现时activeTextEditor取值不是代码文件的问题
  const editor = editors.find((editor) => schemes.includes(editor.document.uri.scheme));

  if (!editor) return null;

  return editor;
}

/**
 * 存在评论窗口时使用此方法获取选中代码，getSelectedText之所以不行是因为此时activeTextEditor为评论窗口
 */
export function getSelectedTextWithoutComment() {
  const editor = getCurrentFileEditor();

  if (!editor) return '';

  // 当前被选中文本的位置信息数组（实际上就是 range 组成的数组）
  const Ranges = editor.selections;
  // 通过位置信息拿到所有被选中的文本
  return Ranges && Ranges.reduce((all, cur) => all + editor.document.getText(cur), '');
}

export interface ICodeInfo {
  line: vscode.TextLine;
  word: string;
  fileName: string;
  directory: string;
}

/**
 * 获取当前鼠标悬浮的代码
 * @param document
 * @param position
 * @returns
 */
export function getFocusCodeInfo(document: vscode.TextDocument, position: vscode.Position): ICodeInfo {
  return {
    line: document.lineAt(position),
    word: document.getText(document.getWordRangeAtPosition(position)),
    fileName: document.fileName,
    directory: path.dirname(document.fileName),
  };
}

/**
 * 获取光标所在行的信息
 * @returns
 */
export function getCursorLineInfo(): vscode.TextLine | null {
  const editor = getCurrentFileEditor();
  if (!editor) return null;

  const document = editor.document;
  const selection = editor.selection;
  // 获取光标所在行
  const line = selection.active.line;
  // 获取该行的文本
  const info = document.lineAt(line);
  return info;
}

/**
 * 将某行设置为选中状态，默认为光标所在行
 * @param lineNumber
 */
export function setLineSelected(lineNumber?: number) {
  const editor = getCurrentFileEditor();

  lineNumber = getCursorLineInfo()?.lineNumber ?? -1;
  if (editor && lineNumber > -1) {
    const line = editor.document.lineAt(lineNumber);
    const selection = new vscode.Selection(line.range.start, line.range.end);

    editor.selection = selection;
  }
}

/**
 * 获取选中代码或光标所在行代码中的错误信息
 */
export function getSelectedCodeErrors() {
  const editor = getCurrentFileEditor();
  const selectedRange = editor?.selection.isEmpty ? getCursorLineInfo()?.range : editor?.selection;
  const document = editor?.document;
  let errInfo = '';

  if (document && selectedRange) {
    const diagnostics = vscode.languages.getDiagnostics(document.uri);

    diagnostics.forEach(async (diagnostic) => {
      const { severity, relatedInformation } = diagnostic;
      const diagnosticType = severity === vscode.DiagnosticSeverity.Warning ? `warning` : `error`;
      if (selectedRange?.contains(diagnostic.range)) {
        errInfo += `Fix the following:${diagnostic.source}${diagnosticType}from within <PROBLEMCODE4179></PROBLEMCODE4179>:${diagnostic.message};`;
      }
      if (relatedInformation?.length) {
        errInfo += `Code related to this diagnostic:`;
        for (let i = 0; i < relatedInformation.length; i++) {
          const { location, message = '' } = relatedInformation[i];
          errInfo += message;
          if (location) {
            const document = await vscode.workspace.openTextDocument(location.uri);
            const relatedCode = document.getText(location.range);
            errInfo += `<RELATEDCODE50${i}>${relatedCode}</RELATEDCODE50{i}>\n`;
          }
        }
      }
    });
  }
  return errInfo;
}

/**
 * 获取所有打开的标签Tab
 */
export function getAllTabs() {
  return vscode.window.tabGroups.all.map((group) => group.tabs).flat(1);
}

/**
 * 获取当前Tab
 */
export function getCurrentTab() {
  return vscode.window.tabGroups.activeTabGroup.activeTab;
}

/**
 * 获取vscode缩放后webview页面尺寸缩放百分比
 * @returns
 */
export function getZoomRatio() {
  const zoomLevel: number = vscode.workspace.getConfiguration('window').get('zoomLevel') || 0;
  return Math.pow(1.2, zoomLevel);
}
