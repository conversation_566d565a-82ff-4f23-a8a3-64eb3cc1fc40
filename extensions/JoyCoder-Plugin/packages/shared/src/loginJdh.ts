import to from 'await-to-js';
import axios from 'axios';
import * as vscode from 'vscode';

import { Logger } from './logger';
import { openInBrowser } from './jump';
import { GlobalState } from './globalState';
import { v4 as uuidv4 } from 'uuid';
import { reportUmp } from './report/reportInVsCode';
import { getUserInfo, setUserInfo } from './userInfo';
import { addRouter } from './router';
import { getJdhCgiUrl, isBusiness, isIDE } from './business';
import { getBaseUrl } from './';
import { ideAppName } from './pluginId';
import { updateLoginStatus } from '@joycoder/plugin-base-ai/src/dialog';
import { JoyCoderProvider } from '@joycoder/agent-driven/src/core/webview/JoycoderProvider';
/**
 * 登录健康后回跳的时候顺带带回sso登录信息
 * @returns
 */
function addRouterIn() {
  return new Promise((resolve) => {
    addRouter({
      path: '/ssologin',
      callback: async (uri) => {
        const info: RegExpMatchArray | null = uri.query.match(/info=([^$&]+)/);
        if (!info) return resolve(info);

        const ssoLoginfo = JSON.parse(decodeURIComponent(info[1]));
        GlobalState.update('ssoLoginInfo', {
          ...ssoLoginfo,
        });
        resolve(ssoLoginfo);
      },
    });
  });
}

/**
 * 强制去登录
 */
export async function forceJdhLogin(isShowDialog = true, loginSuccess?: (isLogined: boolean) => void) {
  if (isShowDialog) {
    // 先弹出确认框
    const confirmText = '去浏览器授权';
    const [, result] = await to(
      Logger.showInformationMessage('JoyCode 需要使用您的登录凭据以确保正确的访问权限', confirmText)
    );

    if (result != confirmText) {
      return false;
    }
  }

  // 去健康的登录特殊处理
  const uuid = uuidv4();
  let loginPath = `http://jdhgpt.jd.com/login/pluginlogin?uuid=${uuid}&userName=${
    getUserInfo().userName
  }&source=joyCoderFe&ideAppName=${ideAppName}`;
  if (isBusiness()) {
    loginPath = `${loginPath}&loginType=1`;
  }

  const loginUrl = getJdhCgiUrl(loginPath);

  // 非IDE则直接跳转去登录
  if (!isIDE()) {
    openInBrowser(loginUrl);

    // 等待从浏览器回跳到VSCode
    addRouterIn();
  }

  if (isIDE()) {
    await openInCommand();
  } else {
    // 获取健康登录态
    const jdhUserToken = await getJdhPrivateLoginToken(uuid);

    if (jdhUserToken == 'timeout') {
      Logger.showErrorMessage('登录超时，请重试~');
      return false;
    }

    const erp = await getErp(jdhUserToken);

    if (jdhUserToken && erp) {
      GlobalState.update('jdhLoginInfo', {
        userName: getUserInfo().userName,
        userToken: jdhUserToken,
        erp,
      });
      setUserInfo({ erp, userToken: jdhUserToken });

      // 更新登录态
      updateLoginStatus(true);
      const visibleProvider = JoyCoderProvider.getVisibleInstance();
      if (visibleProvider) {
        visibleProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin: true } });
      }

      const info = `登录成功，欢迎${erp}~`;
      Logger.showInformationMessage(info);
      loginSuccess?.(true);
      return true;
    }

    const errorMsg = `登录失败，请重试或截图反馈~ uuid:${uuid}；userName:${
      getUserInfo().userName
    }；userToken:${jdhUserToken}；erp:${erp}；`;
    Logger.showErrorMessage(errorMsg);
    return false;
  }
}

/**
 * 客户端是否存在健康登录态
 */
export function hasJdhLoginCookie(): { userName: string; userToken: string } | null {
  // 返回: 如果有登录态，就返回loginInfo，否则返回null
  const info = getJdhLoginInfo();
  if (!info) return null;

  return isIDE()
    ? info.ptKey && info.userName
      ? info
      : null
    : info.erp && info.userName && info.userToken
    ? info
    : null;
}

/**
 * 获取健康客户端登录态
 */
export function getJdhLoginInfo(): null | {
  userToken: string;
  userName: string;
  erp: string;
  ptKey?: string;
  pk?: string;
  loginType?: string;
} {
  return GlobalState.get('jdhLoginInfo');
}

/**
 * 获取健康插件为延长登录态有效期定制的登录态
 * https://cf.jd.com/pages/viewpage.action?pageId=1371796637
 */
export async function getJdhPrivateLoginToken(uuid: string): Promise<string> {
  const [err, res]: [any, any] = await to(
    axios.post(
      getJdhCgiUrl('http://jdhgpt.jd.com/es/bigdata/pollLoginInfo'),
      {
        uuid,
        sourceType: 'encrypt',
        userName: getUserInfo().userName,
        source: 'joyCoderFe',
      },
      {
        timeout: 5 * 60 * 1000,
      }
    )
  );

  // 5分钟内未在web端完成登录的情况
  if (err?.code === 'ECONNABORTED' || err?.message.includes('timeout')) {
    return 'timeout';
  }

  if (err || !res) {
    reportUmp(40, 1, err || res);
    return '';
  }

  const regExp = /data\:(.*?)\n/;
  const match = (res.data as string).match(regExp);
  if (!match) {
    reportUmp(40, -1, res.data);
    return '';
  }

  const data = JSON.parse(match[1]);
  if (data?.code !== '0000') {
    reportUmp(40, data?.code, `jdh登录失败: ${match[1]}`);
    return '';
  }

  reportUmp(40, 0, res.data);

  return data.data;
}

/**
 * 获取IDE登录用户信息
 * http://joycoder-api-inner.jd.com/api/saas/user/v1/userInfo
 */
export async function getJdhIdeUserInfo(
  ptKey?: string,
  loginType?: string | null
): Promise<{
  userId?: string;
  type?: string;
  loginType?: string;
  pk?: string;
  ptKey?: string;
  loginUrl?: string;
  erp?: string;
  id?: string;
  uid?: string;
}> {
  const res = await axios({
    method: 'post',
    url: getJdhCgiUrl(getBaseUrl() + '/api/saas/user/v1/userInfo'),
    headers: {
      'Content-Type': 'application/json',
      ptKey,
      loginType,
    },
    timeout: 5 * 60 * 1000,
  });
  const data = res.data;
  if (data?.code !== 0) {
    reportUmp(40, data?.code, `jdh登录失败: ${data.msg}`);
    return { type: 'error', ...(data.data || {}) };
  }

  reportUmp(40, 0, res.data);
  return data.data;
}

/**
 * 获取erp
 * https://cf.jd.com/pages/viewpage.action?pageId=1371792202
 */
export async function getErp(userToken: string): Promise<string> {
  const { err, res } = await loginResultCheck(userToken);
  const erp = (res as any)?.data?.data?.erp || '';
  if (err || !erp) {
    reportUmp(40, 1001, err || res);
  }

  return erp;
}

export async function loginResultCheck(userToken: string) {
  const [err, res] = await to(
    axios.post(
      getJdhCgiUrl('http://jdhgpt.jd.com/login/loginResultCheck'),
      {
        userToken,
        sourceType: 'joyCoderFe',
        userName: getUserInfo().userName,
      },
      {
        timeout: 5000,
      }
    )
  );
  return { err, res };
}

export async function checkLogin() {
  const isLogin = !!hasJdhLoginCookie();
  if (isIDE()) {
    const info = getJdhLoginInfo();
    return !!isLogin && !!info?.ptKey;
  }
  // const userToken = getUserInfo()?.userToken ?? '';
  const userToken = getJdhLoginInfo()?.userToken ?? '';
  const { res } = await loginResultCheck(userToken);
  return !!(res as any)?.data?.data?.timeEffectFlag && !!isLogin;
}
async function openInCommand() {
  const LOGIN_ACTION_ID = 'workbench.action.joycoderLogin';
  // 执行 命令 LOGIN_ACTION_ID
  vscode.commands.executeCommand(LOGIN_ACTION_ID);
}
