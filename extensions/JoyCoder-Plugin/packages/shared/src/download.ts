import axios from 'axios';
import * as path from 'path';
import * as fs from 'fs-extra';

/**
 * 下载文件
 * @param url
 * @param fileDir
 * @param fileName
 * @returns
 */
export async function downloadFile(url: string, fileDir: string, fileName: string) {
  fs.ensureDirSync(fileDir);

  const mypath = path.resolve(fileDir, fileName);
  const writer = fs.createWriteStream(mypath);
  const response = await axios({
    url,
    method: 'GET',
    responseType: 'stream',
  });
  response.data.pipe(writer);
  return new Promise<void>((resolve, reject) => {
    writer.on('finish', () => resolve());
    writer.on('error', (error: Error) => reject(error));
  });
}
