/**
 * 早期使用的登录模块，保留以便后续参考使用，包含几个阶段：
 * 1、sso.jd.com + szfe文档
 * 2、私有登录态 + szfe文档（为延长有效期）
 * 3、联合登录态 + joycoder文档 + iframe（以下代码实现就是这个版本，但iframe会出现少量失败）
 * 4、我们接口适配健康登录态（目前线上逻辑，代码在loginJdh.js）
 */
import to from 'await-to-js';
import axios from 'axios';
import * as vscode from 'vscode';
import { Logger } from './logger';
import { addRouter } from './router';
import { openInBrowser } from './jump';
import { GlobalState } from './globalState';
import { getIpAddress, safeDecodeURI } from './utils';
import { v4 as uuidv4 } from 'uuid';
import { getUserInfo } from './userInfo';
import { getJdhPrivateLoginToken } from './loginJdh';
import { updateLoginStatus } from '@joycoder/plugin-base-ai/src/dialog/index';
import { isIDE } from './business';

// 添加一个全局的 webview panel 管理器
// const activeWebviewPanel: vscode.WebviewPanel | null = null;

export interface UserInfo {
  userName?: string;
  userToken?: string;
  erp?: string;
  ptKey?: string;
}
export function setActiveWebviewPanel(panel: vscode.WebviewPanel) {
  (global as any).activeWebviewPanel = panel;
}

export function getActiveWebviewPanel(): vscode.WebviewPanel | null {
  return (global as any).activeWebviewPanel || null;
}

/**
 * 获取网页登录的url
 *
 * @export
 */
export function getBrowserLoginUrl() {
  const isInsiders = vscode.env.appName.toLowerCase().includes('insiders');
  return `http://joycoder.jd.com?login=1${isInsiders ? '&isinsiders=1' : ''}`;
}

type LoginCallback = (result: object | string) => void;
const rejectCallbacks: LoginCallback[] = [];
const resolveCallbacks: LoginCallback[] = [];

// 新增回调，监听浏览器回跳事件
addRouter({
  path: '/login',
  callback: async (uri) => {
    const info: RegExpMatchArray | null = uri.query.match(/info=([^$&]+)/);
    if (!info) {
      rejectCallbacks.forEach((cb) =>
        cb({
          code: 1001,
          msg: '获取授权信息失败~',
        })
      );
      rejectCallbacks.splice(0, rejectCallbacks.length);
      return;
    }

    // 再去获取用户信息
    try {
      const loginfo = JSON.parse(safeDecodeURI(info[1]));
      const [userToken, jdhUserToken] = await Promise.all([
        getPrivateLoginToken(loginfo),
        getJdhPrivateLoginToken(loginfo),
      ]);

      if (userToken && jdhUserToken) {
        GlobalState.update('loginInfo', {
          ...loginfo,
          userToken,
        });
        GlobalState.update('jdhLoginInfo', {
          userName: getUserInfo().userName,
          userToken: jdhUserToken,
          erp: loginfo.username,
        });

        if (loginfo?.fullname) {
          const info = `欢迎使用JoyCoder，${loginfo.fullname}~`;
          Logger.showInformationMessage(info);
          // hack 连续展示四次同样的信息，可关闭该弹窗
          setTimeout(() => {
            [...Array(4)].map(() => {
              Logger.showInformationMessage(info);
            });
          }, 1500);
        }
      } else {
        const errorMsg = `登录失败，请在浏览器退出ERP登录再重试或截图联系JoyCoder团队处理~ userToken:${userToken}；jdhUserToken:${jdhUserToken}；loginfo:${JSON.stringify(
          loginfo
        )}；`;
        Logger.showErrorMessage(errorMsg);
        throw errorMsg;
      }

      resolveCallbacks.forEach((cb) => cb(loginfo));
      resolveCallbacks.splice(0, resolveCallbacks.length);
    } catch (e) {
      rejectCallbacks.forEach((cb) =>
        cb({
          code: 1002,
          msg: isIDE() ? '解析授权信息失败或userToken获取失败~' : '解析授权信息失败或ptKey获取失败~',
        })
      );
      rejectCallbacks.splice(0, rejectCallbacks.length);
      return;
    }
  },
});

/**
 * 强制去登录(默认不包括健康登录)
 */
export function forceLogin(options?: { scene?: 'jdh'; uuid?: string }): Promise<object | string> {
  return new Promise(async (resolve, reject) => {
    // 先弹出确认框
    const confirmText = '去浏览器授权';
    const [, result] = await to(
      Logger.showInformationMessage('JoyCode 需要使用您的ERP登录凭据以确保正确的访问权限', confirmText)
    );

    if (result != confirmText) {
      return reject({
        code: 0,
        msg: '用户取消授权',
      });
    }

    let loginUrl = getBrowserLoginUrl();
    if (options?.scene == 'jdh') {
      // 去健康的登录特殊处理
      const uuid = uuidv4();
      loginUrl += `&uuid=${uuid}`;
    }
    // 跳转去登录
    openInBrowser(loginUrl);

    // 新增回调监听
    resolveCallbacks.push(resolve);
    rejectCallbacks.push(reject);
  });
}

/**
 * 强制登录两端
 * @see https://cf.jd.com/pages/viewpage.action?pageId=1371796637，插件登录接口文档
 *
 * @export
 * @return {*}
 */
export async function forceUnionLogin(): Promise<boolean> {
  // 去健康的登录特殊处理
  const [err] = await to(forceLogin({ scene: 'jdh' }));
  if (err) return false;
  return true;
}

/**
 * 客户端是否存在登录态
 */
export function hasLoginCookie(): { ssotoken: string } | null {
  // 返回: 如果有登录态，就返回loginInfo，否则返回null
  const info = getLoginInfo();
  if (info && info.ssotoken && info.userToken) {
    return info;
  } else {
    return null;
  }
}

/**
 * 弱登录，客户端存在登录态则认为已登录
 */
export async function login() {
  // 先检查是否存在登录态
  const loginInfo = hasLoginCookie();
  if (loginInfo) {
    return loginInfo;
  }

  // 无登录态，就去登录
  const [err, res] = await to(forceLogin());
  if (err) {
    throw err;
  }

  return res;
}

/**
 * 清除客户端登录态
 */
export function clearLoginCookie() {
  GlobalState.del('loginInfo');
  GlobalState.del('ssoLoginInfo');
  GlobalState.del('jdhLoginInfo');
}

/**
 * 获取客户端登录态
 */
export function getLoginInfo(): {
  email: string;
  expire: number;
  fullname: string;
  hrmDeptId: string;
  mobile: string;
  orgId: string;
  orgName: string;
  personId: string;
  ssotoken: string;
  ssotype: number;
  tenantCode: string;
  userId: number;
  userToken?: number;
  username: string;
} {
  return GlobalState.get('loginInfo') || {};
}
export function updateJdhLoginInfo(loginInfo: UserInfo = {}) {
  try {
    GlobalState.update('jdhLoginInfo', { ...loginInfo });
  } catch (error) {
    console.error('%c [ updateJdhLoginInfo->error ]-228', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}

/**
 * 获取客户端SSO登录信息
 */
export function getSsoLoginInfo(): {
  email: string;
  expire: number;
  fullname: string;
  hrmDeptId: string;
  mobile: string;
  orgId: string;
  orgName: string;
  personId: string;
  ssotoken: string;
  ssotype: number;
  tenantCode: string;
  userId: number;
  username: string;
} {
  return GlobalState.get('ssoLoginInfo');
}

/**
 * 获取插件为延长登录态有效期定制的登录态
 */
export async function getPrivateLoginToken(loginInfo): Promise<string> {
  const [err, response]: [any, any] = await to(
    axios({
      // url: 'http://chatgpt-relay-test.jd.com/chatgptrelay/genPrivateLoginToken',
      url: 'http://chatgpt-relay.jd.com/chatgptrelay/genPrivateLoginToken',
      headers: {
        cookie: 'sso.jd.com=' + loginInfo.ssotoken + ';',
      },
    })
  );
  const resData = response.data;
  if (err || !resData || resData.code !== 0) return '';

  return resData.data?.hiboxUserToken || '';
}

/**
 * 校验sso登录态是否有效
 */
export async function checkSSOLogin(): Promise<boolean | undefined> {
  const ticket = getLoginInfo()?.ssotoken;
  if (!ticket) return false;
  const [err, response]: [any, any] = await to(
    axios({
      url: 'http://ssa.jd.com/sso/ticket/verifyTicket',
      params: {
        url: 'http://ppms.jd.com/',
        ticket,
        ip: getIpAddress(),
      },
    })
  );
  // 接口异常，不确定是否真的未登录
  if (err || !response) return;
  const resBody = response.data;
  const isLogin = resBody && resBody.REQ_FLAG && resBody.REQ_CODE == 1;
  if (!isLogin) {
    // 通过接口校验确实未登录
    forceLogin();
    return false;
  }
  return true;
}

/**
 * 全局登录
 */
export async function globalLogin() {
  const [loginErr, loginInfo]: [any, any] = await to(login());
  if (loginErr) {
    const { code, msg } = loginErr;
    if (code == 0) return;
    return Logger.showErrorMessage(msg || '登录失败~');
  }
  if (!loginInfo.isShowedWelcome) {
    Logger.showInformationMessage(`欢迎使用JoyCoder，${loginInfo.name}~`);
    GlobalState.update('loginInfo', {
      ...loginInfo,
      isShowedWelcome: true,
    });
  }
}

/**
 * 退出登录命令
 */
export function initLogOutCommand(context: vscode.ExtensionContext) {
  const isIDEnv = isIDE();
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.LogOut', () => {
      clearLoginCookie();
      !isIDEnv && Logger.showInformationMessage('您已退出登录~');
      updateLoginStatus(false);
      vscode.commands.executeCommand('JoyCode.Coder.Logout');
      if (isIDEnv) {
        vscode.commands.executeCommand('workbench.action.joycoderLogout');
      }
    })
  );
}
