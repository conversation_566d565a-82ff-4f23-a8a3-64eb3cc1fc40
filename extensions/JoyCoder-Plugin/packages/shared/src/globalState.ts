/**
 * 管理全局状态
 */
import * as vscode from 'vscode';
import * as path from 'path';
import { clearLocalBinaryFile } from './importRemote';
import { deleteFolderRecursive, getGlobalPath } from './path';
import { WorkspaceState } from './workspaceState';

export class GlobalState {
  static context: vscode.ExtensionContext;

  static init(context: vscode.ExtensionContext) {
    this.context = context;
    initClearGlobalStateCommand(context);
  }

  static update(...args: [string, any]) {
    this.context.globalState.update(...args);
  }

  static get(...args: [string]): any {
    return this.context.globalState.get(...args);
  }

  static del(key: string): any {
    this.update(key, undefined);
  }

  static keys(): any {
    return this.context.globalState.keys();
  }
}

/**
 * 清除状态命令，方便开发调试
 */
function initClearGlobalStateCommand(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.ClearGlobalState', async () => {
      const options: string[] = GlobalState.keys().concat([
        'CodebaseIndexingStatus - Current Workspace',
        '本地二进制文件',
        '本地仓库学习结果',
        '[🧹清除所有缓存🧹]',
      ]);
      options.unshift('[🧹清除所有缓存🧹]');
      const state = await vscode.window.showQuickPick(options);
      if (!state) return;
      if (state === 'CodebaseIndexingStatus - Current Workspace') {
        GlobalState.context.workspaceState.update('codebaseIndexingStatus', 'UNINDEXED');
        vscode.window.showInformationMessage('已清除CodebaseIndexingStatus - Current Workspace');
        return;
      }
      if (state === '本地二进制文件') {
        clearLocalBinaryFile();
        vscode.window.showInformationMessage('已清除二进制模块包，建议您重启VSCode');
        return;
      }
      if (state === '本地仓库学习结果') {
        const indexPath = path.join(getGlobalPath(), 'index');
        deleteFolderRecursive(indexPath);
        vscode.window.showInformationMessage('已清除学习记录，建议您重启VSCode');
        return;
      }
      if (state === '[🧹清除所有缓存🧹]') {
        // 清除所有GlobalState缓存
        GlobalState.keys().forEach((key) => {
          GlobalState.del(key);
        });
        WorkspaceState.keys().forEach((key) => {
          WorkspaceState.del(key);
        });
        // 清除本地二进制文件
        clearLocalBinaryFile();
        // 清除本地仓库学习结果
        const indexPath = path.join(getGlobalPath(), 'index');
        deleteFolderRecursive(indexPath);
        vscode.window.showInformationMessage('全部清理完成');
        return;
      }
      GlobalState.del(state);
      WorkspaceState.del(state);
      vscode.window.showInformationMessage('操作完成');
    })
  );
}
