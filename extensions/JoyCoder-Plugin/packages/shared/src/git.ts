import * as vscode from 'vscode';
import { safeExecSync } from './exec';
import { GitExtension, Repository } from './types/git';

/**
 * 异步获取指定目录的 Git 仓库对象。
 *
 * 该函数首先尝试使用 VS Code 的 Git 扩展获取指定目录的 Git 仓库对象。如果获取失败或 Git 扩展不可用，则会重试最多 20 次,每次间隔 1 秒。如果仍然无法获取到仓库对象,则返回 `undefined`。
 *
 * @param forDirectory - 要获取 Git 仓库的目录 URI。
 * @returns 如果成功获取到 Git 仓库对象,则返回该对象;否则返回 `undefined`。
 */
export async function getRepo(forDirectory: vscode.Uri): Promise<Repository | undefined | null> {
  const TRY_COUNT = 10;
  let _repoWasNone = false;
  async function _getRepo(forDirectory: vscode.Uri): Promise<Repository | undefined | null> {
    // Use the native git extension to get the branch name
    const extension: vscode.Extension<GitExtension> | undefined = vscode.extensions.getExtension('vscode.git');
    if (
      typeof extension === 'undefined' ||
      !extension.isActive ||
      typeof vscode.workspace.workspaceFolders === 'undefined'
    ) {
      return undefined;
    }

    try {
      const git = extension.exports.getAPI(1);
      return git.getRepository(forDirectory);
    } catch (e) {
      _repoWasNone = true;
      console.warn('Git not found: ', e);
      return undefined;
    }
  }

  let repo = await _getRepo(forDirectory);

  let i = 0;
  while (!repo?.state?.HEAD?.name) {
    if (_repoWasNone) return undefined;

    await new Promise((resolve) => setTimeout(resolve, 1000));
    i++;
    if (i >= TRY_COUNT) {
      _repoWasNone = true;
      return undefined;
    }
    repo = await _getRepo(forDirectory);
  }
  return repo;
}

export async function getRepoName(forDirectory: vscode.Uri | string, repo?: any): Promise<string | undefined> {
  forDirectory = typeof forDirectory === 'string' ? vscode.Uri.file(forDirectory) : forDirectory;
  if (!repo) repo = await getRepo(forDirectory);
  const remote = repo?.state?.remotes?.find((r: any) => r.name === 'origin') ?? repo?.state?.remotes[0];
  if (!remote) {
    return undefined;
  }
  const ownerAndRepo = remote.fetchUrl.replace('.git', '').split('/').slice(-2);
  return ownerAndRepo.join('/');
}

export async function getRepoUrl(forDirectory: vscode.Uri | string, repo?: any): Promise<string | undefined> {
  forDirectory = typeof forDirectory === 'string' ? vscode.Uri.file(forDirectory) : forDirectory;
  if (!repo) repo = await getRepo(forDirectory);
  const remote = repo?.state?.remotes?.find((r: any) => r.name === 'origin') ?? repo?.state?.remotes[0];
  if (!remote) {
    return undefined;
  }
  return remote.fetchUrl;
}

export async function getGitRoot(forDirectory: vscode.Uri | string): Promise<string | undefined> {
  forDirectory = typeof forDirectory === 'string' ? vscode.Uri.file(forDirectory) : forDirectory;
  const repo = await getRepo(forDirectory);
  return repo?.rootUri?.fsPath;
}

export async function getBranch(forDirectory: vscode.Uri | string, repo?: any) {
  forDirectory = typeof forDirectory === 'string' ? vscode.Uri.file(forDirectory) : forDirectory;
  if (!repo) repo = await getRepo(forDirectory);

  if (repo?.state?.HEAD?.name === undefined) {
    try {
      const stdout = await safeExecSync('git rev-parse --abbrev-ref HEAD', {
        cwd: forDirectory.fsPath,
      });
      if (!stdout || /fatal\:[ ]/.test(stdout)) return 'NONE';
      return stdout?.trim() || 'NONE';
    } catch (e) {
      return 'NONE';
    }
  }

  return repo?.state?.HEAD?.name || 'NONE';
}

interface GitState {
  gitUrl?: string;
  gitRepo?: string;
  gitBranch?: string;
}

/**
 * GitStateManager类用于管理Git状态。
 */
export class GitStateManager {
  static defaultDir: string;
  static gitStateMap = new Map<string, GitState>();

  static init() {
    const dirs = vscode.workspace.workspaceFolders?.map((folder) => folder.uri.fsPath) || [];
    dirs.forEach(async (dir) => {
      if (!GitStateManager.defaultDir) GitStateManager.defaultDir = dir;
      const uri = vscode.Uri.file(dir);
      const repo = await getRepo(uri);
      if (repo) {
        const gitBranch = await getBranch(uri, repo);
        const gitUrl = await getRepoUrl(uri, repo);
        // const gitRepo = gitUrl?.replace('.git', '').split('/').slice(-2).join('/');
        const gitRepo = gitUrl?.replace('.git', '').split('/').slice(-1).join('/');
        GitStateManager.gitStateMap.set(dir, { gitUrl, gitRepo, gitBranch });
        repo.state.onDidChange(() => {
          // args passed to this callback are always undefined, so keep track of previous branch
          const gitBranch = repo?.state?.HEAD?.name;
          GitStateManager.gitStateMap.set(dir, { gitUrl, gitRepo, gitBranch });
        });
      }
    });
  }

  /**
   * 获取指定路径的Git状态。
   * @param path - 文件路径。
   * @returns Git状态对象。
   */
  static getGitState(path: string): GitState {
    const workspaceFolder = vscode.workspace.getWorkspaceFolder(vscode.Uri.file(path));
    const dir = workspaceFolder?.uri.fsPath || GitStateManager.defaultDir;
    const gitState = GitStateManager.gitStateMap.get(dir) || { gitUrl: '', gitRepo: '', gitBranch: '' };
    return { ...gitState };
  }
}
