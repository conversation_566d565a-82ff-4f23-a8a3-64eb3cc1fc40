// WASM支持的语言映射
export const languageIdToWasmLanguageMapping = {
  python: 'python',
  javascript: 'javascript',
  javascriptreact: 'javascript',
  jsx: 'javascript',
  typescript: 'typescript',
  typescriptreact: 'tsx',
  tsx: 'tsx',
  go: 'go',
  ruby: 'ruby',
  java: 'java',
  dart: 'dart',
};

export const getShortCutsList = () => {
  const list = [
    {
      name: '切换工作区',
      tooltip: '',
      iconPath: '',
      command: 'JoyCode.changeWorkSpace',
      icon: 'toggleWorkSpace.svg',
    },
    {
      name: '快速创建',
      tooltip: '',
      iconPath: '',
      command: 'JoyCode.createProject.start',
      icon: 'quickCreate.svg',
    },
    {
      name: '物料导入',
      tooltip: '',
      iconPath: '',
      command: 'JoyCode.materialImport.start',
      icon: 'importIcon.svg',
    },
    {
      name: '插件市场',
      tooltip: '',
      iconPath: '',
      command: 'JoyCode.marketplace.show',
      icon: 'marketplace.svg',
    },
    {
      name: '内置浏览器',
      tooltip: '',
      iconPath: '',
      command: 'JoyCode.browser.open',
      arguments: ['http://joycoder.jd.com'],
      icon: 'Chrome.svg',
    },
  ];
  return list;
};
export const settingArr = [
  {
    title: '工作区配置',
    command: 'JoyCode.settingWorkSpace',
    icon: 'workSpace.svg',
  },
  { title: '插件设置', command: 'JoyCode.openConfigPage', icon: 'vsSetting.svg' },
  {
    title: '快捷键设置',
    command: 'JoyCode.openGlobalKeybindings',
    icon: 'https://storage.360buyimg.com/jxfe/ppms/u/891523/keybindingsSetting.svg',
  },
  {
    title: '问题反馈',
    command: 'JoyCode.bugReport',
    icon: 'qaICON.svg',
  },
  { title: '检查更新', command: 'JoyCode.checkUpdate', icon: 'checkUpdate.svg' },
  {
    title: '退出登录',
    command: 'JoyCode.LogOut',
    icon: 'https://storage.360buyimg.com/jxfe/ppms/u/891523/logout.svg',
  },
  { title: '官方文档', command: 'JoyCode.openHomePage', url: 'http://joycoder.jd.com/', icon: 'index.svg' },
  {
    title: '常见问题',
    command: 'JoyCode.openFAQ',
    icon: 'qa.svg',
  },
  {
    title: '咚咚群',
    command: 'JoyCode.openME',
    icon: 'dongdong.svg',
  },
];
// 预发环境地址：http://jdhgpt-yf.healthjd.com/
// 生产环境地址：http://jdhgpt.jd.com
export const modelUrl = 'http://jdhgpt.jd.com';
export const modelPreUrl = 'http://jdhgpt-yf.healthjd.com';

// 代码补全模型配置

export const codeModelConfigs = [
  {
    label: 'JoyCode-Lite',
    description: 'JoyCoder代码模型，此模型回复更快速、简洁',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'JoyCoder-chat-m1', //聊天模型
      url: modelUrl + '/bigdata/sendGeneralStreamSync', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'JoyCoder-base', //聊天模型
      url: modelUrl + '/completion/autocompletion', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
  {
    label: 'JoyCode-Pro',
    description: 'JoyCoder代码模型，此模型回复更准确、丰富',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'JoyCoder-chat-m2', //聊天模型
      url: modelUrl + '/bigdata/sendGeneralStreamSync', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'JoyCoder-base', //聊天模型
      url: modelUrl + '/completion/autocompletion', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
];
export const chatModelConfigsLocal = [
  {
    label: 'JoyCode-Lite',
    description: '回复更快速、简洁',
    chatApiModel: 'llm',
    maxTotalTokens: 8192,
  },
];
export const codeModelConfigsLocal = [
  //本地部署配置，隐藏的模型列表，支持老版本可调用
  {
    label: 'JoyCode-Lite1',
    description: 'JoyCoder代码模型，此模型回复更快速、简洁',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'llm', //聊天模型
      url: modelUrl + '/bigdata/sendGeneralStreamSync', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'JoyCoder-Base', //聊天模型
      url: modelUrl + '/completion/autocompletion', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
  {
    label: 'JoyCode-Lite2',
    description: 'JoyCoder代码模型，此模型回复更准确、丰富',
    //新的格式，方便区分chat和completion
    chat: {
      model: 'llm', //聊天模型
      url: modelUrl + '/bigdata/sendGeneralStreamSync', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    },
    completion: {
      model: 'aix2_base', //聊天模型
      url: modelUrl + '/completion/autocompletion', //续写模型地址
      temperature: 0.1,
      max_tokens: 1500,
    },
  },
];

/** 前端相关的语言类型 */
export const FE_FILE_TYPES = ['javascript', 'javascriptreact', 'typescript', 'typescriptreact', 'vue'];

/* 兜底用户头像 */
export const DEFAULT_USER_AVATAR =
  'https://img11.360buyimg.com/img/jfs/t1/235690/22/14532/1724/65f17e7fF3cfc4ff6/a888d7db89639672.png';
// 'https://img12.360buyimg.com/img/jfs/t1/249255/7/5616/1729/65f17ddcF2870e340/e58fd6091b53072b.png';
// 'https://img20.360buyimg.com/img/jfs/t1/238476/12/5568/2671/65f17d4dF17a325aa/ab4d667b2c59d305.png';
// 'https://img11.360buyimg.com/img/jfs/t1/159783/18/36862/874/65f17a67F5e3ba3a0/cf1ad136f8130088.png';
// 'https://img11.360buyimg.com/img/jfs/t1/241777/32/5433/850/65f1171cFd337f990/1e6760179f21d731.png';

/* 兜底模型头像 */
export const DEFAULT_ASSISTANT_AVATAR =
  'https://img30.360buyimg.com/img/jfs/t1/222950/2/30082/4115/6521193cFc19f1365/b0070eb19e1f2566.png';
