/**
 * 读取操作package.json
 */
const path = require('path');
const jsonfile = require('jsonfile');

const _jsonPath = path.join(__dirname, '../package.json');

/**
 * @example
 * const pkg = readPkgJson();
 * pkg.contributes.configuration.properties['JoyCoder.config.workSpaceId'].enum = ['bbb'];
 * updatePkgJson(pkg);
 */

export function readPkgJson() {
  return jsonfile.readFileSync(_jsonPath);
}

export function updatePkgJson(val: any) {
  jsonfile.writeFileSync(_jsonPath, val, { spaces: 2 });
}
