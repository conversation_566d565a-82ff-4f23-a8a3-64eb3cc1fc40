import * as vscode from 'vscode';
import { ViewLoader, ILoaderOptions } from '@joycoder/web';

/**
 * 在浏览器中打开
 * @param url
 */
export function openInBrowser(url: string) {
  vscode.env.openExternal(vscode.Uri.parse(url));
}

/**
 * 在vscode中打开webview
 * @param options
 * @returns
 */
export function openInVscode(options: ILoaderOptions) {
  return ViewLoader.showWebview(options);
}

export { openInVscodeWithBrowser } from '@joycoder/plugin-base-browser';
/**
 * 在vscode中关闭webview
 * @param options
 * @returns
 */
// export function closeInVscode(options: ILoaderOptions) {
//   return ViewLoader.disposeWebview(options)
// }
