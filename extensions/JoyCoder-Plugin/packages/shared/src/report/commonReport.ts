/**
 * shared模块内部的ump上报请求，可用在vscode和webview两端，包括可用率上报及次数上报
 */
import axios from 'axios';
import jsonpAdapter from 'axios-jsonp';
import { to } from 'await-to-js';
import { serializeError } from 'serialize-error';
import { isBrowser } from '../env';
import { getUserInfo } from '../userInfo';

// https://ydn.jd.com/pm/wm/oper.html?biz=2057

const BIZ_ID = 2057;

export async function reportUmpInner(
  operation: string | number,
  result: string | number,
  message?: any,
  extInfo: any = {}
) {
  if (process.env.PLUGIN_VER === 'business') return;
  message = JSON.stringify(serializeError(message || ''));
  const escapedMessage = message.replace(/[,|\r\n]/g, ' ');
  const contents = [BIZ_ID, operation, result, 0, escapedMessage].join('|');

  const userInfo = getUserInfo();

  const [err, res] = await to(
    axios({
      method: 'GET',
      url: 'https://api.jingxi.com/api',
      params: {
        functionId: 'pingou_webmonitor_biz',
        appid: 'jx_h5',
        body: JSON.stringify({
          contents,
          t: Date.now(),
        }),
      },
      adapter: isBrowser ? jsonpAdapter : null,
      headers: {
        referer: `https://st.jingxi.com/joycoder/username-${userInfo.userName}/erp-${userInfo.erp}/index.html`,
        'user-agent': `Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1 userIdeVersion/${extInfo.userIdeVersion} userOs/${extInfo.userOs}`,
        'content-type': 'application/x-www-form-urlencoded',
        cookie: 'cid=joycoder; visitkey=joycoder;', // 若没有会被后台过滤
      },
    })
  );
  err && console.error('UMP上报异常：', err, res);
}
