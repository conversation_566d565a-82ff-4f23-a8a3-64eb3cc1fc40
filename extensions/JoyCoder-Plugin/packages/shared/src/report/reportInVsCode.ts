/**
 * vscode环境中上报
 */
import * as vscode from 'vscode';
// import * as fse from 'fs-extra';
// import * as path from 'path';
import { reportUmpInner } from './commonReport';
// import { PLUGIN_ID } from '../pluginId';

export function initReportRdCommand(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.reportRd', (rd) => {
      reportRd(rd);
    })
  );
}

export function reportFirstInstallTimes() {
  // const joycoderRootPath = vscode.extensions.getExtension(PLUGIN_ID)?.extensionPath || '';
  // const dirName = path.dirname(joycoderRootPath);
  // const firstInstallFlag = path.join(dirName, './joycode.first.install');
  // const isExist = fse.pathExistsSync(firstInstallFlag);
  // if (!isExist) {
  //   // 文件不存在，则认为是首次安装，可统计为安装次数
  //   reportRd(23);
  //   fse.ensureFile(firstInstallFlag);
  // }
}

const extInfo = {
  userIdeVersion: vscode.version,
  userOs: process.platform,
};

/**
 * 异步报告UMP信息
 * @param {string | number} operation - 操作类型
 * @param {string | number} result - 结果类型
 * @param {any} [message] - 可选的消息内容
 * @param {any} [extInfo] - 可选的扩展信息
 */
export async function reportUmp(...args: [string | number, string | number, any?, any?]) {
  const [operation, result, message] = args;
  reportUmpInner(operation, result, message, extInfo);
}

export function reportRd(rd: string | number) {
  reportUmpInner(rd, 1, '', extInfo);
}

export function reportError(...args: any[]) {
  // wq.webmonitor.joycoder.ErrorLog

  // eslint-disable-next-line prefer-rest-params
  reportUmpInner(45, 1, arguments, extInfo);
}
