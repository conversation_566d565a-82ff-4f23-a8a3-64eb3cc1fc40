import axios from 'axios';
import { getJdhLoginInfo } from '../loginJdh';
import to from 'await-to-js';
import { Choice, IActionReportParam, IExtendMsg } from './ationType';
import { getJdhCgiUrl, isIDE, isBusiness } from '../business';
import { getBaseUrl } from '../';
import { userName } from './actionReport';
import { GitStateManager } from '../git';
import { Logger } from '../logger';
import { getExtHeaders } from '@joycoder/agent-common/src/vscode/index';

interface ICompletionBaseReportParam {
  // 研发ide版本号，例如1.83.1或者IC-2022.3.365
  ideaVersion: string;
  // 插件版本号
  // jetbrains系列版本号规则：数字.数字.数字（例如：2.3.1）
  // vsCode版本号规则：vsfront-数字.数字.数字（例如：vsfront-1.2.1）
  // vsCode AI专业版版本号规则：vscode-数字.数字.数字（例如：vscode-1.2.1）
  pluginVersion: string;
  // 计算机名称（可选）
  computerName?: string;
  // 计算机域名（可选）
  computerDomain?: string;
  // 用户名，登录接口3.插件登录接口中入参的userName字段
  userName: string;
  // 用户Token，登录接口3.插件登录接口中获取登录结果中的userToken值
  userToken: string;
  // 电脑MAC地址
  mac: string;
  // 项目名称（可选）
  projectName?: string;
  // 当前文件名（可选）
  fileName: string;
  // 当前文件路径（可选）
  filePath: string;
}

interface ICompletionCustomReportParam {
  // 模型
  model: string;
  // 温度
  temperature: string;
  // 补全记录id
  completionId: string;
  // 光标位置
  offset?: number;
  // 前缀代码
  prefixCode: string;
  // 后缀代码
  sufixCode: string;
  // 代码语言
  codeLanguage: string;
  // 结果，采纳选项数据，请求前转换成json串
  choices: Choice[];
  // 结果，采纳选项的数组下标
  acceptIndex?: number;
  // 扩展上报信息
  extendMsg?: IExtendMsg;
}

type ICompletionReportParam = ICompletionBaseReportParam & ICompletionCustomReportParam;

/**
 * 上报到健康侧补全数据
 *
 * @param {ICompletionReportParam} reportParam
 */
export async function sendReportToCompletion(reportParam: ICompletionReportParam) {
  Logger.log('[ sendReportToCompletion to joycoder ]--->', reportParam);
  let reportUrl = 'http://jdhgpt.jd.com/completion/completionFeedback';
  if (isBusiness()) {
    reportUrl = getJdhCgiUrl(reportUrl);
  } else if (isIDE()) {
    reportUrl = getBaseUrl() + '/api/saas/stat/v1/log/completion';
  }
  Logger.log('[ completionReport ]---> reportUrl', reportUrl);
  const [err, res] = await to<{ data: { success: boolean } }>(
    axios.post(
      reportUrl,
      {
        ...reportParam,
        choices: JSON.stringify(reportParam.choices) || '',
        extendMsg: JSON.stringify(reportParam.extendMsg) || '',
      },
      {
        headers: getExtHeaders().headers,
      }
    )
  );
  (err || !res?.data?.success) && console.error('[Completion日志上报异常]:', err, res?.data, reportParam);
}

/**
 * 转换上报参数
 *
 * @param {IActionReportParam} actionParam
 * @return {*}  {ICompletionReportParam}
 */
export function convertActionReportToCompletionReport(actionParam: IActionReportParam): ICompletionReportParam {
  const jdhLoginInfo = getJdhLoginInfo();
  const gitState = GitStateManager.getGitState(actionParam.curFilePath);
  const completionParam: ICompletionReportParam = {
    // 从IActionBaseReportParam映射到ICompletionBaseReportParam
    ideaVersion: actionParam.ideVersion,
    pluginVersion: actionParam.pluginVersion,
    userName: jdhLoginInfo?.userName || userName || '',
    userToken: jdhLoginInfo?.userToken || '',
    mac: actionParam.mac,
    projectName: actionParam.projectName,
    fileName: actionParam.curFilePath.split(/[/\\]/).pop() || '',
    filePath: actionParam.curFilePath,

    // 模型
    model: actionParam.model || 'code-llama',
    // 温度
    temperature: actionParam.temperature || '0',
    // 补全记录id
    completionId: actionParam.conversationId || '',
    // 光标位置
    offset: actionParam.offset || 0,
    // 前缀代码
    prefixCode: actionParam.prefixCode || '',
    // 后缀代码
    sufixCode: actionParam.sufixCode || '',
    // 代码语言
    codeLanguage: actionParam.codeLanguage || 'js',
    // 结果，采纳选项数据，请求前转换成json串
    choices: actionParam.choices || [],
    // 结果，采纳选项的数组下标
    acceptIndex: actionParam.acceptIndex,
    // 扩展参数
    extendMsg: Object.assign(gitState, actionParam.extendMsg),
  };

  return completionParam;
}
