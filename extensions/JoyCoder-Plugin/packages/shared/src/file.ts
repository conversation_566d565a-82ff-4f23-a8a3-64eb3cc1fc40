import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as tar from 'tar';
import { Logger } from './logger';

/**
 * 获取当前聚焦的文件类型
 * @returns
 */
export function getCurrentFileType() {
  return vscode.window.activeTextEditor?.document?.languageId || '';
}

/**
 * 从当前文件/目录向上级查找目标isTarget，直到isEnd为true
 *
 * @param {string} filePath
 * @param {{
 *     isTarget: (path: string) => boolean;
 *     isEnd: (path: string, dir: string) => boolean;
 *   }} options
 * @return {*}
 */
export function getTargetPath(
  filePath: string,
  options: {
    isTarget: (path: string) => boolean;
    isEnd: (path: string, dir: string) => boolean;
  }
) {
  const _getTargetPath = (filePath: string): string => {
    // 1. 判断自己是否是目录，否则读取父级目录
    const stat = fs.lstatSync(filePath);
    const dir = stat.isDirectory() ? filePath : path.dirname(filePath);
    const files = fs.readdirSync(dir);
    // 2. 判断目录下是否有目标文件
    const targetFile = files.find((fileName) => options.isTarget(path.join(dir, fileName)));
    if (targetFile) return path.join(dir, targetFile);
    // 3. 判断是否可以结束
    if (options.isEnd(filePath, dir) || dir === '/') return '';
    // 4. 递归读取父级目录
    return _getTargetPath(path.dirname(dir));
  };
  return _getTargetPath(filePath);
}

/**
 * 根据文件路径获取项目根目录
 * @param filePath
 * @returns {string} rootDir
 */
export function getProjectRootDirByFilePath(filePath: string, targetFile = '.git'): string {
  if (filePath == '/') return ''; // 已遍历至系统根目录

  const stat = fs.lstatSync(filePath);
  const fileDir = stat.isDirectory() ? filePath : path.dirname(filePath);
  if (fs.existsSync(path.join(fileDir, targetFile))) {
    return fileDir;
  } else {
    return getProjectRootDirByFilePath(path.dirname(fileDir), targetFile);
  }
}

/**
 * 获取当前打开的项目根目录
 * 解决JoyCoder快捷操作、cmd+shift+p触发时vscode.commands.registerCommand回掉拿不到uri问题
 *
 * @export
 * @return {*}
 */
export function getCurrentWorkspaceUri(uri?: vscode.Uri, isLog = false) {
  // 1. 从目录右键菜单进入
  if (uri) {
    return uri;
  }
  // 2. 快捷命令进入
  // 2.1 获取当前工作区所有根文件夹数组
  const workspaceFolders = vscode.workspace.workspaceFolders;
  // 2.2 工作区暂无项目
  if (!workspaceFolders) {
    isLog && Logger.showErrorMessage('当前VSCode中暂无打开的项目，请打开项目后再操作哦~');
    return {
      fsPath: '',
    };
  }
  // 2.3 取工作区第一个项目
  if (workspaceFolders.length > 1) {
    // 工作区根项目最多只支持一个
    isLog &&
      Logger.showInformationMessage(
        '当前工作区有多个项目，JoyCoder已为您默认选择第一个项目。若要选择其他路径，请右击您要选择的目录'
      );
  }
  const workspaceFolder = workspaceFolders[0];
  // 索引、项目名称、URL
  const { uri: workspaceFolderUri } = workspaceFolder;
  return workspaceFolderUri;
}

/**
 * 获取资源文件的uri
 * @param fileName
 * @param childDirName
 * @returns
 */
export function getAssetsFilePath(fileName: string, childDirName?: string) {
  const res = vscode.Uri.parse(
    vscode.Uri.file(path.join(__dirname, `../assets${childDirName ? `/${childDirName}` : ''}`, fileName)).path
  );
  return res;
}

/**
 * 解压 .tar.gz 文件
 * @param inputPath 输入的 .tar.gz 文件路径
 * @param outputDir 输出的解压后的文件目录
 */
export async function decompressTarGzFile(inputPath: string, outputDir: string): Promise<void> {
  try {
    // 确保输出目录存在
    await fs.promises.mkdir(outputDir, { recursive: true });

    // 解压 .tar.gz 文件
    await tar.x({
      file: inputPath,
      cwd: outputDir,
    });

    console.log('解压完成');
  } catch (err) {
    console.error('解压失败:', err);
  }
}
