/**
 * 监听处理从vscode外部呼起插件的情况
 */
import * as vscode from 'vscode';

interface Router {
  path: string;
  callback: (uri: vscode.Uri) => void;
}

const routers: any = {};

export function addRouter(opt: Router) {
  routers[opt.path] = opt.callback;
}

export function registerRouter() {
  vscode.window.registerUriHandler({
    handleUri(uri: vscode.Uri): vscode.ProviderResult<void> {
      if (routers) {
        routers[uri.path](uri);
      }
    },
  });
}

export { routers, Router };
