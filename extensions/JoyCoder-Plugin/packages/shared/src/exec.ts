import spawn from 'cross-spawn';
import { execSync, ExecSyncOptionsWithStringEncoding } from 'child_process';

/**
 * 通过js脚本执行命令行
 * @param args
 * @returns
 * @example
 * exec('npm', ['list', '-g', '-depth', '0'], { stdio: 'inherit' });
 */
export const exec = (...args: Parameters<typeof spawn>) => {
  return new Promise((resolve, reject) => {
    spawn(...args)
      .on('close', resolve)
      .on('error', reject);
  });
};

/**
 * 安全的进行execSync
 *
 * @export
 * @param {string} command
 * @return {string}
 */
export function safeExecSync(command: string, options?: Partial<ExecSyncOptionsWithStringEncoding>): string {
  let result = '';
  try {
    result = execSync(command, { encoding: 'utf8', ...options });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    result = error?.stderr || error?.message || error?.output?.join('') || '';
  }
  return result;
}
