/**
 * 功能开关
 */
import * as vscode from 'vscode';
import { getVscodeConfig } from './config';

type SwitchFeature =
  | 'when.JoyCode.hoverProvider.jxComponent'
  | 'when.JoyCode.quickJump.searchH5BadJs'
  | 'when.JoyCode.quickJump.searchXcxBadJs';

const FeatureSwitchMap = {
  jxszfe: [
    'when.JoyCode.hoverProvider.jxComponent',
    'when.JoyCode.quickJump.searchXcxBadJs',
    'when.JoyCode.quickJump.searchH5BadJs',
  ],
};

/**
 * view切换的key
 *
 * @export
 */
export enum ViewSwitchKey {
  AIChatView = 'when.JoyCode.view.aichat',
  NavView = 'when.JoyCode.view.nav',
}

/**
 * 切换webview面板中展示的view
 *
 * @export
 * @param {ViewSwitchKey} key
 * @param {boolean} flag
 */
export function toggleViewShow(key: ViewSwitchKey, flag: boolean) {
  vscode.commands.executeCommand('setContext', key, flag);
}

/**
 * 可选功能是否开启
 * 供各个插件中需要单独判断的地方使用
 *
 * @export
 * @param {SwitchFeature} feature
 * @return {*}
 */
export function isFeatureSwitchOn(feature: SwitchFeature) {
  const current = getVscodeConfig('JoyCode.config.workSpaceId');

  return FeatureSwitchMap?.[current]?.includes(feature);
}

/**
 * 设置全局变量
 * 1. 供package.json中when子句使用，参考文档：https://liiked.github.io/VS-Code-Extension-Doc-ZH/#/references/when-clause-contexts
 *
 * @export
 * @return {*}
 */
export function initContextVariable() {
  toggleViewShow(ViewSwitchKey.AIChatView, true);
  toggleViewShow(ViewSwitchKey.NavView, false);

  const current = getVscodeConfig('JoyCode.config.workSpaceId');

  const currentList = FeatureSwitchMap[current];
  if (!currentList) return;

  currentList.forEach((feature: string) => {
    vscode.commands.executeCommand('setContext', feature, true);
  });
}
