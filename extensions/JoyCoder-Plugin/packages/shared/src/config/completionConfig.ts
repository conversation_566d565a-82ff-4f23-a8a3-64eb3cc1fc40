import axios from 'axios';
import { to } from 'await-to-js';
import { getActionBaseParam } from '../report/actionReport';
import { getJdhLoginInfo } from '../loginJdh';
import { reportUmp } from '../report/reportInVsCode';
import { getJdhCgiUrl, modelUrl, MAX_CACHE_MINUTES, setVscodeConfig, isIDE, getBaseUrl } from '../';
import moment from 'moment';
interface ICompletionConfigParam {
  // 研发ide版本号，例如1.83.1或者IC-2022.3.365
  ideaVersion?: string;
  // 插件版本号
  // jetbrains系列版本号规则：数字.数字.数字（例如：2.3.1）
  // vsCode版本号规则：vsfront-数字.数字.数字（例如：vsfront-1.2.1）
  // vsCode AI专业版版本号规则：vscode-数字.数字.数字（例如：vscode-1.2.1）
  pluginVersion?: string;
  userName?: string;
  // 用户Token，登录接口3.插件登录接口中获取登录结果中的userToken值
  userToken?: string;
  // IDE场景类型
  sceneType?: string;
}
interface IModelInfo {
  model: string; // 聊天模型
  url: string; // 聊天模型地址
  temperature: number;
  max_tokens: number;
}
interface ICompletionModelConfigParam {
  label: string;
  description: string;
  chat: IModelInfo;
  completion: IModelInfo;
}
// 缓存数据和缓存时间
let completionModelConfig: ICompletionModelConfigParam[] = [];
let cacheTime = 0;

export async function getCompletionConfig() {
  const config = getActionBaseParam();
  const userInfo = getJdhLoginInfo();
  const params: ICompletionConfigParam = isIDE()
    ? {
        sceneType: 'completion',
      }
    : {
        ideaVersion: config?.ideVersion,
        pluginVersion: config.pluginVersion,
        userName: userInfo?.userName || '',
        userToken: userInfo?.userToken || '',
      };
  const completionConfigUrl = isIDE()
    ? `${getBaseUrl()}/api/saas/models/v1/config/getPluginRunBaseConfig`
    : `${modelUrl}/completion/completionConfig`;
  const [err, res]: [any, any] = await to(
    axios.post(getJdhCgiUrl(completionConfigUrl), {
      ...params,
    })
  );
  // @ts-ignore
  const completionConfig = isIDE() ? res?.data?.data?.completion : res?.data?.data;
  if (err || !completionConfig) {
    reportUmp(40, 1001, err || res);
  }

  return completionConfig;
}
/**
 * 获取补全模型配置
 *
 * @returns 插件运行的基础配置。
 */
export async function getPluginRunBaseConfig() {
  const currentTime = Date.now();
  // 检查缓存是否存在且未过期
  if (completionModelConfig?.length > 0 && !isExpired()) {
    return completionModelConfig;
  }
  const params = {
    sceneType: 'completion',
  };
  const pluginRunBaseConfigUrl = isIDE()
    ? `${getBaseUrl()}/api/saas/models/v1/config/getPluginRunBaseConfig`
    : `${modelUrl}/config/getPluginRunBaseConfig`;
  const [err, res]: [any, any] = await to(
    axios.post(getJdhCgiUrl(pluginRunBaseConfigUrl), {
      ...params,
    })
  );
  const completion = res?.data?.completion || res?.data?.data?.completion;
  if (err || !completion) {
    reportUmp(40, 1001, err || res);
    return []; // 返回空数组以防止后续代码出错
  }
  const modelOptions = completion?.modelOptions;
  if (modelOptions) {
    completionModelConfig = modelOptions.map((item) => {
      return {
        label: item.showName,
        description: item?.description ?? 'JoyCoder代码模型，此模型回复更快速、简洁',
        chat: {
          model: item.chatModel, // 聊天模型
          url: item.chatModelUrl, // 聊天模型地址
          // url: getJdhCgiUrl(modelUrl + '/bigdata/sendGeneralStreamSync'), // 聊天模型地址
          temperature: 0.1,
          max_tokens: item.chatModelMaxTokens ?? 2048,
        },
        completion: {
          model: item.code, // 聊天模型
          url: item.modelUrl, // 续写模型地址
          temperature: 0.1,
          max_tokens: item.maxTokens,
        },
      };
    });
  }

  // 更新缓存数据和缓存时间
  cacheTime = currentTime;

  return completionModelConfig;
}

function isExpired() {
  const now = moment(new Date());
  const diff = now.diff(moment(cacheTime), 'minute');
  if (diff >= MAX_CACHE_MINUTES) return true;
}
//  同步后端跨文件感知设置
export async function updateCodeCompletionsMoreContext() {
  const [err, res]: [any, any] = await to(canUpdate('CROSS_FILES'));
  const checkResult = res?.data?.data?.checkResult;
  if (err) {
    return;
  }
  setVscodeConfig('JoyCode.config.codeCompletionsMoreContext', !!checkResult);
}
// 获取用户灰度信息
export async function canUpdate(feature) {
  const userInfo = getJdhLoginInfo();
  const params = {
    userName: userInfo?.userName || '',
    userToken: userInfo?.userToken || '',
    feature,
  };
  const canUpdateUrl = isIDE() ? `${getBaseUrl()}/api/saas/release/v1/canUpdate` : `${modelUrl}/plugin/canUpdate`;
  const headers = getFetchHeader();

  const [err, res]: [any, any] = await to(
    axios.post(canUpdateUrl, isIDE() ? {} : { ...params }, isIDE() ? { headers } : undefined)
  );
  if (err) {
    reportUmp(40, 1001, err || res);
    throw new Error(err);
  }
  return res;
}

function getFetchHeader() {
  const baseHeaders = {
    'Content-Type': 'application/json; charset=UTF-8',
  };
  const jdhLoginInfo = getJdhLoginInfo();
  const headers =
    isIDE() && jdhLoginInfo?.ptKey
      ? {
          ...baseHeaders,
          ptKey: jdhLoginInfo.ptKey,
          ...(jdhLoginInfo?.loginType && { loginType: jdhLoginInfo?.loginType }),
        }
      : baseHeaders;
  return headers;
}

//  获取仓库问答灰度状态
export async function getRepositoryStatus() {
  const [err, res]: [any, any] = await to(canUpdate('REPO_ASK'));
  const data = res?.data?.data?.checkResult;
  if (err) {
    return null;
  }
  return data;
}
//  获取自动化编程配置模型
export async function getAutoCodeModel() {
  const [err, res]: [any, any] = await to(canUpdate('AI_CODER_MODEL_DEFAULT'));
  const data = res?.data?.data?.optionValue;
  if (err) {
    return null;
  }
  return data;
}
