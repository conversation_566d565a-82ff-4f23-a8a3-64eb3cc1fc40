{"name": "plugin-demo", "displayName": "插件名称", "publisher": "JoyCoder", "description": "插件描述", "version": "3.1.2", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "icon": "assets/logo.png", "contributes": {"commands": []}, "sideEffects": false, "scripts": {"compile": "webpack", "watch": "webpack --watch", "build": "webpack --mode production --devtool hidden-source-map", "package": "pnpm build && vsce package --allow-star-activation --no-dependencies --allow-missing-repository", "publish": "vsce publish --allow-star-activation --no-dependencies --allow-missing-repository"}, "repository": {"type": "git", "url": ""}, "dependencies": {"@joycoder/shared": "workspace:*"}}