import * as vscode from 'vscode';
import { parse, ParserPlugin } from '@babel/parser';
import traverse, { NodePath } from '@babel/traverse';
import { ImportDeclaration } from '@babel/types';
import { join } from 'path';

function getBabelParserPlugins(language: 'ts' | 'js'): ParserPlugin[] {
  const plugins: ParserPlugin[] = [
    'jsx',
    'doExpressions',
    'objectRestSpread',
    'decorators-legacy',
    'classProperties',
    'exportDefaultFrom',
    'exportNamespaceFrom',
    'asyncGenerators',
    'functionBind',
    'functionSent',
    'dynamicImport',
  ];

  if (language === 'ts') {
    plugins.unshift('typescript');
  } else {
    plugins.unshift('flow');
  }

  return plugins;
}

export default (doc: vscode.TextDocument): Set<string> => {
  const importSet: Set<string> = new Set();
  const documentText = doc.getText();
  try {
    const ast = parse(documentText, {
      sourceType: 'module',
      plugins: getBabelParserPlugins('ts'),
      errorRecovery: true,
    });
    traverse(ast, {
      ImportDeclaration(path: NodePath<ImportDeclaration>) {
        const { node } = path;
        const moduleName = node.source.value;
        importSet.add(join(moduleName));
      },
    });
  } catch (e) {
    return importSet;
  }

  return importSet;
};
