import * as vscode from 'vscode';
import * as path from 'path';
import getFilenameWithoutExtname from './getFilenameWithoutExtname';

export default function (itemText: string, moduleValue?: string): vscode.CompletionItem {
  const isNoModuleValue = moduleValue === ''; // import './index.scss';
  const moduleName = moduleValue || getFilenameWithoutExtname(path.basename(itemText));
  const completionItem = new vscode.CompletionItem(
    isNoModuleValue ? `import '${itemText}';` : `import ${moduleName} from '${itemText}';`,
    vscode.CompletionItemKind.Variable
  );

  moduleValue = moduleValue === '{  }' ? '{ ${1} }' : `\${1:${moduleName}}`;
  completionItem.detail = 'JoyCode';
  completionItem.insertText = new vscode.SnippetString(
    isNoModuleValue ? `import '${itemText}';` : `import ${moduleValue} from '${itemText}';`
  );
  completionItem.command = {
    title: 'reportRd',
    command: 'JoyCode.reportRd',
    arguments: [8],
  };

  return completionItem;
}
