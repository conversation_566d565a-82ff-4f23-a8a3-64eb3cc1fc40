import * as vscode from 'vscode';
import * as path from 'path';
import getCompletionItem from './getCompletionItem';
import getFilenameWithoutExtname from './getFilenameWithoutExtname';

interface IValidateRule {
  validateCallback: any;
  args: any[];
}

function checkValidateOfTsDeclarationFile(importFileName: string): boolean {
  return !importFileName.endsWith('.d.ts');
}

function checkValidateOfNoStartWithDot(importFileName: string): boolean {
  return !importFileName.startsWith('.');
}

function checkValidateOfExtName(importFileName: string): boolean {
  return /\.(js|ts|jsx|tsx|vue|css|less|scss)$/.test(importFileName);
}

function checkValidateOfAlreadyImport(importFileName: string, alreadyImportSet: Set<string>): boolean {
  return !alreadyImportSet.has(importFileName);
}

function checkValidateOfCurrentFile(importFileName: string, currentFilename: any): boolean {
  return importFileName !== currentFilename;
}

const checkIsValidate = (validateRules: IValidateRule[]) => {
  return validateRules.every((validateRule) => {
    return validateRule.validateCallback(...validateRule.args);
  });
};

function getItemsFromFile(
  filename: string,
  importFilename: string,
  alreadyImportSet: Set<string>
): vscode.CompletionItem[] {
  const items: vscode.CompletionItem[] = [];

  const importModuleCssRegExp = /\.module\.(less|css|scss)$/;
  const importCssRegExp = /\.(css|less|scss)$/;
  const importSourceValue = `./${getFilenameWithoutExtname(importFilename)}`;
  const validateRules: IValidateRule[] = [
    {
      validateCallback: checkValidateOfExtName,
      args: [importFilename],
    },
    {
      validateCallback: checkValidateOfNoStartWithDot,
      args: [importFilename],
    },
    {
      validateCallback: checkValidateOfTsDeclarationFile,
      args: [importFilename],
    },
    {
      validateCallback: checkValidateOfAlreadyImport,
      args: [path.join(importSourceValue), alreadyImportSet],
    },
    {
      validateCallback: checkValidateOfCurrentFile,
      args: [importFilename, filename],
    },
  ];
  if (checkIsValidate(validateRules)) {
    if (importModuleCssRegExp.test(importSourceValue)) {
      items.push(getCompletionItem(importSourceValue, 'styles'));
    } else if (importCssRegExp.test(importSourceValue)) {
      items.push(getCompletionItem(importSourceValue, ''));
    } else {
      items.push(getCompletionItem(importSourceValue));
    }
  }
  return items;
}

/**
 *  import styles from './index.module.scss';
 *  import utils from './utils';
 *  import xxx from './xxx';
 *  import './index.scss';
 */
export default async (
  filename: string,
  directoryPath: string,
  alreadyImportSet: Set<string>
): Promise<vscode.CompletionItem[]> => {
  const items: vscode.CompletionItem[] = [];
  try {
    const directoryUri = vscode.Uri.parse(directoryPath);
    const files = await vscode.workspace.fs.readDirectory(directoryUri);
    for (const file of files) {
      const [importFilename, fileType] = [file[0], file[1]];
      if (fileType === vscode.FileType.File) {
        items.push(...getItemsFromFile(filename, importFilename, alreadyImportSet));
      }
    }
  } catch (e) {
    console.error(e);
  }
  return items;
};
