import * as vscode from 'vscode';
import getCompletionItem from './getCompletionItem';

/**
 * 框架相关
 */
export default (projectFramework: string, alreadyImportSet: Set<string>): vscode.CompletionItem[] => {
  const items: vscode.CompletionItem[] = [];
  if (projectFramework === 'taro' && !alreadyImportSet.has('@tarojs/taro')) {
    items.push(getCompletionItem('@tarojs/taro', 'Taro, {  }'));
    items.push(getCompletionItem('@tarojs/components', '{  }'));
    items.push(getCompletionItem('react', '{  }'));
  } else if (projectFramework === 'react' && !alreadyImportSet.has('react')) {
    items.push(getCompletionItem('react', '{  }'));
  } else if (projectFramework === 'vue' && !alreadyImportSet.has('vue')) {
    items.push(getCompletionItem('vue', '{  }'));
  }
  return items;
};
