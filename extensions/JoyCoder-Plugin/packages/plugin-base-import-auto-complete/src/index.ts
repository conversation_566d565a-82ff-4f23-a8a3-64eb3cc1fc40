import * as vscode from 'vscode';
import * as path from 'path';
import { FE_FILE_TYPES, getProjecctFramework } from '@joycoder/shared';
import getAlreadyImportSet from './getAlreadyImportSet';
import getCommonItems from './getCommonItems';
import getStoreItems from './getStoreItems';
import getFrameworkItems from './getFrameworkItems';
import getComponentItems from './getComponentItems';
import getFileItems from './getFileItems';

async function provideCompletionItems(
  document: vscode.TextDocument,
  position: vscode.Position
): Promise<vscode.CompletionItem[]> {
  const text: string = document.getText(document.getWordRangeAtPosition(position));
  if (!'import'.startsWith(text)) {
    return [];
  }

  const items: vscode.CompletionItem[] = [];
  const alreadyImportSet = getAlreadyImportSet(document);
  const projectFramework = getProjecctFramework();
  const filename = path.basename(document.fileName);
  const filePath = path.dirname(document.fileName);
  const directoryPath = path.dirname(document.fileName);

  items.push(...(await getStoreItems(filename, filePath, projectFramework, alreadyImportSet)));
  items.push(...(await getCommonItems(filePath)));
  items.push(...(await getComponentItems(filename, filePath, alreadyImportSet)));
  items.push(...(await getFileItems(filename, directoryPath, alreadyImportSet)));
  items.push(...getFrameworkItems(projectFramework, alreadyImportSet));
  return items;
}

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.languages.registerCompletionItemProvider(
      FE_FILE_TYPES,
      {
        provideCompletionItems,
      },
      'import',
      ' '
    )
  );
}
