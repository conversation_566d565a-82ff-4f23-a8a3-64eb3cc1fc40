import * as vscode from 'vscode';
import * as path from 'path';
import getCompletionItem from './getCompletionItem';
import getFilenameWithoutExtname from './getFilenameWithoutExtname';

/**
 *  import xxx from './common'
 */
export default async (filePath: string): Promise<vscode.CompletionItem[]> => {
  const items: vscode.CompletionItem[] = [];
  try {
    const componentsDirectoryPath = path.resolve(filePath, 'common');
    const componentsDirectoryUri = vscode.Uri.parse(componentsDirectoryPath);
    const files = await vscode.workspace.fs.readDirectory(componentsDirectoryUri);
    files.forEach((file) => {
      const importSourceValue = `./common/${getFilenameWithoutExtname(file[0])}`;
      items.push(getCompletionItem(importSourceValue));
    });
  } catch (err) {
    // ignore;
  }
  return items;
};
