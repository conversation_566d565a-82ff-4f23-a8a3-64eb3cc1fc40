const { merge } = require('webpack-merge');
const parentConfig = require('../../webpack.config');
const { DefinePlugin } = require('webpack');
// 获取父配置中的 process.env 定义
const parentEnv =
  parentConfig.plugins?.find((plugin) => plugin instanceof DefinePlugin && plugin.definitions?.['process.env'])
    ?.definitions?.['process.env'] || {};

/** @type WebpackConfig */
let extensionConfig = {
  plugins: [
    new DefinePlugin({
      'process.env': {
        ...parentEnv,
        PLUGIN_VER: JSON.stringify(process.env.PLUGIN_VER),
      },
    }),
  ],
};

module.exports = merge(parentConfig, extensionConfig);
