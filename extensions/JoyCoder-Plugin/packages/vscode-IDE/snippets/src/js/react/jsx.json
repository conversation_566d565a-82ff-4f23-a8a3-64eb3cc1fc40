{"Fragment": {"key": "Fragment", "prefix": ["Fragment", "<>"], "body": ["<>", "  ${1}", "</>"], "description": "<Fragment> 通常使用 <>...</> 代替，它们都允许你在不添加额外节点的情况下将子元素组合。"}, "Profiler": {"key": "Profiler", "prefix": ["Profiler"], "body": ["<Profiler id=\"${1:App}\" onRender={${2:onRender}}>", "  ${3}", "</Profiler>"], "description": "<Profiler> 允许你编程式测量 React 树的渲染性能。"}, "StrictMode": {"key": "StrictMode", "prefix": ["StrictMode"], "body": ["<StrictMode>", "  ${1}", "</StrictMode>"], "description": "<StrictMode> 帮助你在开发过程中尽早地发现组件中的常见错误。"}, "Suspense": {"key": "Suspense", "prefix": ["Suspense"], "body": ["<Suspense fallback={<${1:Loading} />}>", "  ${2}", "</Suspense>"], "description": "<Suspense> 允许在子组件完成加载前展示一个 fallback。"}}