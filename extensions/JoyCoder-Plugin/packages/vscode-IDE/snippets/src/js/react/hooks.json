{"useCallback": {"key": "useCallback", "prefix": "useCallback", "body": ["useCallback(() => {", "  ${1}", "}, [${2}])"], "description": "useCallback 是一个允许你在多次渲染中缓存函数的 React Hook。"}, "useContext": {"key": "useContext", "prefix": "useContext", "body": ["const ${1:myContext} = useContext(${2})"], "description": "useContext 是一个 React Hook，可以让你读取和订阅组件中的 context。"}, "useDebugValue": {"key": "useDebugValue", "prefix": "useDebugValue", "body": ["useDebugValue(${1}, data => ${2});"], "description": "useDebugValue 是一个 React Hook，可以让你在 React 开发工具 中为自定义 Hook 添加标签。"}, "useDeferredValue": {"key": "useDeferredValue", "prefix": "useDeferredValue", "body": ["const ${1:deferredVal} = useDeferredValue(${2})"], "description": "useDeferredValue 是一个 React Hook，可以让你延迟更新 UI 的某些部分。"}, "useEffect": {"key": "useEffect", "prefix": "useEffect", "body": ["useEffect(() => {", "  ${1}", "", "  return () => {", "    ${2}", "  }", "}, [${3}])", ""], "description": "useEffect 是一个 React Hook，它允许你 将组件与外部系统同步。"}, "useId": {"key": "useId", "prefix": "useId", "body": ["const ${1:id} = useId(${2})"], "description": "useId 是一个 React Hook，可以生成传递给无障碍属性的唯一 ID。"}, "useImperativeHandle": {"key": "useImperativeHandle", "prefix": "useImperativeHandle", "body": ["useImperativeHandle(${1}, () => {", "  return {", "    ${2}", "  };", "}, [${3}]);"], "description": "useImperativeHandle 是 React 中的一个 Hook，它能让你自定义由 ref 暴露出来的句柄。"}, "useInsertionEffect": {"key": "useInsertionEffect", "prefix": "useInsertionEffect", "body": ["useInsertionEffect(() => {", "  ${1}", "});"], "description": "useInsertionEffect 可以在布局副作用触发之前将元素插入到 DOM 中。"}, "useLayoutEffect": {"key": "useLayoutEffect", "prefix": "useLayoutEffect", "body": ["useLayoutEffect(() => {", "  ${1}", "", "  return () => {", "    ${2}", "  };", "}, [${3}])"], "description": "useLayoutEffect 是 useEffect 的一个版本，在浏览器重新绘制屏幕之前触发。"}, "useMemo": {"key": "useMemo", "prefix": "useMemo", "body": ["useMemo(() => ${1}, [${2}])"], "description": "useMemo 是一个 React Hook，它在每次重新渲染的时候能够缓存计算的结果。"}, "useReducer": {"key": "useReducer", "prefix": "useReducer", "body": ["const [state, dispatch] = useReducer(${1}, ${2}, ${3})"], "description": "useReducer 是一个 React Hook，它允许你向组件里面添加一个 reducer。"}, "useRef": {"key": "useRef", "prefix": "useRef", "body": ["const ${1:value} = useRef(${2})"], "description": "useRef 是一个 React Hook，它能让你引用一个不需要渲染的值。"}, "useState": {"key": "useState", "prefix": "useState", "body": ["const [${1}, set${1/(.*)/${1:/capitalize}/}] = useState(${2})"], "description": "useState 是一个 React Hook，它允许你向组件添加一个 状态变量。"}, "useSyncExternalStore": {"key": "useSyncExternalStore", "prefix": "useSyncExternalStore", "body": ["const ${1} = useSyncExternalStore(${2}, ${3}, ${4})"], "description": "useSyncExternalStore 是一个让你订阅外部 store 的 React Hook。"}, "useTransition": {"key": "useTransition", "prefix": "useTransition", "body": ["const [${1:isPending}, ${2:startTransition}] = useTransition();"], "description": "useTransition 是一个帮助你在不阻塞 UI 的情况下更新状态的 React Hook。"}}