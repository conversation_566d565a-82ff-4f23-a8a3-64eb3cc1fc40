{"Taro Hooks": {"prefix": "use", "body": ["${1|useLaunch,useError,usePageNotFound,useUnhandledRejection,useRouter,useLoad,useReady,useDidShow,useDidHide,useUnload,usePullDownRefresh,useReachBottom,usePageScroll,useResize,useShareAppMessage,useTabItemTap,useAddToFavorites,useShareTimeline,useSaveExitState,useTitleClick,useOptionMenuClick,usePullIntercept|}((res) => {", "\tconsole.log('$1', res)", "\t${2}", "})"], "description": "Taro Hooks: https://taro-docs.jd.com/docs/hooks"}, "useLaunch": {"prefix": ["use", "useLaunch"], "body": ["useLaunch((options) => {", "\tconsole.log('onLaunch', options)", "\t${1}", "})"], "description": "等同于 App 入口的 onLaunch 生命周期钩子。"}, "useError": {"prefix": ["use", "useError"], "body": ["useError((error) => {", "\tconsole.log('useError', error)", "\t${1}", "})"], "description": "等同于 App 入口的 onError 生命周期钩子。"}, "usePageNotFound": {"prefix": ["use", "usePageNotFound"], "body": ["usePageNotFound((res) => {", "\tconsole.log('usePageNotFound', res)", "\tTaro.redirectTo({", "\t\turl: '${1:pages/...}',", "\t})", "})"], "description": "等同于 App 入口的 onPageNotFound 生命周期钩子。"}, "useUnhandledRejection": {"prefix": ["use", "useUnhandledRejection"], "body": ["useUnhandledRejection((res) => {", "\tconsole.log('useUnhandledRejection', res.reason, res.promise)", "\t${1}", "})"], "description": "等同于 App 入口的 onUnhandledRejection 生命周期钩子。"}, "useRouter": {"prefix": ["use", "useRouter"], "body": ["const { ${1:params} } = useRouter()", "${2}"], "description": "等同于 Class Component 的 getCurrentInstance().router。"}, "useLoad": {"prefix": ["use", "useLoad"], "body": ["useLoad((params) => {", "\tconsole.log('onLoad', params)", "\t${1}", "})"], "description": "等同于页面的 onLoad 生命周期钩子。"}, "useReady": {"prefix": ["use", "useReady"], "body": ["useReady(() => {", "\tconsole.log('useReady')", "\t${1}", "})"], "description": "等同于页面的 onReady 生命周期钩子。"}, "useDidShow": {"prefix": ["use", "useDidShow"], "body": ["useDidShow(() => {", "\tconsole.log('useDidShow')", "\t${1}", "})"], "description": "页面显示/切入前台时触发。等同于 onShow 页面生命周期钩子。"}, "useDidHide": {"prefix": ["use", "useDidHide"], "body": ["useDidHide(() => {", "\tconsole.log('useDidHide')", "\t${1}", "})"], "description": "页面隐藏/切入后台时触发。等同于 onHide 页面生命周期钩子。"}, "useUnload": {"prefix": ["use", "useUnload"], "body": ["useUnload(() => {", "\tconsole.log('useUnload')", "\t${1}", "})"], "description": "等同于页面的 onUnload 生命周期钩子。"}, "usePullDownRefresh": {"prefix": ["use", "usePullDownRefresh"], "body": ["usePullDownRefresh(() => {", "\tconsole.log('usePullDownRefresh')", "\tsetTimeout(Taro.stopPullDownRefresh, ${1:1000})", "})"], "description": "监听用户下拉动作。等同于 onPullDownRefresh 页面生命周期钩子。"}, "useReachBottom": {"prefix": ["use", "useReachBottom"], "body": ["useReachBottom(() => {", "\tconsole.log('useReachBottom')", "\t${1}", "})"], "description": "监听用户上拉触底事件。等同于 onReachBottom 页面生命周期钩子。"}, "usePageScroll": {"prefix": ["use", "usePageScroll"], "body": ["usePageScroll((res) => {", "\tconsole.log('usePageScroll', res.scrollTop)", "\t${1}", "})"], "description": "监听用户滑动页面事件。等同于 onPageScroll 页面生命周期钩子。"}, "useResize": {"prefix": ["use", "useResize"], "body": ["useResize((res) => {", "\tconsole.log('useResize', res.size.windowWidth)", "\tconsole.log('useResize', res.size.windowHeight)", "\t${1}", "})"], "description": "小程序屏幕旋转时触发。等同于 onResize 页面生命周期钩子。"}, "useShareAppMessage": {"prefix": ["use", "useShareAppMessage"], "body": ["useShareAppMessage((res) => {", "\tif (res.from === 'button') {", "\t\tconsole.log(res)", "\t}", "\treturn {", "\t\ttitle: '${1:自定义转发标题}',", "\t\tpath: '${2:/page/user?id=123}',", "\t\timageUrl: '${3}'", "\t}", "})"], "description": "监听用户点击页面内转发按钮（Button 组件 openType='share'）或右上角菜单“转发”按钮的行为，并自定义转发内容。等同于 onShareAppMessage 页面生命周期钩子。"}, "useTabItemTap": {"prefix": ["use", "useTabItemTap"], "body": ["useTabItemTap((item) => {", "\tconsole.log(item.index)", "\tconsole.log(item.pagePath)", "\tconsole.log(item.text)", "\t${1}", "})"], "description": "点击 tab 时触发。等同于 onTabItemTap 页面生命周期钩子。"}, "useAddToFavorites": {"prefix": ["use", "useAddToFavorites"], "body": ["useAddToFavorites((res) => {", "\tconsole.log('useAddToFavorites', res)", "\treturn {", "\t\ttitle: '${1:自定义标题}',", "\t\timageUrl: '${2:https://demo.png}',", "\t\tquery: '${3:name=xxx&age=xxx}',", "\t}", "})"], "description": "监听用户点击右上角菜单“收藏”按钮的行为，并自定义收藏内容。等同于 onAddToFavorites 页面生命周期钩子。"}, "useShareTimeline": {"prefix": ["use", "useShareTimeline"], "body": ["useShareTimeline(() => {", "\tconsole.log('useShareTimeline')", "\t${1}", "})"], "description": "监听右上角菜单“分享到朋友圈”按钮的行为，并自定义分享内容。等同于 onShareTimeline 页面生命周期钩子。"}, "useSaveExitState": {"prefix": ["use", "useSaveExitState"], "body": ["useSaveExitState(() => {", "\tconst exitState = ${1:{ myDataField: 'myData' }} // 需要保存的数据", "\treturn {", "\t\tdata: exitState,", "\t\texpireTimeStamp: ${2:Date.now() + 24 * 60 * 60 * 1000}, // 超时时刻", "\t}", "})"], "description": "每当小程序可能被销毁之前，页面回调函数 onSaveExitState 会被调用，可以进行退出状态的保存。"}, "useTitleClick": {"prefix": ["use", "useTitleClick"], "body": ["useTitleClick(() => {", "\tconsole.log('onTitleClick')", "\t${1}", "})"], "description": "只有支付宝小程序支持。等同于 onTitleClick 页面生命周期钩子。"}, "useOptionMenuClick": {"prefix": ["use", "useOptionMenuClick"], "body": ["useOptionMenuClick(() => {", "\tconsole.log('useOptionMenuClick')", "\t${1}", "})"], "description": "只有支付宝小程序支持。等同于 onOptionMenuClick 页面生命周期钩子。"}, "usePullIntercept": {"prefix": ["use", "usePullIntercept"], "body": ["usePullIntercept(() => {", "\tconsole.log('usePullIntercept')", "\t${1}", "})"], "description": "只有支付宝小程序支持。等同于 onPullIntercept 页面生命周期钩子。"}}