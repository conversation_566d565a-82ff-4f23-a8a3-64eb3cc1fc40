{"判断TARO_ENV": {"prefix": ["iftaroenv", "ta<PERSON><PERSON>v", "ta<PERSON>f"], "body": ["if (process.env.TARO_ENV === '${1|weapp,h5,qq,jd,rn,swan,alipay,tt|}') {", "$0", "}"], "description": "Taro 条件编译语法（适用于JS/TS中）"}, "判断TARO_ENV含ELSE": {"prefix": ["iftaroenvelse", "taroenvelse", "taroifelse"], "body": ["if (process.env.TARO_ENV === '${1|weapp,h5,qq,jd,rn,swan,alipay,tt|}') {", "$3", "} else if (process.env.TARO_ENV === '${2|weapp,h5,qq,jd,rn,swan,alipay,tt|}') {", "$0", "}"], "description": "<PERSON>ro 条件编译语法，含else（适用于JS/TS中）"}, "判断TARO_ENV(JSX中使用)": {"prefix": ["iftaroenvjsx", "taroenvjsx", "taroifjsx"], "body": ["{ process.env.TARO_ENV === '${1|weapp,h5,qq,jd,rn,swan,alipay,tt|}' && $0 }"], "description": "Taro 条件编译语法（适用于JSX/TSX中）"}, "判断NODE_ENV": {"prefix": ["nodeenv", "process.env.NODE_ENV"], "body": ["if (process.env.NODE_ENV === '${1|development,production|}') {", "$0", "}"]}, "Taro判断IOS or 安卓": {"prefix": ["taroenvplatform", "android", "ios"], "body": ["if (Taro.getSystemInfoSync().platform === '${1|android,ios|})' {", "$0", "}"]}}