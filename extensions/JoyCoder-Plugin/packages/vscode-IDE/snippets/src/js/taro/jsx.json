{"PageMeta": {"prefix": "PageMeta", "body": ["<PageMeta className=\"${1}\" pageStyle=\"${2}\">", "\t${3}", "</PageMeta>"], "description": "页面属性配置节点，用于指定页面的一些属性、监听页面事件。只能是页面内的第一个节点。可以配合 navigation-bar 组件一同使用。 通过这个节点可以获得类似于调用 Taro.setBackgroundTextStyle Taro.setBackgroundColor 等接口调用的效果。"}, "CoverImage": {"prefix": "CoverImage", "body": ["<CoverImage className=\"${1}\" src=\"${2}\"></CoverImage>"], "description": "覆盖在原生组件之上的图片视图。可覆盖的原生组件同cover-view，支持嵌套在cover-view里。"}, "CoverView": {"prefix": "CoverView", "body": ["<CoverView className=\"${1}\" onClick=\"${2}\">", "\t${3}", "</CoverView>"], "description": "覆盖在原生组件之上的文本视图。可覆盖的原生组件包括 map、video、canvas、camera、live-player、live-pusher 只支持嵌套 cover-view、cover-image，可在 cover-view 中使用 button。"}, "GridView": {"prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ["<GridView className=\"${1}\">", "\t${2}", "</GridView>"], "description": "网格布局容器，仅支持作为 scroll-view 自定义模式下的直接子节点或 sticky-section 组件直接子节点。仅 Skyline 支持。"}, "ListView": {"prefix": "ListView", "body": ["<ListView className=\"${1}\">", "\t${2}", "</ListView>"], "description": "列表布局容器，仅支持作为 scroll-view 自定义模式下的直接子节点或 sticky-section 组件直接子节点。仅 Skyline 支持。"}, "CustomWrapper": {"prefix": "CustomWrapper", "body": ["<CustomWrapper>", "\t${1}", "</CustomWrapper>"], "description": "custom-wrapper 自定义组件包裹器 当数据更新层级较深时，可用此组件将需要更新的区域包裹起来，这样更新层级将大大减少"}, "MatchMedia": {"prefix": "MatchMedia", "body": ["<MatchMedia minWidth=\"${1}\" maxWidth=\"${2}\">", "\t${3}", "</MatchMedia>"], "description": "media query 匹配检测节点。可以指定一组 media query 规则，满足时，这个节点才会被展示。 通过这个节点可以实现“页面宽高在某个范围时才展示某个区域”这样的效果。"}, "MovableArea": {"prefix": "MovableArea", "body": ["<MovableArea className=\"${1}\">", "\t${2}", "</MovableArea>"], "description": ""}, "MovableView": {"prefix": "Mo<PERSON><PERSON><PERSON><PERSON>", "body": ["<MovableView className=\"${1}\" direction=\"${2}\">", "\t${3}", "</MovableView>"], "description": "可移动的视图容器，在页面中可以拖拽滑动。movable-view 必须在 movable-area 组件中，并且必须是直接子节点，否则不能移动。"}, "NativeSlot": {"prefix": "NativeSlot", "body": ["<NativeSlot>", "\t${1}", "</NativeSlot>"], "description": "译的原生组件支持使用 slot 插槽"}, "PageContainer": {"prefix": "<PERSON><PERSON><PERSON><PERSON>", "body": ["<PageContainer className=\"${1}\">", "\t${2}", "</PageContainer>"], "description": "页面容器。小程序如果在页面内进行复杂的界面设计（如在页面内弹出半屏的弹窗、在页面内加载一个全屏的子页面等），用户进行返回操作会直接离开当前页面，不符合用户预期，预期应为关闭当前弹出的组件。 为此提供“假页”容器组件，效果类似于 popup 弹出层，页面内存在该容器时，当用户进行返回操作，关闭该容器不关闭页面。返回操作包括三种情形，右滑手势、安卓物理返回键和调用 navigateBack 接口。"}, "RootPortal": {"prefix": "RootPortal", "body": ["<RootPortal className=\"${1}\">", "\t${2}", "</RootPortal>"], "description": "root-portal 使整个子树从页面中脱离出来，类似于在 CSS 中使用 fixed position 的效果。主要用于制作弹窗、弹出层等。"}, "ScrollView": {"prefix": "ScrollView", "body": ["<ScrollView className=\"${1}\" scrollY scrollWithAnimation onScroll={${2}} onScrollToLower={${3}}>", "\t${4}", "</ScrollView>"], "description": "可滚动视图区域。使用竖向滚动时，需要给scroll-view一个固定高度，通过 WXSS 设置 height。组件属性的长度单位默认为 px。Tips: H5 中 ScrollView 组件是通过一个高度（或宽度）固定的容器内部滚动来实现的，因此务必正确的设置容器的高度。例如: 如果 ScrollView 的高度将 body 撑开，就会同时存在两个滚动条（body 下的滚动条，以及 ScrollView 的滚动条）。 微信小程序 中 ScrollView 组件如果设置 scrollX 横向滚动时，并且子元素为多个时（单个子元素时设置固定宽度则可以正常横向滚动），需要通过 WXSS 设置 white-space: nowrap 来保证元素不换行，并对 ScrollView 内部元素设置 display: inline-block 来使其能够横向滚动。"}, "ShareElement": {"prefix": "ShareElement", "body": ["<ShareElement className=\"${1}\">", "\t${2}", "</ShareElement>"], "description": "共享元素。共享元素是一种动画形式，类似于 flutter Hero 动画，表现为元素像是在页面间穿越一样。该组件需与 PageContainer 组件结合使用。 使用时需在当前页放置 ShareElement 组件，同时在 PageContainer 容器中放置对应的 ShareElement 组件，对应关系通过属性值 key 映射。当设置 PageContainer 显示时，transform 属性为 true 的共享元素会产生动画。当前页面容器退出时，会产生返回动画。"}, "StickyHeader": {"prefix": "StickyHeader", "body": ["<StickyHeader className=\"${1}\">", "\t${2}", "</StickyHeader>"], "description": "吸顶布局容器，仅支持作为 scroll-view 自定义模式下的直接子节点或 sticky-section 组件直接子节点。仅 Skyline 支持。"}, "StickySection": {"prefix": "StickySection", "body": ["<StickySection className=\"${1}\">", "\t${2}", "</StickySection>"], "description": "吸顶布局容器，仅支持作为 scroll-view 自定义模式下的直接子节点。仅 Skyline 支持。"}, "Slot": {"prefix": "Slot", "body": ["<Slot name=\"${1}\">", "\t${2}", "</Slot>"], "description": "slot 插槽"}, "Swiper": {"prefix": "Swiper", "body": ["<Swiper className=\"${1}\" circular autoplay onChange={${2}}>", "\t<SwiperItem>", "\t\t${3}", "\t</SwiperItem>", "</Swiper>"], "description": "滑块视图容器。其中只可放置 swiper-item 组件，否则会导致未定义的行为。"}, "SwiperItem": {"prefix": "SwiperItem", "body": ["<SwiperItem className=\"${1}\">", "\t${2}", "</SwiperItem>"], "description": "仅可放置在 swiper 组件中，宽高自动设置为100%"}, "View": {"prefix": "View", "body": ["<View className=\"${1}\">${2}</View>"], "description": "基础的视图容器"}, "View-For": {"prefix": "View-For", "body": ["{${1:list}.map(item => {", "\treturn <View className=\"${2}\" key={item.${3}}>", "\t\t${4}", "\t</View>", "})}"], "description": "View的循环"}, "Icon": {"prefix": "Icon", "body": ["<Icon size=\"${1}\" color=\"${2}\" type=\"${3|success,success_no_circle,info,warn,waiting,cancel,download,search,clear,circle,info_circle|}\"></Icon>"], "description": "图标。组件属性的长度单位默认为 px"}, "Text": {"prefix": "Text", "body": ["<Text className=\"${1}\">${2}</Text>"], "description": "文本"}, "Progress": {"prefix": "Progress", "body": ["<Progress className=\"${1}\" percent={${2}} strokeWidth={${3}} active activeColor=\"${4}\"></Progress>"], "description": "进度条。组件属性的长度单位默认为 px"}, "RichText": {"prefix": "RichText", "body": ["<RichText className=\"${1}\" nodes={${2}}></RichText>"], "description": "富文本"}, "Button": {"prefix": "<PERSON><PERSON>", "body": ["<Button className=\"${1}\" size=\"${2|default,mini|}\" openType=\"${3|share,getPhoneNumber,contact,getRealtimePhoneNumber,getUserInfo,launchApp,openSetting,feedback,chooseAvatar,agreePrivacyAuthorization|}\">${4}</Button>"], "description": "按钮"}, "Checkbox": {"prefix": "Checkbox", "body": ["<Checkbox className=\"${1}\" value=\"${2}\" checked={${3}}>${4}</Checkbox>"], "description": "复选框"}, "CheckboxGroup": {"prefix": "CheckboxGroup", "body": ["<CheckboxGroup className=\"${1}\" name=\"${2}\" onChange={${3}}>", "\t${4}", "</CheckboxGroup>"], "description": "多项选择器，内部由多个checkbox组成"}, "Editor": {"prefix": "Editor", "body": ["<Editor id=\"${1}\" className=\"${2}\" placeholder={${3}} onReady={${4}}></Editor>"], "description": "富文本编辑器，可以对图片、文字进行编辑。"}, "Form": {"prefix": "Form", "body": ["<Form className=\"${1}\" onSubmit={${2}} onReset={${3}}>", "\t${4}", "</Form>"], "description": "表单。将组件内的用户输入的 switch input checkbox slider radio picker 提交。"}, "Input": {"prefix": "Input", "body": ["<Input className=\"${1}\" type=\"${2|text,number,idcard,digit,safe-password,nickname,numberpad,digitpad,idcardpad|}\" placeholder=\"${3}\" value=\"${4}\" onInput={${5}}></Input>"], "description": "输入框。该组件是原生组件，使用时请注意相关限制"}, "KeyboardAccessory": {"prefix": "KeyboardAccessory", "body": ["<KeyboardAccessory className=\"${1}\">", "\t${2}", "</KeyboardAccessory>"], "description": "设置 Input / Textarea 聚焦时键盘上方 CoverView / CoverImage 工具栏视图。需要配置 Taro 插件 @tarojs/plugin-platform-weapp 的 enablekeyboardAccessory 参数为 true 后才能使用"}, "Label": {"prefix": "Label", "body": ["<Label className=\"${1}\" for=\"${2}\">", "\t${3}", "</Label>"], "description": "用来改进表单组件的可用性。使用for属性找到对应的id，或者将控件放在该标签下，当点击时，就会触发对应的控件。 for优先级高于内部控件，内部有多个控件的时候默认触发第一个控件。 目前可以绑定的控件有：button, checkbox, radio, switch。"}, "Picker": {"prefix": "Picker", "body": ["<Picker className=\"${1}\" mode=\"${2|selector,multiSelector,time,date,region|}\" range={[${3}]} onChange={${4}}>", "\t${5}", "</Picker>"], "description": "从底部弹起的滚动选择器"}, "PickerView": {"prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ["<PickerView className=\"${1}\" value=\"${2}\" onChange={${3}}>", "\t${4}", "</PickerView>"], "description": "嵌入页面的滚动选择器 其中只可放置 picker-view-column 组件，其它节点不会显示"}, "PickerViewColumn": {"prefix": "PickerViewColumn", "body": ["<PickerViewColumn className=\"${1}\">", "\t${2}", "</PickerViewColumn>"], "description": "滚动选择器子项 仅可放置于 <PickerView /> 中，其孩子节点的高度会自动设置成与 picker-view 的选中框的高度一致"}, "Radio": {"prefix": "Radio", "body": ["<Radio className=\"${1}\" value=\"${2}\" checked=\"${3}\">${4}</Radio>"], "description": "单选项"}, "RadioGroup": {"prefix": "RadioGroup", "body": ["<RadioGroup className=\"${1}\">", "\t{[{value: '法国',text: '法国',checked: false}].map((item, index) => {", "\t\treturn (", "\t\t\t<Label className=\"${2}\" for={index} key={index}>", "\t\t\t\t<Radio className=\"${3}\" value={item.value} checked={item.checked}>{item.text}</Radio>", "\t\t\t</Label>", "\t\t)", "\t})}", "</RadioGroup>"], "description": "单项选择器，内部由多个 Radio 组成。"}, "Slider": {"prefix": "Slide<PERSON>", "body": ["<Slider className=\"${1}\" step={${2}} value={${3}} showValue min={${4}} max={${5}}></Slider>"], "description": "滑动选择器"}, "Switch": {"prefix": "Switch", "body": ["<Switch className=\"${1}\" checked={${2}} onChange={${3}}></Switch>"], "description": "开关选择器"}, "Textarea": {"prefix": "Textarea", "body": ["<Textarea className=\"${1}\" onInput={${2}}>", "\t${3}", "</Textarea>"], "description": "多行输入框。该组件是原生组件，使用时请注意相关限制"}, "FunctionalPageNavigator": {"prefix": "FunctionalPageNavigator", "body": ["<FunctionalPageNavigator className=\"${1}\">", "\t${2}", "</FunctionalPageNavigator>"], "description": "仅在插件中有效，用于跳转到插件功能页"}, "NavigationBar": {"prefix": "NavigationBar", "body": ["<NavigationBar className=\"${1}\" title=\"${2}\">", "\t${3}", "</NavigationBar>"], "description": "页面导航条配置节点，用于指定导航栏的一些属性。只能是 PageMeta 组件内的第一个节点，需要配合它一同使用。 通过这个节点可以获得类似于调用 Taro.setNavigationBarTitle Taro.setNavigationBarColor 等接口调用的效果。"}, "Navigator": {"prefix": "Navigator", "body": ["<Navigator className=\"${1}\" url=\"${2}\">${3}</Navigator>"], "description": "页面链接"}, "TabItem": {"prefix": "TabItem", "body": ["<TabItem className=\"${1}\" label=\"${2}\" name=\"${3}\">", "\t${4}", "</TabItem>"], "description": "标签栏子项"}, "Tabs": {"prefix": "Tabs", "body": ["<Tabs className=\"${1}\" activeName=\"${2}\">", "\t${3}", "</Tabs>"], "description": "标签栏"}, "AnimationVideo": {"prefix": "AnimationVideo", "body": ["<AnimationVideo className=\"${1}\"></AnimationVideo>"], "description": "透明视频动画"}, "AnimationView": {"prefix": "AnimationView", "body": ["<AnimationView className=\"${1}\">", "\t${2}", "</AnimationView>"], "description": "<PERSON><PERSON> 动画"}, "ArCamera": {"prefix": "ArCamera", "body": ["<ArCamera className=\"${1}\">", "\t${2}", "</ArCamera>"], "description": "AR 相机"}, "Audio": {"prefix": "Audio", "body": ["<Audio className=\"${1}\">", "\t${2}", "</Audio>"], "description": "音频。1.6.0版本开始，该组件不再维护。建议使用能力更强的 Taro.createInnerAudioContext 接口"}, "Camera": {"prefix": "Camera", "body": ["<Camera className=\"${1}\" mode=\"${2|normal,scanCode|}\"></Camera>"], "description": "系统相机"}, "ChannelLive": {"prefix": "ChannelLive", "body": ["<ChannelLive className=\"${1}\"></ChannelLive>"], "description": "小程序内嵌视频号直播组件，展示视频号直播状态和封面，并无弹窗跳转至视频号。注意：使用该组件打开的视频号视频需要与小程序的主体一致。"}, "ChannelVideo": {"prefix": "ChannelVideo", "body": ["<ChannelVideo className=\"${1}\"></ChannelVideo>"], "description": "小程序内嵌视频号视频组件，支持在小程序中播放视频号视频，并无弹窗跳转至视频号。注意：使用该组件打开的视频号视频需要与小程序相同主体或关联主体。"}, "Image": {"prefix": "Image", "body": ["<Image className=\"${1}\" src=\"${2}\" mode=\"${3|scaleToFill,aspectFit,aspectFill,widthFix,heightFix,top,bottom,center,left,right,top left,top right,bottom left,bottom right|}\"></Image>"], "description": "图片。支持 JPG、PNG、SVG、WEBP、GIF 等格式以及云文件ID。"}, "LivePlayer": {"prefix": "LivePlayer", "body": ["<LivePlayer className=\"${1}\" src='\"${2}\" mode=\"${3|live,RTC|}\" autoplay></LivePlayer>"], "description": "实时音视频播放。相关api：Taro.createLivePlayerContext。需要先通过类目审核，再在小程序管理后台，“设置”-“接口设置”中自助开通该组件权限。"}, "LivePusher": {"prefix": "LivePusher", "body": ["<LivePusher className=\"${1}\" src='\"${2}\" mode=\"${3|SD,HD,FHD,RTC|}\" autoplay></LivePusher>"], "description": "实时音视频录制。需要用户授权 scope.camera、scope.record 需要先通过类目审核，再在小程序管理后台，「开发」-「接口设置」中自助开通该组件权限。"}, "Lottie": {"prefix": "<PERSON><PERSON>", "body": ["<Lottie className=\"${1}\" path=\"${2}\"></Lottie>"], "description": "<PERSON><PERSON>"}, "RtcRoom": {"prefix": "RtcRoom", "body": ["<RtcRoom className=\"${1}\" id=\"${2}\"></RtcRoom>"], "description": "实时音视频通话房间"}, "RtcRoomItem": {"prefix": "RtcRoomItem", "body": ["<RtcRoomItem className=\"${1}\" id=\"${2}\"></RtcRoomItem>"], "description": "实时音视频通话画面"}, "Video": {"prefix": "Video", "body": ["<Video", "\tclassName=\"${1}\"", "\tid='${2:video}'", "\tsrc='${3:https://wxsnsdy.tc.qq.com/105/20210/snsdyvideodownload?filekey=30280201010421301f0201690402534804102ca905ce620b1241b726bc41dcff44e00204012882540400&bizid=1023&hy=SH&fileparam=302c020101042530230204136ffd93020457e3c4ff02024ef202031e8d7f02030f42400204045a320a0201000400}'", "\tposter='${4:https://misc.aotu.io/booxood/mobile-video/cover_900x500.jpg}'", "\tinitialTime={${5:0}}", "\tcontrols={${6:true}}", "\tautoplay={${7:false}}", "\tloop={${8:false}}", "\tmuted={${9:false}}", "/>"], "description": "视频。相关api：Taro.createVideoContext"}, "VoipRoom": {"prefix": "VoipRoom", "body": ["<VoipRoom className=\"${1}\" openId=\"${2}\"></VoipRoom>"], "description": "多人音视频对话"}, "Map": {"prefix": "Map", "body": ["<Map className=\"${1}\" onClick={${2}} longitude={${3}} latitude={${4}}></Map>"], "description": "地图。相关api Taro.createMapContext。"}, "Canvas": {"prefix": "<PERSON><PERSON>", "body": ["<Canvas id=\"${1}\" className=\"${2}\" type=\"${3|2d,webgl|}\" width=\"${4}\" height=\"${5}\"></Canvas>"], "description": "画布"}, "Ad": {"prefix": "Ad", "body": ["<Ad", "\tunitId=''", "\tadIntervals={60}", "\tonLoad={() => console.log('ad onLoad')}", "\tonError={() => console.log('ad onError')}", "\tonClose={() => console.log('ad onClose')}", "/>"], "description": "Banner 广告"}, "AdCustom": {"prefix": "AdCustom", "body": ["<AdCustom", "\tunitId=''", "\tadIntervals={60}", "\tonLoad={() => console.log('ad onLoad')}", "\tonError={() => console.log('ad onError')}", "\tonClose={() => console.log('ad onClose')}", "/>"], "description": "Banner 广告"}, "AwemeData": {"prefix": "AwemeData", "body": ["<AwemeData className=\"${1}\"></AwemeData>"], "description": "直播间状态组件"}, "CommentList": {"prefix": "CommentList", "body": ["<CommentList className=\"${1}\"></CommentList>"], "description": "评论列表"}, "CommentDetail": {"prefix": "CommentDetail", "body": ["<CommentDetail className=\"${1}\"></CommentDetail>"], "description": "评论详情"}, "ContactButton": {"prefix": "ContactButton", "body": ["<ContactButton className=\"${1}\"></ContactButton>"], "description": "智能客服"}, "FollowSwan": {"prefix": "FollowSwan", "body": ["<FollowSwan className=\"${1}\"></FollowSwan>"], "description": "关注小程序"}, "Lifestyle": {"prefix": "Lifestyle", "body": ["<Lifestyle className=\"${1}\"></Lifestyle>"], "description": "关注生活号"}, "Like": {"prefix": "Like", "body": ["<Like className=\"${1}\"></Like>"], "description": "点赞"}, "Login": {"prefix": "<PERSON><PERSON>", "body": ["<Login className=\"${1}\"></Login>"], "description": "联合登录 / 手机号授权内嵌组件"}, "OfficialAccount": {"prefix": "OfficialAccount", "body": ["<OfficialAccount className=\"${1}\"></OfficialAccount>"], "description": "公众号关注组件"}, "OpenData": {"prefix": "OpenData", "body": ["<OpenData className=\"${1}\" type='${2:userAvatarUrl}'></OpenData>"], "description": "用于展示平台开放的数据"}, "WebView": {"prefix": "WebView", "body": ["<WebView className=\"${1}\" src='${2:https://mp.weixin.qq.com/}' onMessage={${3:this.handleMessage}}></WebView>"], "description": "web-view 组件是一个可以用来承载网页的容器，会自动铺满整个小程序页面。个人类型与海外类型的小程序暂不支持使用。"}}