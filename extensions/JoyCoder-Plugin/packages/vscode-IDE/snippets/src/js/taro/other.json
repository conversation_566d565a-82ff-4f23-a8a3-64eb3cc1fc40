{"Taro3函数组件": {"prefix": ["taro", "rfc", "com"], "body": ["import { View } from '@tarojs/components'", "import { memo, useEffect, useState } from 'react'", "import './index.scss'", "", "function ${1:FnComp}() {", "  const [info, setInfo] = useState({ number: 0 })", "\t${2}", "", "  useEffect(() => {", "    setInfo({ number: 1 })", "  }, [])", "", "  return <View className='header'>{info.number}</View>", "}", "", "export default memo(${1:FnComp})", ""], "description": "Taro3函数组件"}}