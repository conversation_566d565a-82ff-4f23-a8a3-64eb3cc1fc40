{"Taro.ENV_TYPE": {"prefix": "Taro.ENV_TYPE", "body": ["if (Taro.ENV_TYPE == Taro.ENV_TYPE.${1|WEAPP,WEB,JD,SWAN,ALIPAY,TT,RN,QUICKAPP,QQ|}) {", "\t${2}", "}"], "description": "环境判断"}, "Taro.getEnv": {"prefix": "Taro.getEnv", "body": ["const ${1:ENV_TYPE} = Taro.getEnv();", "${2}"], "description": "获取当前环境值，具体值参考 Taro.ENV_TYPE"}, "Taro.Events": {"prefix": "Taro.Events", "body": ["const events = new Events()", "events.on('${1:eventName}', (arg) => {", "\t${2}", "})", "events.trigger('${1:eventName}', arg)", "events.off('${1:eventName}')", "${2}"], "description": "Taro 提供了 Taro.Events 来实现消息机制，使用时需要实例化它"}, "Taro.eventCenter": {"prefix": "Taro.eventCenter", "body": ["Taro.eventCenter.on('${1:eventName}', (arg) => {", "\t${2}", "})", "Taro.eventCenter.trigger('${1:eventName}', arg)", "Taro.eventCenter.off('${1:eventName}')", "${2}"], "description": "Taro 提供了一个全局消息中心 Taro.eventCenter 以供使用，它是 Taro.Events 的实例"}, "Taro.pxTransform": {"prefix": "Taro.pxTransform", "body": ["const rpxStr = Taro.pxTransform(${1:750});", "${2}"], "description": "尺寸转换，将px尺寸转为rpx"}, "Taro.initPxTransform": {"prefix": "Taro.initPxTransform", "body": ["Taro.initPxTransform({", "\tdesignWidth: 750,", "\tdeviceRatio: {", "\t\t640: 2.34 / 2,", "\t\t750: 1,", "\t\t828: 1.81 / 2,", "\t},", "})"], "description": "尺寸转换初始化"}, "Taro.getAppInfo": {"prefix": "Taro.getAppInfo", "body": ["const res = Taro.getAppInfo();", "${2}"], "description": "小程序获取和 Taro 相关的 App 信息"}, "Taro.getRenderer": {"prefix": "Taro.get<PERSON><PERSON>er", "body": ["const res = Taro.getRenderer();", "${2}"], "description": "获取当前页面渲染引擎类型"}, "Taro.requirePlugin": {"prefix": "Taro.requirePlugin", "body": ["const res = Taro.requirePlugin();", "${2}"], "description": "小程序引用插件 JS 接口"}, "Taro.getCurrentInstance": {"prefix": "Taro.getCurrentInstance", "body": ["const { app, router } = Taro.getCurrentInstance();"], "description": "获取当前页面实例"}, "Taro.setGlobalDataPlugin": {"prefix": "Taro.setGlobalDataPlugin", "body": ["const App = createApp(...)", "App.use(Taro.setGlobalDataPlugin, {", "\txxx: 999", "})", "Taro.getApp().xxx"], "description": "Vue3 插件，用于设置 getApp() 中的全局变量"}, "Taro.getTabBar": {"prefix": "Taro.getTabBar", "body": ["const { page } = Taro.getCurrentInstance();", "const res = Taro.getTabBar(page);"], "description": "获取自定义 TabBar 对应的 React 或 Vue 组件实例"}, "Taro.interceptorify": {"prefix": "Taro.interceptorify", "body": ["const fetchDataInterceptorify = Taro.interceptorify(Taro.request)"], "description": "包裹 promiseify api 的洋葱圈模型"}, "Taro.getApp": {"prefix": "Taro.getApp", "body": ["const ${1:app} = Taro.getApp();"], "description": "获取到小程序全局唯一的 App 实例。"}, "Taro.getCurrentPages": {"prefix": "Taro.getCurrentPages", "body": ["const ${1:curPages} = Taro.getCurrentPages();", "${2}"], "description": "获取当前页面栈。数组中第一个元素为首页，最后一个元素为当前页面"}, "Taro.env": {"prefix": "Taro.env", "body": ["const res = Taro.env;"], "description": ""}, "Taro.canIUse": {"prefix": "Taro.canIUse", "body": ["const res = Taro.canIUse('${1:button.open-type.contact}');"], "description": "判断小程序的 API，回调，参数，组件等是否在当前版本可用。"}, "Taro.canIUseWebp": {"prefix": "Taro.canIUseWebp", "body": ["const res = Taro.canIUseWebp();"], "description": "判断能否使用 WebP 格式"}, "Taro.base64ToArrayBuffer": {"prefix": "Taro.base64ToArrayBuffer", "body": ["const base64 = 'CxYh'", "const arrayBuffer = Taro.base64ToArrayBuffer(base64)"], "description": "将 Base64 字符串转成 ArrayBuffer 数据。"}, "Taro.arrayBufferToBase64": {"prefix": "Taro.arrayBufferToBase64", "body": ["const arrayBuffer = new Uint8Array([11, 22, 33])", "const base64 = Taro.arrayBufferToBase64(arrayBuffer)"], "description": "将 Base64 字符串转成 ArrayBuffer 数据。"}, "Taro.openSystemBluetoothSetting": {"prefix": "Taro.openSystemBluetoothSetting", "body": ["Taro.openSystemBluetoothSetting({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "跳转系统蓝牙设置页"}, "Taro.openAppAuthorizeSetting": {"prefix": "Taro.openAppAuthorizeSetting", "body": ["Taro.openAppAuthorizeSetting({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "跳转系统微信授权管理页"}, "Taro.getWindowInfo": {"prefix": "Taro.getWindowInfo", "body": ["const windowInfo = Taro.getWindowInfo()", "console.log(windowInfo.pixelRatio)", "console.log(windowInfo.screenWidth)", "console.log(windowInfo.screenHeight)", "console.log(windowInfo.windowWidth)", "console.log(windowInfo.windowHeight)", "console.log(windowInfo.statusBarHeight)", "console.log(windowInfo.safeArea)", "console.log(windowInfo.screenTop)"], "description": "获取窗口信息"}, "Taro.getSystemSetting": {"prefix": "Taro.getSystemSetting", "body": ["const systemSetting = Taro.getSystemSetting()", "console.log(systemSetting.bluetoothEnabled)", "console.log(systemSetting.deviceOrientation)", "console.log(systemSetting.locationEnabled)", "console.log(systemSetting.wifiEnabled)"], "description": "获取设备设置"}, "Taro.getSystemInfoSync": {"prefix": "Taro.getSystemInfoSync", "body": ["try {", "  const res = Taro.getSystemInfoSync()", "  console.log(res.model)", "  console.log(res.pixelRatio)", "  console.log(res.windowWidth)", "  console.log(res.windowHeight)", "  console.log(res.language)", "  console.log(res.version)", "  console.log(res.platform)", "} catch (e) {", "  // Do something when catch error", "}"], "description": "获取系统信息同步接口"}, "Taro.getSystemInfoAsync": {"prefix": "Taro.getSystemInfoAsync", "body": ["Taro.getSystemInfoAsync({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "异步获取系统信息"}, "Taro.getSystemInfo": {"prefix": "Taro.getSystemInfo", "body": ["Taro.getSystemInfo({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取系统信息，支持 Promise 化使用。"}, "Taro.getSkylineInfoSync": {"prefix": "Taro.getSkylineInfoSync", "body": ["const res = Taro.getSkylineInfoSync();"], "description": "获取当前运行环境对于 Skyline 渲染引擎 的支持情况 基础库 2.26.2 开始支持"}, "Taro.getSkylineInfo": {"prefix": "Taro.getSkylineInfo", "body": ["Taro.getSkylineInfo({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取当前运行环境对于 Skyline 渲染引擎 的支持情况 基础库 2.26.2 开始支持"}, "Taro.getRendererUserAgent": {"prefix": "Taro.getRendererUserAgent", "body": ["Taro.getRendererUserAgent({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取 Webview 小程序的 UserAgent 基础库 2.26.3 开始支持"}, "Taro.getDeviceInfo": {"prefix": "Taro.getDeviceInfo", "body": ["const res = Taro.getDeviceInfo();"], "description": "获取设备基础信息"}, "Taro.getAppBaseInfo": {"prefix": "Taro.getAppBaseInfo", "body": ["const res = Taro.getAppBaseInfo();"], "description": "获取微信APP基础信息"}, "Taro.getAppAuthorizeSetting": {"prefix": "Taro.getAppAuthorizeSetting", "body": ["const res = Taro.getAppAuthorizeSetting();"], "description": "获取微信APP授权设置"}, "Taro.updateWeChatApp": {"prefix": "Taro.updateWeChatApp", "body": ["Taro.updateWeChatApp({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "更新客户端版本。当判断用户小程序所在客户端版本过低时，可使用该接口跳转到更新微信页面。"}, "Taro.getUpdateManager": {"prefix": "Taro.getUpdateManager", "body": ["const updateManager = Taro.getUpdateManager()", "updateManager.onCheckForUpdate(function (res) {", "  // 请求完新版本信息的回调", "  console.log(res.hasUpdate)", "})", "updateManager.onUpdateReady(function () {", "  Taro.showModal({", "    title: '更新提示',", "    content: '新版本已经准备好，是否重启应用？',", "    success: function (res) {", "      if (res.confirm) {", "        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启", "        updateManager.applyUpdate()", "      }", "    }", "  })", "})", "updateManager.onUpdateFailed(function () {", "  // 新的版本下载失败", "})"], "description": "获取全局唯一的版本更新管理器，用于管理小程序更新。 关于小程序的更新机制，可以查看运行机制文档。"}, "updateManager.onCheckForUpdate": {"prefix": "updateManager.onCheckForUpdate", "body": ["updateManager.onCheckForUpdate((result) => {${1}});"], "description": "当向微信后台请求完新版本信息，会进行回调"}, "updateManager.onUpdateReady": {"prefix": "updateManager.onUpdateReady", "body": ["updateManager.onUpdateReady((result) => {${1}});"], "description": "当新版本下载完成，会进行回调"}, "updateManager.onUpdateFailed": {"prefix": "updateManager.onUpdateFailed", "body": ["updateManager.onUpdateFailed((result) => {${1}});"], "description": "当新版本下载失败，会进行回调"}, "updateManager.applyUpdate": {"prefix": "updateManager.applyUpdate", "body": ["updateManager.applyUpdate();"], "description": "当新版本下载完成，调用该方法会强制当前小程序应用上新版本并重启"}, "Taro.getLaunchOptionsSync": {"prefix": "Taro.getLaunchOptionsSync", "body": ["const res = Taro.getLaunchOptionsSync();"], "description": "获取小程序启动时的参数。与 App.onLaunch 的回调参数一致。"}, "Taro.getEnterOptionsSync": {"prefix": "Taro.getEnterOptionsSync", "body": ["const res = Taro.getEnterOptionsSync();"], "description": "获取本次小程序启动时的参数。如果当前是冷启动，则返回值与 App.onLaunch 的回调参数一致；如果当前是热启动，则返回值与 App.onShow 一致。"}, "Taro.onUnhandledRejection": {"prefix": "Taro.onUnhandledRejection", "body": ["Taro.onUnhandledRejection((res) => {", "\tconsole.log('Taro.onUnhandledRejection', res.reason, res.promise)", "})"], "description": ""}, "Taro.onThemeChange": {"prefix": "Taro.onThemeChange", "body": ["Taro.onThemeChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听系统主题改变事件。该事件与 App.onThemeChange 的回调时机一致。"}, "Taro.onPageNotFound": {"prefix": "Taro.onPageNotFound", "body": ["Taro.onPageNotFound((res) => {", "\tconsole.log(res)", "})"], "description": "监听小程序要打开的页面不存在事件。该事件与 App.onPageNotFound 的回调时机一致。"}, "Taro.onError": {"prefix": "Taro.onError", "body": ["Taro.onError((res) => {", "\tconsole.log(res)", "})"], "description": "监听小程序错误事件。如脚本错误或 API 调用报错等。该事件与 App.onError 的回调时机与参数一致。"}, "Taro.onAudioInterruptionEnd": {"prefix": "Taro.onAudioInterruptionEnd", "body": ["Taro.onAudioInterruptionEnd((res) => {", "\tconsole.log(res)", "})"], "description": "监听音频中断结束事件。在收到 onAudioInterruptionBegin 事件之后，小程序内所有音频会暂停，收到此事件之后才可再次播放成功"}, "Taro.onAudioInterruptionBegin": {"prefix": "Taro.onAudioInterruptionBegin", "body": ["Taro.onAudioInterruptionBegin((res) => {", "\tconsole.log(res)", "})"], "description": "监听音频因为受到系统占用而被中断开始事件。以下场景会触发此事件：闹钟、电话、FaceTime 通话、微信语音聊天、微信视频聊天。此事件触发后，小程序内所有音频会暂停。"}, "Taro.onAppShow": {"prefix": "Taro.onAppShow", "body": ["Taro.onAppShow((res) => {", "\tconsole.log(res)", "})"], "description": "监听小程序切前台事件。该事件与 App.onShow 的回调参数一致。"}, "Taro.onAppHide": {"prefix": "Taro.onAppHide", "body": ["Taro.onAppHide((res) => {", "\tconsole.log(res)", "})"], "description": "监听小程序切后台事件。该事件与 App.onHide 的回调时机一致。"}, "Taro.offUnhandledRejection": {"prefix": "Taro.offUnhandledRejection", "body": ["Taro.offUnhandledRejection((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听未处理的 Promise 拒绝事件"}, "Taro.offThemeChange": {"prefix": "Taro.offThemeChange", "body": ["Taro.offThemeChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听系统主题改变事件"}, "Taro.offPageNotFound": {"prefix": "Taro.offPageNotFound", "body": ["Taro.offPageNotFound((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听小程序要打开的页面不存在事件"}, "Taro.offError": {"prefix": "Taro.offError", "body": ["Taro.offError((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听音频播放错误事件"}, "Taro.offAudioInterruptionEnd": {"prefix": "Taro.offAudioInterruptionEnd", "body": ["Taro.offAudioInterruptionEnd((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听音频中断结束事件"}, "Taro.offAudioInterruptionBegin": {"prefix": "Taro.offAudioInterruptionBegin", "body": ["Taro.offAudioInterruptionBegin((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听音频因为受到系统占用而被中断开始事件"}, "Taro.offAppShow": {"prefix": "Taro.offAppShow", "body": ["Taro.offAppShow((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听小程序切前台事件"}, "Taro.offAppHide": {"prefix": "Taro.offAppHide", "body": ["Taro.offAppHide((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听小程序切后台事件"}, "Taro.setEnableDebug": {"prefix": "Taro.setEnableDebug", "body": ["Taro.setEnableDebug({", "\tenableDebug: true,", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "设置是否打开调试开关，此开关对正式版也能生效。"}, "Taro.getRealtimeLogManager": {"prefix": "Taro.getRealtimeLogManager", "body": ["const logger = Taro.getRealtimeLogManager();", "logger.info({str: 'hello world'}, 'info log', 100, [1, 2, 3])", "logger.error({str: 'hello world'}, 'error log', 100, [1, 2, 3])", "logger.warn({str: 'hello world'}, 'warn log', 100, [1, 2, 3])"], "description": "获取实时日志管理器对象。"}, "Taro.getLogManager": {"prefix": "Taro.getLogManager", "body": ["const logger = Taro.getLogManager();", "logger.log({str: 'hello world'}, 'info log', 100, [1, 2, 3])", "logger.info({str: 'hello world'}, 'info log', 100, [1, 2, 3])", "logger.debug({str: 'hello world'}, 'error log', 100, [1, 2, 3])", "logger.warn({str: 'hello world'}, 'warn log', 100, [1, 2, 3])"], "description": "获取日志管理器对象。"}, "logger.log": {"prefix": "logger.log", "body": ["logger.log(${1});"], "description": "写log日志，可以提供任意个参数。"}, "logger.info": {"prefix": "logger.info", "body": ["logger.info(${1});"], "description": "写info日志，可以提供任意个参数。"}, "logger.warn": {"prefix": "logger.warn", "body": ["logger.warn(${1});"], "description": "写warn日志，可以提供任意个参数。"}, "logger.debug": {"prefix": "logger.debug", "body": ["logger.debug(${1});"], "description": "写debug日志，可以提供任意个参数。"}, "Taro.reportPerformance": {"prefix": "Taro.reportPerformance", "body": ["Taro.reportPerformance(1101, 680);", "Taro.reportPerformance(1101, 680, 'custom');"], "description": "小程序测速上报。使用前，需要在小程序管理后台配置。 详情参见小程序测速指南。"}, "Taro.preloadWebview": {"prefix": "Taro.preloadWebview", "body": ["Taro.preloadWebview({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "预加载下个页面的 WebView"}, "Taro.preloadSkylineView": {"prefix": "Taro.preloadSkylineView", "body": ["Taro.preloadSkylineView({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "预加载下个页面所需要的 Skyline 运行环境"}, "Taro.preloadAssets": {"prefix": "Taro.preloadAssets", "body": ["Taro.preloadAssets({", "\tdata: {},", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "为视图层预加载媒体资源文件, 目前支持：font，image"}, "Taro.getPerformance": {"prefix": "Taro.getPerformance", "body": ["const performance = Taro.getPerformance()", "const observer = performance.createObserver((entryList) => {", "\tconsole.log(entryList.getEntries())", "})", "observer.observe({ entryTypes: ['render', 'script', 'navigation'] })"], "description": "小程序测速上报。使用前，需要在小程序管理后台配置。 详情参见小程序测速指南。"}, "Taro.getUserCryptoManager": {"prefix": "Taro.getUserCryptoManager", "body": ["const userCryptoManager = Taro.getUserCryptoManager();", "userCryptoManager.getLatestUserKey({", "\tsuccess: res => {", "\t\tconst { encryptKey, iv, version, expireTime } = res", "\t\tconsole.log(encryptKey, iv, version, expireTime)", "\t}", "})"], "description": "获取用户加密模块"}, "Taro.getRandomValues": {"prefix": "Taro.getRandomValues", "body": ["Taro.getRandomValues({", "\tlength: 6 // 生成 6 个字节长度的随机数", "}).then(res => {", "\tconsole.log(Taro.arrayBufferToBase64(res.randomValues)) // 转换为 base64 字符串后打印", "})"], "description": "获取密码学安全随机数"}, "Taro.preload": {"prefix": "Taro.preload", "body": ["Taro.preload({ key: 'value' })"], "description": "跳转预加载 API"}, "Taro.switchTab": {"prefix": "Taro.switchTab", "body": ["Taro.switchTab({", "\turl: '${1}',", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "跳转到tabBar页面，并关闭其他所有非tabBar页面"}, "Taro.reLaunch": {"prefix": "Taro.reLaunch", "body": ["Taro.reLaunch({", "\turl: '${1}',", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭所有页面，打开到应用内的某个页面。"}, "Taro.redirectTo": {"prefix": "Taro.redirectTo", "body": ["Taro.redirectTo({", "\turl: '${1}',", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭当前页面，跳转到应用内的某个页面。"}, "Taro.navigateTo": {"prefix": "Taro.navigateTo", "body": ["Taro.navigateTo({", "\turl: '${1}',", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "保留当前页面，跳转到应用内的某个页面，使用Taro.navigateBack可以返回到原页面。目前页面路径最多只能十层。"}, "Taro.navigateBack": {"prefix": "Taro.navigateBack", "body": ["Taro.navigateBack({", "\tdelta: ${1:1}", "});"], "description": "关闭当前页面，返回上一页面或多级页面。可通过 getCurrentPages()) 获取当前的页面栈，决定需要返回几层。"}, "Taro.openBusinessView": {"prefix": "Taro.openBusinessView", "body": ["Taro.openBusinessView({", "\tbusinessType: 'wxpayScoreDetail',", "\textraData: {", "\t\tmch_id: '1230000109',", "\t\tservice_id: '88888888000011',", "\t\tout_order_no: '1234323JKHDFE1243252',", "\t\ttimestamp: '1530097563',", "\t\tnonce_str: 'zyx53Nkey8o4bHpxTQvd8m7e92nG5mG2',", "\t\tsign_type: 'HMAC-SHA256',", "\t\tsign: '029B52F67573D7E3BE74904BF9AEA'", "\t},", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "商户通过调用订单详情接口打开微信支付分小程序，引导用户查看订单详情（小程序端）"}, "Taro.openEmbeddedMiniProgram": {"prefix": "Taro.openEmbeddedMiniProgram", "body": ["Taro.openEmbeddedMiniProgram({", "\tappId: '',", "\tpath: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "打开半屏小程序。接入指引请参考 半屏小程序能力。"}, "Taro.navigateToMiniProgram": {"prefix": "Taro.navigateToMiniProgram", "body": ["Taro.navigateToMiniProgram({", "\tappId:'${1}',", "\tpath:'${2}',", "\textraData:{${3}},", "\tenvVersion:'${4:release}',", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "打开同一公众号下关联的另一个小程序"}, "Taro.navigateBackMiniProgram": {"prefix": "Taro.navigateBackMiniProgram", "body": ["Taro.navigateBackMiniProgram({", "\textraData:{${1}},", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "返回到上一个小程序，只有在当前小程序是被其他小程序打开时可以调用成功"}, "Taro.exitMiniProgram": {"prefix": "Taro.exitMiniProgram", "body": ["Taro.exitMiniProgram({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "退出当前小程序。必须有点击行为才能调用成功。"}, "Taro.updateShareMenu": {"prefix": "Taro.updateShareMenu", "body": ["Taro.updateShareMenu({", "\twithShareTicket: ${1:false}", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "更新转发属性"}, "Taro.showShareMenu": {"prefix": "Taro.showShareMenu", "body": ["Taro.showShareMenu({", "\twithShareTicket: ${1:false}", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示当前页面的转发按钮"}, "Taro.showShareImageMenu": {"prefix": "Taro.showShareImageMenu", "body": ["Taro.downloadFile({", "\turl: 'https://res.wx.qq.com/wxdoc/dist/assets/img/demo.ef5c5bef.jpg',", "\tsuccess: (res) => {", "\t\tTaro.showShareImageMenu({", "\t\t\tpath: res.tempFile<PERSON>ath", "\t\t})", "\t}", "})"], "description": "打开分享图片弹窗，可以将图片发送给朋友、收藏或下载"}, "Taro.shareVideoMessage": {"prefix": "Taro.shareVideoMessage", "body": ["Taro.downloadFile({", "\turl: '', // 下载url", "\tsuccess: (res) => {", "\t\tTaro.shareVideoMessage({", "\t\t\tvideoPath: res.tempFilePath", "\t\t})", "\t}", "})"], "description": "转发视频到聊天"}, "Taro.shareFileMessage": {"prefix": "Taro.shareFileMessage", "body": ["Taro.shareFileMessage({", "\tfilePath: '',", "\tfileName: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "转发文件到聊天"}, "Taro.onCopyUrl": {"prefix": "Taro.onCopyUrl", "body": ["Taro.onCopyUrl((res) => {", "\tconsole.log(res)", "})"], "description": "监听用户点击右上角菜单的「复制链接」按钮时触发的事件"}, "Taro.offCopyUrl": {"prefix": "Taro.offCopyUrl", "body": ["Taro.offCopyUrl((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听用户点击右上角菜单的「复制链接」按钮时触发的事件"}, "Taro.hideShareMenu": {"prefix": "Taro.hideShareMenu", "body": ["Taro.hideShareMenu({", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "隐藏当前页面的转发按钮"}, "Taro.getShareInfo": {"prefix": "Taro.getShareInfo", "body": ["Taro.getShareInfo({", "\tshareTicket: ${1},", "\ttimeout:${2:10000},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取转发详细信息"}, "Taro.authPrivateMessage": {"prefix": "Taro.authPrivateMessage", "body": ["Taro.authPrivateMessage({", "\tshareTicket: 'xxxxxx',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "验证私密消息"}, "Taro.showToast": {"prefix": "Taro.showToast", "body": ["Taro.showToast({", "\ttitle: '${1}',", "\ticon: '${2|success,error,loading,none|}',", "\timage: '${3}',", "\tduration: ${4:1500},", "\tmask: ${5:false},", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示消息提示框"}, "Taro.showModal": {"prefix": "Taro.showModal", "body": ["Taro.showModal({", "\ttitle: '${1}',", "\tcontent: '${2}',", "\tshowCancel: ${3:true},", "\tcancelText: '${4:取消}',", "\tcancelColor: '${5:#000000}',", "\tconfirmText: '${6:确定}',", "\tconfirmColor: '${7:#3CC51F}',", "\tsuccess: (result) => {", "\t\t${8:if(result.confirm){", "\t\t\t${9}", "\t\t}}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示模态对话框"}, "Taro.showLoading": {"prefix": "Taro.showLoading", "body": ["Taro.showLoading({", "\ttitle: ${1},", "\tmask: ${2:true},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示loading提示框, 需主动调用Taro.hideLoading才能关闭提示框"}, "Taro.showActionSheet": {"prefix": "Taro.showActionSheet", "body": ["Taro.showActionSheet({", "\titemList: [${1}],", "\titemColor: '${2:#000000}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示操作菜单"}, "Taro.hideToast": {"prefix": "Taro.hideToast", "body": ["Taro.hideToast();"], "description": "隐藏消息提示框"}, "Taro.hideLoading": {"prefix": "Taro.hideLoading", "body": ["Taro.hideLoading();"], "description": "隐藏loading提示框"}, "Taro.enableAlertBeforeUnload": {"prefix": "Taro.enableAlertBeforeUnload", "body": ["Taro.enableAlertBeforeUnload({", "\tmessage: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "开启小程序页面返回询问对话框。"}, "Taro.disableAlertBeforeUnload": {"prefix": "Taro.disableAlertBeforeUnload", "body": ["Taro.disableAlertBeforeUnload({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "关闭小程序页面返回询问对话框"}, "Taro.showNavigationBarLoading": {"prefix": "Taro.showNavigationBarLoading", "body": ["Taro.showNavigationBarLoading();"], "description": "在当前页面显示导航条加载动画。"}, "Taro.setNavigationBarTitle": {"prefix": "Taro.setNavigationBarTitle", "body": ["Taro.setNavigationBarTitle({", "\ttitle: '${1}'", "});"], "description": "动态设置当前页面的标题。"}, "Taro.setNavigationBarColor": {"prefix": "Taro.setNavigationBarColor", "body": ["Taro.setNavigationBarColor({", "\tfrontColor: '${1:#ffffff}',", "\tbackgroundColor: '${2:#000000}',", "\tanimation: {", "\t\tduration: ${3:0},", "\t\ttimingFunc: '${4:linear}'", "\t},", "\tsuccess: (result)=>{", "\t\t${5}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "设置导航栏颜色"}, "Taro.hideNavigationBarLoading": {"prefix": "Taro.hideNavigationBarLoading", "body": ["Taro.hideNavigationBarLoading();"], "description": "隐藏导航条加载动画。"}, "Taro.hideHomeButton": {"prefix": "Taro.hideHomeButton", "body": ["Taro.hideHomeButton();"], "description": "隐藏返回首页按钮。微信7.0.7版本起，当用户打开的小程序最底层页面是非首页时，默认展示“返回首页”按钮，开发者可在页面 onShow 中调用 hideHomeButton 进行隐藏。"}, "Taro.setBackgroundTextStyle": {"prefix": "Taro.setBackgroundTextStyle", "body": ["Taro.setBackgroundTextStyle({", "\ttextStyle: '${1:dark}'", "});"], "description": "动态设置下拉背景字体、loading图的样式"}, "Taro.setBackgroundColor": {"prefix": "Taro.setBackgroundColor", "body": ["Taro.setBackgroundColor({", "\tbackgroundColor: '${1}',", "\tbackgroundColorTop: '${2}',", "\tbackgroundColorBottom: '${3}'", "});"], "description": "动态设置窗口的背景色"}, "Taro.showTabBarRedDot": {"prefix": "Taro.showTabBarRedDot", "body": ["Taro.showTabBarRedDot({", "\tindex: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示tabBar某一项的右上角的红点"}, "Taro.showTabBar": {"prefix": "Taro.showTabBar", "body": ["Taro.showTabBar({", "\tanimation: ${1:false},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "显示 tabBar"}, "Taro.setTabBarStyle": {"prefix": "Taro.setTabBarStyle", "body": ["Taro.setTabBarStyle({", "\tcolor: '${1:#000000}',", "\tselectedColor: '${2:#1AAD16}',", "\tbackgroundColor: '${3:#e3e3e3}',", "\tborderStyle: '${4:black}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "动态设置tabBar的整体样式"}, "Taro.setTabBarItem": {"prefix": "Taro.setTabBarItem", "body": ["Taro.setTabBarItem({", "\tindex: ${1},", "\ttext: ${2},", "\ticonPath: ${3},", "\tselectedIconPath: ${4},", "\tsuccess: (result)=>{", "\t\t${5}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "动态设置tabBar某一项的内容"}, "Taro.setTabBarBadge": {"prefix": "Taro.setTabBarBadge", "body": ["Taro.setTabBarBadge({", "\tindex: ${1},", "\ttext: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "为tabBar某一项的右上角添加文本"}, "Taro.removeTabBarBadge": {"prefix": "Taro.removeTabBarBadge", "body": ["Taro.removeTabBarBadge({", "\tindex: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "移除tabBar某一项右上角的文本"}, "Taro.hideTabBarRedDot": {"prefix": "Taro.hideTabBarRedDot", "body": ["Taro.hideTabBarRedDot({", "\tindex: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "隐藏tabBar某一项的右上角的红点"}, "Taro.hideTabBar": {"prefix": "Taro.hideTabBar", "body": ["Taro.hideTabBar({", "\tanimation: ${1:false},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "隐藏 tabBar"}, "Taro.loadFontFace": {"prefix": "Taro.loadFontFace", "body": ["Taro.loadFontFace({", "\tfamily: '${1}',", "\tsource: '${2}',", "\tdesc: {", "\t\tstyle: '${3:normal}',", "\t\tweight: '${4:normal}',", "\t\tletiant: '${5:normal}'", "\t},", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "动态加载网络字体"}, "Taro.stopPullDownRefresh": {"prefix": "Taro.stopPullDownRefresh", "body": ["Taro.stopPullDownRefresh()"], "description": "停止当前页面下拉刷新。"}, "Taro.startPullDownRefresh": {"prefix": "Taro.startPullDownRefresh", "body": ["Taro.startPullDownRefresh()"], "description": "开始下拉刷新，调用后触发下拉刷新动画，效果与用户手动下拉刷新一致"}, "Taro.pageScrollTo": {"prefix": "Taro.pageScrollTo", "body": ["Taro.pageScrollTo({", "\tscrollTop: ${1:0},", "\tduration: ${2:300},", "\tselector: '${3:.header}'", "});"], "description": "将页面滚动到目标位置。"}, "Taro.createAnimation": {"prefix": "Taro.createAnimation", "body": ["${1:const animation = }Taro.createAnimation({", "\tduration: ${2:400},", "\ttimingFunction: '${3:linear}',", "\tdelay: ${4:0},", "\ttransformOrigin: '${5:50% 50% 0}'", "});"], "description": "创建一个动画实例 animation。调用实例的方法来描述动画。最后通过动画实例的 export 方法导出动画数据传递给组件的 animation 属性。"}, "animation.opacity": {"prefix": "animation.opacity", "body": ["animation.opacity(${1});"], "description": "透明度，参数范围 0~1"}, "animation.backgroundColor": {"prefix": "animation.backgroundColor", "body": ["animation.backgroundColor(${1});"], "description": "颜色值"}, "animation.width": {"prefix": "animation.width", "body": ["animation.width(${1});"], "description": "宽度"}, "animation.height": {"prefix": "animation.height", "body": ["animation.height(${1});"], "description": "长度"}, "animation.top": {"prefix": "animation.top", "body": ["animation.top(${1});"], "description": "顶部距离"}, "animation.left": {"prefix": "animation.left", "body": ["animation.height(${1});"], "description": "左侧距离"}, "animation.bottom": {"prefix": "animation.bottom", "body": ["animation.bottom(${1});"], "description": "底部距离"}, "animation.right": {"prefix": "animation.right", "body": ["animation.right(${1});"], "description": "右侧距离"}, "animation.rotate": {"prefix": "animation.rotate", "body": ["animation.rotate(${1});"], "description": "绕原点旋转"}, "animation.rotateX": {"prefix": "animation.rotateX", "body": ["animation.rotateX(${1});"], "description": "绕x轴旋转"}, "animation.rotateY": {"prefix": "animation.rotateY", "body": ["animation.rotateY(${1});"], "description": "绕y轴旋转"}, "animation.rotateZ": {"prefix": "animation.rotateZ", "body": ["animation.rotateZ(${1});"], "description": "绕z轴旋转"}, "animation.rotate3d": {"prefix": "animation.rotate3d", "body": ["animation.rotate3d(${1},${2},${3},${4});"], "description": "3d旋转"}, "animation.scale": {"prefix": "animation.scale", "body": ["animation.scale(${1}${2:,});"], "description": "x轴[y轴]缩放。一个参数时，表示在X轴、Y轴两个相同；两个参数时表示在X轴，在Y轴不同"}, "animation.scaleX": {"prefix": "animation.scaleX", "body": ["animation.scaleX(${1});"], "description": "x轴缩放"}, "animation.scaleY": {"prefix": "animation.scaleY", "body": ["animation.scaleY(${1});"], "description": "y轴缩放"}, "animation.scaleZ": {"prefix": "animation.scaleZ", "body": ["animation.scaleZ(${1});"], "description": "z轴缩放"}, "animation.scale3d": {"prefix": "animation.scale3d", "body": ["animation.scale3d(${1},${2},${3});"], "description": "3d缩放"}, "animation.translate": {"prefix": "animation.translate", "body": ["animation.translate(${1}${2:,});"], "description": "x轴[y轴]偏移。一个参数时，表示在X轴；两个参数时表示在X轴，在Y轴"}, "animation.translateX": {"prefix": "animation.translateX", "body": ["animation.translateX(${1});"], "description": "x轴偏移"}, "animation.translateY": {"prefix": "animation.translateY", "body": ["animation.translateY(${1});"], "description": "y轴偏移"}, "animation.translateZ": {"prefix": "animation.translateZ", "body": ["animation.translateZ(${1});"], "description": "z轴偏移"}, "animation.translate3d": {"prefix": "animation.translate3d", "body": ["animation.translate3d(${1},${2},${3});"], "description": "3d偏移"}, "animation.skew": {"prefix": "animation.skew", "body": ["animation.skew(${1}${2:,});"], "description": "x轴[y轴]倾斜。一个参数时，表示在X轴；两个参数时表示在X轴，在Y轴"}, "animation.skewX": {"prefix": "animation.skewX", "body": ["animation.skewX(${1});"], "description": "x轴倾斜"}, "animation.skewY": {"prefix": "animation.skewY", "body": ["animation.skewY(${1});"], "description": "y轴倾斜"}, "animation.matrix": {"prefix": "animation.matrix", "body": ["animation.matrix(${1},${2},${3},${4},${5},${6});"], "description": "平面矩阵变换"}, "animation.matrix3d": {"prefix": "animation.matrix3d", "body": ["animation.matrix3d(${1},${2},${3},${4},${5},${6},${7},${8},${9},${10},${11},${12},${13},${14},${15},${16});"], "description": "立体矩阵变换"}, "Taro.setTopBarText": {"prefix": "Taro.setTopBarText", "body": ["Taro.setTopBarText({", "\ttext: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "动态设置置顶栏文字内容，只有当前小程序被置顶时能生效，如果当前小程序没有被置顶，也能调用成功，但是不会立即生效，只有在用户将这个小程序置顶后才换上设置的文字内容。"}, "Taro.nextTick": {"prefix": "Taro.nextTick", "body": ["Taro.nextTick(() => {", "\t${1}", "})"], "description": "延迟一部分操作到下一个时间片再执行。（类似于 setTimeout）。 因为自定义组件中的 setData 和 triggerEvent 等接口本身是同步的操作，当这几个接口被连续调用时，都是在一个同步流程中执行完的，因此若逻辑不当可能会导致出错。 一个极端的案例：当父组件的 setData 引发了子组件的 triggerEvent，进而使得父组件又进行了一次 setData，期间有通过 wx:if 语句对子组件进行卸载，就有可能引发奇怪的错误，所以对于不需要在一个同步流程内完成的逻辑，可以使用此接口延迟到下一个时间片再执行。"}, "Taro.getMenuButtonBoundingClientRect": {"prefix": "Taro.getMenuButtonBoundingClientRect", "body": ["const ${1:menuButton} = Taro.getMenuButtonBoundingClientRect();"], "description": "获取菜单按钮的布局置信息"}, "Taro.setWindowSize": {"prefix": "Taro.setWindowSize", "body": ["Taro.setWindowSize({", "\twidth: '100px',", "\theight: '100px',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "设置窗口大小，该接口仅适用于 PC 平台"}, "Taro.onWindowResize": {"prefix": "Taro.onWindowResize", "body": ["Taro.onWindowResize((result)=>{", "\t\t${1}", "\t});"], "description": "监听窗口尺寸变化事件"}, "Taro.offWindowResize": {"prefix": "Taro.offWindowResize", "body": ["Taro.offWindowResize((result)=>{", "\t\t${1}", "\t});"], "description": "取消监听窗口尺寸变化事件"}, "Taro.checkIsPictureInPictureActive": {"prefix": "Taro.checkIsPictureInPictureActive", "body": ["const res = Taro.checkIsPictureInPictureActive();"], "description": "返回当前是否存在小窗播放（小窗在 video/live-player/live-pusher 下可用）"}, "Taro.request": {"prefix": "Taro.request", "body": ["${1:const request = }Taro.request({", "\turl: '${2}',", "\tdata: {${3}},", "\theader: {${4:'content-type':'application/json'}},", "\tmethod: '${5:GET}',", "\tdataType: '${6:json}',", "\tresponseType: '${7:text}',", "\tsuccess: (result)=>{", "\t\t${8}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "发起网络请求"}, "Taro.addInterceptor": {"prefix": "Taro.addInterceptor", "body": ["const interceptor = function (chain) {", "\tconst requestParams = chain.requestParams", "\tconst { method, data, url } = requestParams", "\treturn chain.proceed(requestParams)", "\t\t.then(res => {", "\t\t\tconsole.log(res)", "\t\t\treturn res", "\t\t})", "\t}", "Taro.addInterceptor(interceptor)", "Taro.request({ url })"], "description": "可以使用拦截器在请求发出前或发出后做一些额外操作。在调用 Taro.request 发起请求之前，调用 Taro.addInterceptor 方法为请求添加拦截器，拦截器的调用顺序遵循洋葱模型。 拦截器是一个函数，接受 chain 对象作为参数。chain 对象中含有 requestParmas 属性，代表请求参数。拦截器内最后需要调用 chain.proceed(requestParams) 以调用下一个拦截器或发起请求。Taro 提供了两个内置拦截器 logInterceptor 与 timeoutInterceptor，分别用于打印请求的相关信息和在请求超时时抛出错误。"}, "Taro.cleanInterceptors": {"prefix": "Taro.cleanInterceptors", "body": ["Taro.cleanInterceptors();"], "description": "清除所有拦截器"}, "Taro.downloadFile": {"prefix": "Taro.downloadFile", "body": ["${1:const downloadTask = }Taro.downloadFile({", "\turl: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "下载文件资源到本地。客户端直接发起一个 HTTPS GET 请求，返回文件的本地临时路径，单次下载允许的最大文件为 50MB。使用前请注意阅读相关说明。注意：请在服务端响应的 header 中指定合理的 Content-Type 字段，以保证客户端正确处理文件类型。"}, "downloadTask.onProgressUpdate": {"prefix": "downloadTask.onProgressUpdate", "body": ["downloadTask.onProgressUpdate((result)=>{", "\t\t${1}", "\t});"], "description": "监听下载进度变化事件。"}, "downloadTask.abort": {"prefix": "downloadTask.abort", "body": ["downloadTask.abort();"], "description": "中断下载任务。"}, "Taro.uploadFile": {"prefix": "Taro.uploadFile", "body": ["${1:const uploadTask = }Taro.uploadFile({", "\turl: '${2}',", "\tfilePath: ${3},", "\tname: ${4},", "\tformData: {${5}},", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "将本地资源上传到开发者服务器，客户端发起一个 HTTPS POST 请求，其中 content-type 为 multipart/form-data。"}, "uploadTask.onProgressUpdate": {"prefix": "uploadTask.onProgressUpdate", "body": ["uploadTask.onProgressUpdate((result)=>{", "\t\t${1}", "\t});"], "description": "监听上传进度变化事件。"}, "uploadTask.abort": {"prefix": "uploadTask.abort", "body": ["uploadTask.abort();"], "description": "中断上传任务。"}, "Taro.sendSocketMessage": {"prefix": "Taro.sendSocketMessage", "body": ["Taro.sendSocketMessage({", "\tdata: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "通过WebSocket连接发送数据，需要先Taro.connectSocket，并在Taro.onSocketOpen回调之后才能发送。"}, "Taro.onSocketOpen": {"prefix": "Taro.onSocketOpen", "body": ["Taro.onSocketOpen(${1:result} => {", "\t${2}", "});"], "description": "监听WebSocket连接打开事件。"}, "Taro.onSocketMessage": {"prefix": "Taro.onSocketMessage", "body": ["Taro.onSocketMessage((${1:result})=>{${2}});"], "description": "监听WebSocket接受到服务器的消息事件。"}, "Taro.onSocketError": {"prefix": "Taro.onSocketError", "body": ["Taro.onSocketError(${1:result} => {", "\t${2}", "});"], "description": "监听WebSocket错误。"}, "Taro.onSocketClose": {"prefix": "Taro.onSocketClose", "body": ["Taro.onSocketClose((${1:result})=>{", "\t${2}", "});"], "description": "监听WebSocket关闭。"}, "Taro.connectSocket": {"prefix": "Taro.connectSocket", "body": ["${1:const sockTask = }Taro.connectSocket({", "\turl: '${2}',", "\theader: {${3:'content-type':'application/json'}},", "\tmethod: '${4:GET}',", "\tprotocols: [${5}],", "\tsuccess: ()=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "创建一个WebSocket连接。"}, "Taro.closeSocket": {"prefix": "Taro.closeSocket", "body": ["Taro.closeSocket({", "\tcode: ${1:1000},", "\treason: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭WebSocket连接。"}, "socketTask.send": {"prefix": "socketTask.send", "body": ["socketTask.send({", "\tdata: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "通过WebSocket连接发送数据。"}, "socketTask.close": {"prefix": "socketTask.close", "body": ["socketTask.close({", "\tcode: ${1},", "\treason: ${2},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭WebSocket连接。"}, "socketTask.onOpen": {"prefix": "socketTask.onOpen", "body": ["socketTask.onOpen(${1:result} => {", "\t${2}", "});"], "description": "监听WebSocket连接打开事件。"}, "socketTask.onClose": {"prefix": "socketTask.onClose", "body": ["socketTask.onClose(${1:result} => {", "\t${2}", "});"], "description": "监听 WebSocket 连接关闭事件。"}, "socketTask.onError": {"prefix": "socketTask.onError", "body": ["socketTask.onError(${1:result} => {", "\t${2}", "});"], "description": "监听 WebSocket 错误。"}, "socketTask.onMessage": {"prefix": "socketTask.onMessage", "body": ["socketTask.onMessage(${1:result} => {", "\t${2}", "});"], "description": "监听WebSocket接受到服务器的消息事件。"}, "Taro.requestPayment": {"prefix": "Taro.requestPayment", "body": ["Taro.requestPayment({", "\ttimeStamp: '${1}',", "\tnonceStr: '${2}',", "\tpackage: '${3}',", "\tsignType: '${4}',", "\tpaySign: '${5}',", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "发起微信支付。"}, "Taro.faceVerifyForPay": {"prefix": "Taro.faceVerifyForPay", "body": ["Taro.faceVerifyForPay({", "\tscene: '${1}',", "\tpackage: '${2}',", "\tpackageSign: '${3}',", "\totherVerifyTitle: '${4}',", "\tsuccess: (result)=>{", "\t\t${5}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "支付各个安全场景验证人脸。"}, "Taro.setStorageSync": {"prefix": "Taro.setStorageSync", "body": ["Taro.setStorageSync(${1:key}, ${2:data});"], "description": "将 data 存储在本地缓存中指定的 key 中，会覆盖掉原来该 key 对应的内容，这是一个同步接口。"}, "Taro.setStorage": {"prefix": "Taro.setStorage", "body": ["Taro.setStorage({", "\tkey: '${1:key}',", "\tdata: ${2:data},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "将数据存储在本地缓存中指定的 key 中，会覆盖掉原来该 key 对应的内容，这是一个异步接口。"}, "Taro.revokeBufferURL": {"prefix": "Taro.revokeBufferURL", "body": ["Taro.revokeBufferURL();"], "description": "根据 URL 销毁存在内存中的数据"}, "Taro.removeStorageSync": {"prefix": "Taro.removeStorageSync", "body": ["Taro.removeStorageSync(${1:key});"], "description": "从本地缓存中同步移除指定 key。"}, "Taro.removeStorage": {"prefix": "Taro.removeStorage", "body": ["Taro.removeStorage({", "\tkey: '${1:key},'", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "从本地缓存中异步移除指定 key。"}, "Taro.getStorageSync": {"prefix": "Taro.getStorageSync", "body": ["Taro.getStorageSync(${1:key});"], "description": "从本地缓存中同步获取指定 key 对应的内容。"}, "Taro.getStorageInfoSync": {"prefix": "Taro.getStorageInfoSync", "body": ["${1:const storageInfo = }Taro.getStorageInfoSync();"], "description": "同步获取当前storage的相关信息"}, "Taro.getStorageInfo": {"prefix": "Taro.getStorageInfo", "body": ["Taro.getStorageInfo({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "异步获取当前storage的相关信息"}, "Taro.getStorage": {"prefix": "Taro.getStorage", "body": ["Taro.getStorage({", "\tkey: '${1:key}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "从本地缓存中异步获取指定 key 对应的内容。"}, "Taro.createBufferURL": {"prefix": "Taro.createBufferURL", "body": ["Taro.createBufferURL(${1});"], "description": "根据传入的 buffer 创建一个唯一的 URL 存在内存中"}, "Taro.clearStorageSync": {"prefix": "Taro.clearStorageSync", "body": ["Taro.clearStorageSync();"], "description": "同步清除本地数据缓存"}, "Taro.clearStorage": {"prefix": "Taro.clearStorage", "body": ["Taro.clearStorage();"], "description": "异步清除本地数据缓存"}, "Taro.setBackgroundFetchToken": {"prefix": "Taro.setBackgroundFetchToken", "body": ["Taro.setBackgroundFetchToken({", "\ttoken: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "设置自定义登录态，在周期性拉取数据时带上，便于第三方服务器验证请求合法性"}, "Taro.onBackgroundFetchData": {"prefix": "Taro.onBackgroundFetchData", "body": ["Taro.onBackgroundFetchData({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "收到 backgroundFetch 数据时的回调"}, "Taro.getBackgroundFetchToken": {"prefix": "Taro.getBackgroundFetchToken", "body": ["Taro.getBackgroundFetchToken({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取设置过的自定义登录态。若无，则返回 fail。"}, "Taro.getBackgroundFetchData": {"prefix": "Taro.getBackgroundFetchData", "body": ["Taro.getBackgroundFetchData({", "\tfetchType: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "拉取 backgroundFetch 客户端缓存数据"}, "Taro.reportMonitor": {"prefix": "Taro.reportMonitor", "body": ["Taro.reportMonitor(${1:name},${2:value});"], "description": "自定义业务数据监控上报接口。"}, "Taro.reportEvent": {"prefix": "Taro.reportEvent", "body": ["Taro.reportEvent('purchase', {", "\tprice: 120,", "\tcolor: 'red'", "})"], "description": "事件上报"}, "Taro.reportAnalytics": {"prefix": "Taro.reportAnalytics", "body": ["Taro.reportAnalytics('purchase', {", "\tprice: 120,", "\tcolor: 'red'", "})"], "description": "自定义分析数据上报接口。使用前，需要在小程序管理后台自定义分析中新建事件，配置好事件名与字段。"}, "Taro.getExptInfoSync": {"prefix": "Taro.getExptInfoSync", "body": ["Taro.getExptInfoSync(['color']);"], "description": "给定实验参数数组，获取对应的实验参数值"}, "Taro.createOffscreenCanvas": {"prefix": "Taro.createOffscreenCanvas", "body": ["const res = Taro.createOffscreenCanvas({", "\ttype: '2d',", "\theight: '100',", "\twidth: '100',", "});"], "description": "创建离屏 canvas 实例"}, "Taro.canvasToTempFilePath": {"prefix": "Taro.canvasToTempFilePath", "body": ["Taro.canvasToTempFilePath({", "\tx: ${1:0},", "\ty: ${2:0},", "\twidth: ${3},", "\theight: ${4},", "\tdestWidth: ${5},", "\tdestHeight: ${6},", "\tcanvasId: ${7},", "\tfileType: ${8:png},", "\tquality: ${9:1.0},", "\tsuccess: (result)=>{", "\t\t${10}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "}, this);"], "description": "把当前画布指定区域的内容导出生成指定大小的图片，并返回文件路径。"}, "Taro.canvasPutImageData": {"prefix": "Taro.canvasPutImageData", "body": ["Taro.canvasPutImageData({", "\tcanvasId: ${1},", "\tdata: ${2},", "\tx: ${3},", "\ty: ${4},", "\twidth: ${5},", "\theight: ${6},", "\tsuccess: (result)=>{", "\t\t${7}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "}, this);"], "description": "将像素数据绘制到画布的方法。"}, "Taro.canvasGetImageData": {"prefix": "Taro.canvasGetImageData", "body": ["Taro.canvasGetImageData({", "\tcanvasId: ${1},", "\tx: ${2:0},", "\ty: ${3:0},", "\twidth: ${4},", "\theight: ${5},", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "}, this);"], "description": "返回一个数组，用来描述 canvas 区域隐含的像素数据。"}, "Taro.createCanvasContext": {"prefix": "Taro.createCanvasContext", "body": ["const canvasContext = Taro.createCanvasContext(${2:canvasId}, this);"], "description": "创建 canvas 绘图上下文（指定 canvasId）。"}, "canvasContext.setFillStyle": {"prefix": "canvasContext.setFillStyle", "body": ["canvasContext.setFillStyle('${1}');"], "description": "设置填充色。"}, "canvasContext.fillStyle": {"prefix": "canvasContext.fillStyle", "body": ["canvasContext.fillStyle = ${1};"], "description": "设置填充色。基础库 1.9.90 起支持"}, "canvasContext.setStrokeStyle": {"prefix": "canvasContext.setStrokeStyle", "body": ["canvasContext.setStrokeStyle(${1});"], "description": "设置边框颜色。"}, "canvasContext.strokeStyle": {"prefix": "canvasContext.strokeStyle", "body": ["canvasContext.strokeStyle = ${1};"], "description": "设置边框颜色。基础库 1.9.90 起支持"}, "canvasContext.setShadow": {"prefix": "canvasContext.setShadow", "body": ["canvasContext.setShadow(${1:offsetX}, ${2:offsetY}, ${3:blur}, ${4:color});"], "description": "设置阴影样式。"}, "canvasContext.createLinearGradient": {"prefix": "canvasContext.createLinearGradient", "body": ["canvasContext.createLinearGradient(${1:x0}, ${2:y0}, ${3:x1}, ${4:y1});"], "description": "创建一个线性的渐变颜色。需要使用 addColorStop() 来指定渐变点，至少要两个。"}, "canvasContext.createCircularGradient": {"prefix": "canvasContext.createCircularGradient", "body": ["canvasContext.createCircularGradient(${1:x}, ${2:y}, ${3:r});"], "description": "创建一个圆形的渐变颜色。需要使用 addColorStop() 来指定渐变点，至少要两个。"}, "canvasContext.addColorStop": {"prefix": "canvasContext.addColorStop", "body": ["canvasContext.addColorStop(${1:stop(0 ~ 1)}, ${2:color});"], "description": "创建一个颜色的渐变点。"}, "canvasContext.setLineWidth": {"prefix": "canvasContext.setLineWidth", "body": ["canvasContext.setLineWidth(${1});"], "description": "设置线条的宽度。"}, "canvasContext.lineWidth": {"prefix": "canvasContext.lineWidth", "body": ["canvasContext.lineWidth = ${1};"], "description": "设置线条的宽度。基础库 1.9.90 起支持"}, "canvasContext.setLineCap": {"prefix": "canvasContext.setLineCap", "body": ["canvasContext.setLineCap('${1:butt}');"], "description": "设置线条的端点样式。"}, "canvasContext.lineCap": {"prefix": "canvasContext.lineCap", "body": ["canvasContext.lineCap = '${1:butt}';"], "description": "设置线条的端点样式。基础库 1.9.90 起支持"}, "canvasContext.setLineJoin": {"prefix": "canvasContext.setLineJoin", "body": ["canvasContext.setLineJoin('${1:bevel}');"], "description": "设置线条的交点样式。"}, "canvasContext.lineJoin": {"prefix": "canvasContext.lineJoin", "body": ["canvasContext.lineJoin = '${1:bevel}';"], "description": "设置线条的交点样式。基础库 1.9.90 起支持"}, "canvasContext.setLineDash": {"prefix": "canvasContext.setLineDash", "body": ["canvasContext.setLineDash([${1:pattern}], ${2:offset});"], "description": "设置线条的宽度。"}, "canvasContext.setMiterLimit": {"prefix": "canvasContext.setMiterLimit", "body": ["canvasContext.setMiterLimit(${1});"], "description": "设置最大斜接长度，斜接长度指的是在两条线交汇处内角和外角之间的距离。 当 setLineJoin() 为 miter 时才有效。"}, "canvasContext.rect": {"prefix": "canvasContext.rect", "body": ["canvasContext.rect(${1:x}, ${2:y}, ${3:width}, ${4:height});"], "description": "创建一个矩形。"}, "canvasContext.fillRect": {"prefix": "canvasContext.fillRect", "body": ["canvasContext.fillRect(${1:x}, ${2:y}, ${3:width}, ${4:height});"], "description": "填充一个矩形。"}, "canvasContext.strokeRect": {"prefix": "canvasContext.strokeRect", "body": ["canvasContext.strokeRect(${1:x}, ${2:y}, ${3:width}, ${4:height});"], "description": "画一个矩形(非填充)。"}, "canvasContext.clearRect": {"prefix": "canvasContext.clearRect", "body": ["canvasContext.clearRect(${1:x}, ${2:y}, ${3:width}, ${4:height});"], "description": "清除画布上在该矩形区域内的内容。"}, "canvasContext.fill": {"prefix": "canvasContext.fill", "body": ["canvasContext.fill();"], "description": "对当前路径中的内容进行填充。"}, "canvasContext.stroke": {"prefix": "canvasContext.stroke", "body": ["canvasContext.stroke();"], "description": "画出当前路径的边框。"}, "canvasContext.beginPath": {"prefix": "canvasContext.beginPath", "body": ["canvasContext.beginPath();"], "description": "开始创建一个路径，需要调用fill或者stroke才会使用路径进行填充或描边。"}, "canvasContext.closePath": {"prefix": "canvasContext.closePath", "body": ["canvasContext.closePath();"], "description": "关闭一个路径"}, "canvasContext.moveTo": {"prefix": "canvasContext.moveTo", "body": ["canvasContext.moveTo(${1:x}, ${2:y});"], "description": "把路径移动到画布中的指定点，不创建线条。"}, "canvasContext.lineTo": {"prefix": "canvasContext.lineTo", "body": ["canvasContext.lineTo(${1:x}, ${2:y});"], "description": "lineTo 方法增加一个新点，然后创建一条从上次指定点到目标点的线。"}, "canvasContext.arc": {"prefix": "canvasContext.arc", "body": ["canvasContext.arc(${1:x}, ${2:y}, ${3:r}, ${4:sAngle}, ${5:eAngle}, ${6:false});"], "description": "画一条弧线。"}, "canvasContext.bezierCurveTo": {"prefix": "canvasContext.bezierCurveTo", "body": ["canvasContext.bezierCurveTo(${1:cp1x}, ${2:cp1y}, ${3:cp2x}, ${4:cp2y}, ${5:x}, ${6:y});"], "description": "创建三次方贝塞尔曲线路径。曲线的起始点为路径中前一个点。"}, "canvasContext.quadraticCurveTo": {"prefix": "canvasContext.quadraticCurveTo", "body": ["canvasContext.quadraticCurveTo(${1:cpx}, ${2:cpy}, ${3:x}, ${4:y});"], "description": "创建二次贝塞尔曲线路径。曲线的起始点为路径中前一个点。"}, "canvasContext.scale": {"prefix": "canvasContext.scale", "body": ["canvasContext.scale(${1:scaleWidth}, ${2:scaleHeight});"], "description": "在调用scale方法后，之后创建的路径其横纵坐标会被缩放。多次调用scale，倍数会相乘。"}, "canvasContext.rotate": {"prefix": "canvasContext.rotate", "body": ["canvasContext.rotate(${1:degrees} * Math.PI/180);"], "description": "以原点为中心，原点可以用 translate方法修改。顺时针旋转当前坐标轴。"}, "canvasContext.translate": {"prefix": "canvasContext.translate", "body": ["canvasContext.translate(${1:x}, ${2:y});"], "description": "对当前坐标系的原点(0, 0)进行变换，默认的坐标系原点为页面左上角。"}, "canvasContext.clip": {"prefix": "canvasContext.clip", "body": ["canvasContext.clip();"], "description": "从原始画布中剪切任意形状和尺寸。"}, "canvasContext.setFontSize": {"prefix": "canvasContext.setFontSize", "body": ["canvasContext.setFontSize(${1:20});"], "description": "设置字体的字号。"}, "canvasContext.fillText": {"prefix": "canvasContext.fillText", "body": ["canvasContext.fillText('${1:text}', ${2:x}, ${3:y});"], "description": "在画布上绘制被填充的文本。"}, "canvasContext.setTextAlign": {"prefix": "canvasContext.setTextAlign", "body": ["canvasContext.setTextAlign('${1:center}');"], "description": "用于设置文字的对齐"}, "canvasContext.textAlign": {"prefix": "canvasContext.textAlign", "body": ["canvasContext.textAlign = '${1:center}';"], "description": "用于设置文字的对齐,基础库 1.9.90 起支持"}, "canvasContext.setTextBaseline": {"prefix": "canvasContext.setTextBaseline", "body": ["canvasContext.setTextBaseline('${1:middle}');"], "description": "用于设置文字的水平对齐"}, "canvasContext.textBaseline": {"prefix": "canvasContext.textBaseline", "body": ["canvasContext.textBaseline = '${1:middle}';"], "description": "用于设置文字的水平对齐,基础库 1.9.90 起支持"}, "canvasContext.drawImage": {"prefix": "canvasContext.drawImage", "body": ["canvasContext.drawImage(${1:imageResource}, ${2:dx}, ${3:dy}, ${4:dWidth}, ${5:dHeight});"], "description": "绘制图像到画布。"}, "canvasContext.setGlobalAlpha": {"prefix": "canvasContext.setGlobalAlpha", "body": ["canvasContext.setGlobalAlpha(${1:1});"], "description": "设置全局画笔透明度。"}, "canvasContext.globalAlpha": {"prefix": "canvasContext.globalAlpha", "body": ["canvasContext.globalAlpha = ${1:1};"], "description": "设置全局画笔透明度。基础库 1.9.90 起支持"}, "canvasContext.save": {"prefix": "canvasContext.save", "body": ["canvasContext.save();"], "description": "保存当前的绘图上下文。"}, "canvasContext.restore": {"prefix": "canvasContext.restore", "body": ["canvasContext.restore();"], "description": "恢复之前保存的绘图上下文。"}, "canvasContext.draw": {"prefix": "canvasContext.draw", "body": ["canvasContext.draw(${1:true}, ${2:callback});"], "description": "将之前在绘图上下文中的描述（路径、变形、样式）画到 canvas 中"}, "canvasContext.measureText": {"prefix": "canvasContext.measureText", "body": ["${1:const metrics =}canvasContext.measureText(${2});"], "description": "测量文本尺寸信息，目前仅返回文本宽度。同步接口。"}, "canvasContext.globalCompositeOperation": {"prefix": "canvasContext.globalCompositeOperation", "body": ["canvasContext.globalCompositeOperation = ${1};"], "description": "该属性是设置要在绘制新形状时应用的合成操作的类型。"}, "canvasContext.arcTo": {"prefix": "canvasContext.arcTo", "body": ["canvasContext.arcTo(${1:x1}, ${2:y1}, ${3:x2}, ${4:y2}, ${5:radius});"], "description": "根据控制点和半径绘制圆弧路径。"}, "canvasContext.strokeText": {"prefix": "canvasContext.strokeText", "body": ["canvasContext.strokeText(${1:text}, ${2:x}, ${3:y});"], "description": "给定的 (x, y) 位置绘制文本描边的方法"}, "canvasContext.lineDashOffset": {"prefix": "canvasContext.lineDashOffset", "body": ["canvasContext.lineDashOffset = ${1:value};"], "description": "设置虚线偏移量的属性"}, "canvasContext.createPattern": {"prefix": "canvasContext.createPattern", "body": ["canvasContext.createPattern(${1:image}, ${2:repetition});"], "description": "对指定的图像创建模式的方法，可在指定的方向上重复元图像"}, "canvasContext.shadowBlur": {"prefix": "canvasContext.shadowBlur", "body": ["canvasContext.shadowBlur = ${1:value};"], "description": "设置阴影的模糊级别"}, "canvasContext.shadowColor": {"prefix": "canvasContext.shadowColor", "body": ["canvasContext.shadowColor = ${1:value};"], "description": "设置阴影的颜色"}, "canvasContext.shadowOffsetX": {"prefix": "canvasContext.shadowOffsetX", "body": ["canvasContext.shadowOffsetX = ${1:value};"], "description": "设置阴影相对于形状在水平方向的偏移"}, "canvasContext.shadowOffsetY": {"prefix": "canvasContext.shadowOffsetY", "body": ["canvasContext.shadowOffsetY = ${1:value};"], "description": "设置阴影相对于形状在竖直方向的偏移"}, "canvasContext.font": {"prefix": "canvasContext.font", "body": ["canvasContext.font = '${1:style}, ${2:weight}, ${3:size}, ${4:family}';"], "description": "设置当前字体样式的属性"}, "canvasContext.setTransform": {"prefix": "canvasContext.setTransform", "body": ["canvasContext.setTransform(${1:scaleX}, ${2:skewX}, ${3:skewY}, ${4:scaleY}, ${5:translateX}, ${6:translateY});"], "description": "使用矩阵重新设置（覆盖）当前变换的方法"}, "Taro.createMapContext": {"prefix": "Taro.createMapContext", "body": ["${1:const mapContext = }Taro.createMapContext('${2:map}'${3:, this});"], "description": "创建并返回 map 上下文 mapContext 对象。在自定义组件下，第二个参数传入组件实例this，以操作组件内 <map/> 组件"}, "mapContext.getCenterLocation": {"prefix": "mapContext.getCenterLocation", "body": ["mapContext.getCenterLocation({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取当前地图中心的经纬度，返回的是 gcj02 坐标系，可以用于 Taro.openLocation"}, "mapContext.moveToLocation": {"prefix": "mapContext.moveToLocation", "body": ["mapContext.moveToLocation();"], "description": "将地图中心移动到当前定位点，需要配合map组件的show-location使用"}, "mapContext.translateMarker": {"prefix": "mapContext.translateMarker", "body": ["mapContext.translateMarker({", "\tmarkerId: ${1},", "\tdestination: {", "\t\tlatitude: ${2},", "\t\tlongitude: ${3}", "\t},", "\tautoRotate: ${4:false},", "\trotate: ${5},", "\tduration: ${6:1000},", "\tanimationEnd: ()=>{},", "\tfail: ()=>{}", "});"], "description": "平移marker，带动画"}, "mapContext.includePoints": {"prefix": "mapContext.includePoints", "body": ["mapContext.includePoints({", "\tpoints: [", "\t\t{", "\t\t\tlatitude: ${1:latitude},", "\t\t\tlongitude: ${2:longitude}", "\t\t}", "\t],", "\tpadding:[$3]", "});"], "description": "缩放视野展示所有经纬度"}, "mapContext.getRegion": {"prefix": "mapContext.getRegion", "body": ["mapContext.getRegion({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取当前地图的视野范围"}, "mapContext.getScale": {"prefix": "mapContext.getScale", "body": ["mapContext.getScale({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取当前地图的缩放级别"}, "Taro.saveImageToPhotosAlbum": {"prefix": "Taro.saveImageToPhotosAlbum", "body": ["Taro.saveImageToPhotosAlbum({", "\tfilePath: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "保存图片到系统相册。需要用户授权 scope.writePhotosAlbum"}, "Taro.previewMedia": {"prefix": "Taro.previewMedia", "body": ["Taro.previewMedia({", "\tcurrent: '${1}',", "\tsources: [${2}],", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "预览图片和视频。"}, "Taro.previewImage": {"prefix": "Taro.previewImage", "body": ["Taro.previewImage({", "\tcurrent: '${1}',", "\turls: [${2}],", "\tsuccess: ()=>{},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "预览图片。"}, "Taro.getImageInfo": {"prefix": "Taro.getImageInfo", "body": ["Taro.getImageInfo({", "\tsrc: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取图片信息，倘若为网络图片，需先配置download域名才能生效。"}, "Taro.editImage": {"prefix": "Taro.editImage", "body": ["Taro.editImage({", "\tsrc: '', // 图片路径", "})"], "description": "编辑图片接口"}, "Taro.compressImage": {"prefix": "Taro.compressImage", "body": ["Taro.compressImage({", "\tsrc: '', // 图片路径", "\tquality: 80 // 压缩质量", "})"], "description": "压缩图片接口，可选压缩质量"}, "Taro.chooseMessageFile": {"prefix": "Taro.chooseMessageFile", "body": ["Taro.chooseMessageFile({", "\tcount: 10,", "\ttype: 'image',", "\tsuccess: function (res) {", "\t\t// tempFilePath可以作为img标签的src属性显示图片", "\t\tconst tempFilePaths = res.tempFilePaths", "\t}", "})"], "description": "从客户端会话选择文件。"}, "Taro.chooseImage": {"prefix": "Taro.chooseImage", "body": ["Taro.chooseImage({", "\tcount: ${1:9},", "\tsizeType: ${2:['original','compressed']},", "\tsourceType: ${3:['album','camera']},", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "从本地相册选择图片或使用相机拍照。"}, "Taro.cropImage": {"prefix": "Taro.cropImage", "body": ["Taro.cropImage({", "\tsrc: '', // 图片路径", "\tcropScale: '1:1', // 裁剪比例", "\tsuccess: function (res) {", "\t\t// 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片", "\t\tconst tempFilePaths = res.tempFilePaths", "\t}", "})"], "description": "从客户端会话选择文件。"}, "Taro.saveVideoToPhotosAlbum": {"prefix": "Taro.saveVideoToPhotosAlbum", "body": ["Taro.saveVideoToPhotosAlbum({", "\tfilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "保存视频到系统相册。需要用户授权 scope.writePhotosAlbum"}, "Taro.openVideoEditor": {"prefix": "Taro.openVideoEditor", "body": ["Taro.openVideoEditor({", "\tfilePath: ''", "})"], "description": "打开视频编辑器"}, "Taro.getVideoInfo": {"prefix": "Taro.getVideoInfo", "body": ["Taro.downloadFile({", "  url: '${1:https://mock.taro.org/mock_video.mp4}',", "  success(res) {", "    Taro.getVideoInfo({", "      src: res.tempFile<PERSON>,", "      success (res) {", "        console.log(res)", "      },", "      fail (res) {", "        console.log(res)", "      },", "    })", "  }", "})"], "description": ""}, "Taro.createVideoContext": {"prefix": "Taro.createVideoContext", "body": ["${1:const videoContext = }Taro.createVideoContext('${2:videoId}'${3:, this});"], "description": "创建并返回 video 上下文 videoContext 对象。在自定义组件下，第二个参数传入组件实例this，以操作组件内 <video/> 组件"}, "videoContext.play": {"prefix": "videoContext.play", "body": ["videoContext.play();"], "description": "播放"}, "videoContext.pause": {"prefix": "videoContext.pause", "body": ["videoContext.pause();"], "description": "暂停"}, "videoContext.stop": {"prefix": "videoContext.stop", "body": ["videoContext.stop();"], "description": "停止"}, "videoContext.seek": {"prefix": "videoContext.seek", "body": ["videoContext.seek(${1});"], "description": "跳转到指定位置，单位 s"}, "videoContext.sendDanmu": {"prefix": "videoContext.sendDanmu", "body": ["videoContext.sendDanmu({", "\ttext: ${1},", "\tcolor: ${2}", "});"], "description": "发送弹幕，包含两个属性 text, color。"}, "videoContext.playbackRate": {"prefix": "videoContext.playbackRate", "body": ["videoContext.playbackRate(${1});"], "description": "设置倍速播放，支持的倍率有 0.5/0.8/1.0/1.25/1.5"}, "videoContext.requestFullScreen": {"prefix": "videoContext.requestFullScreen", "body": ["videoContext.requestFullScreen({", "\tdirection: ${1:0}", "});"], "description": "进入全屏，可传入{direction}参数, 有效值为 0, 90,-90"}, "videoContext.exitFullScreen": {"prefix": "videoContext.exitFullScreen", "body": ["videoContext.exitFullScreen();"], "description": "退出全屏"}, "videoContext.showStatusBar": {"prefix": "videoContext.showStatusBar", "body": ["videoContext.showStatusBar();"], "description": "显示状态栏，仅在iOS全屏下有效"}, "videoContext.hideStatusBar": {"prefix": "videoContext.hideStatusBar", "body": ["videoContext.hideStatusBar();"], "description": "隐藏状态栏，仅在iOS全屏下有效"}, "Taro.compressVideo": {"prefix": "Taro.compressVideo", "body": ["Taro.compressVideo({", "\tsrc: res.tempFile<PERSON>,", "\tquality: quality,", "\tbitrate: 1032,", "\tfps: 24,", "\tresolution:0.5,", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "压缩视频接口。 开发者可指定压缩质量 quality 进行压缩。当需要更精细的控制时，可指定 bitrate、fps、和 resolution，当 quality 传入时，这三个参数将被忽略。原视频的相关信息可通过 getVideoInfo 获取。"}, "Taro.chooseVideo": {"prefix": "Taro.chooseVideo", "body": ["Taro.chooseVideo({", "\tsourceType:['album', 'camera'],", "\tcompressed: ${1:true},", "\tmaxDuration:${2:15},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "拍摄视频或从手机相册中选视频，返回视频的临时文件路径。"}, "Taro.chooseMedia": {"prefix": "Taro.chooseMedia", "body": ["Taro.chooseMedia({", "\tcount: 9,", "\tmediaType: ['image','video'],", "\tsourceType: ['album', 'camera'],", "\tmaxDuration: 30,", "\tcamera: 'back',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "拍摄或从手机相册中选择图片或视频。"}, "Taro.stopVoice": {"prefix": "Taro.stopVoice", "body": ["Taro.stopVoice();"], "description": "结束播放语音。1.6.0 起不再维护。建议使用能力更强的 Taro.createInnerAudioContext 接口"}, "Taro.setInnerAudioOption": {"prefix": "Taro.setInnerAudioOption", "body": ["Taro.setInnerAudioOption({", "\tmixWithOther: true,", "\tobeyMuteSwitch: true,", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "设置 InnerAudioContext项。设置之后对当前小程序全局生效。"}, "Taro.playVoice": {"prefix": "Taro.playVoice", "body": ["Taro.playVoice({", "\tfilePath: '${1}',", "\tduration: ${2:60},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始播放语音，同时只允许一个语音文件正在播放，如果前一个语音文件还没播放完，将中断前一个语音播放。1.6.0 起不再维护"}, "Taro.pauseVoice": {"prefix": "Taro.pauseVoice", "body": ["Taro.pauseVoice();"], "description": "暂停正在播放的语音。1.6.0 起不再维护"}, "Taro.getAvailableAudioSources": {"prefix": "Taro.getAvailableAudioSources", "body": ["Taro.getAvailableAudioSources({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取当前支持的音频输入源"}, "Taro.createWebAudioContext": {"prefix": "Taro.createWebAudioContext", "body": ["const res = Taro.createWebAudioContext();"], "description": "创建 WebAudio 上下文。"}, "Taro.createMediaAudioPlayer": {"prefix": "Taro.createMediaAudioPlayer", "body": ["const res = Taro.createMediaAudioPlayer();"], "description": "创建媒体音频播放器对象 MediaAudioPlayer 对象，可用于播放视频解码器 VideoDecoder 输出的音频"}, "Taro.createInnerAudioContext": {"prefix": "Taro.createInnerAudioContext", "body": ["// properties(Read only)(duration,currentTime,paused,buffered,volume)", "// properties(src,startTime,autoplay,loop,obeyMuteSwitch)", "${1:const innerAudioContext = }Taro.createInnerAudioContext();"], "description": "创建并返回内部 audio 上下文 innerAudioContext 对象。本接口是 Taro.createAudioContext 升级版。1.6.0 开始支持"}, "innerAudioContext.play": {"prefix": "inneraudioContext.play", "body": ["innerAudioContext.play();"], "description": "播放"}, "innerAudioContext.pause": {"prefix": "inneraudioContext.pause", "body": ["innerAudioContext.pause();"], "description": "暂停"}, "innerAudioContext.stop": {"prefix": "inneraudioContext.stop", "body": ["innerAudioContext.stop();"], "description": "停止"}, "innerAudioContext.seek": {"prefix": "inneraudioContext.seek", "body": ["innerAudioContext.seek(${1});"], "description": "跳转到指定位置，单位 s。精确到小数点后 3 位，即支持 ms 级别精确度"}, "innerAudioContext.destroy": {"prefix": "inneraudioContext.destroy", "body": ["innerAudioContext.destroy();"], "description": "销毁当前实例"}, "innerAudioContext.onCanplay": {"prefix": "inneraudioContext.onCanplay", "body": ["innerAudioContext.onCanplay(()=>{${1}});"], "description": "音频进入可以播放状态，但不保证后面可以流畅播放"}, "innerAudioContext.onPlay": {"prefix": "inneraudioContext.onPlay", "body": ["innerAudioContext.onPlay((${1})=>{", "\t${2}", "});"], "description": "音频播放事件"}, "innerAudioContext.onPause": {"prefix": "inneraudioContext.onPause", "body": ["innerAudioContext.onPause((${1})=>{", "\t${2}", "});"], "description": "音频暂停播放事件"}, "innerAudioContext.onStop": {"prefix": "inneraudioContext.onStop", "body": ["innerAudioContext.onStop((${1})=>{", "\t${2}", "});"], "description": "音频停止播放事件"}, "innerAudioContext.onEnded": {"prefix": "inneraudioContext.onEnded", "body": ["innerAudioContext.onEnded((${1})=>{", "\t${2}", "});"], "description": "音频自然播放结束事件"}, "innerAudioContext.onTimeUpdate": {"prefix": "inneraudioContext.onTimeUpdate", "body": ["innerAudioContext.onTimeUpdate((${1})=>{", "\t${2}", "});"], "description": "音频播放进度更新事件"}, "innerAudioContext.onError": {"prefix": "inneraudioContext.onError", "body": ["innerAudioContext.onError((${1:errMsg})=>{", "\t${2}", "});"], "description": "音频播放错误事件"}, "innerAudioContext.onWaiting": {"prefix": "inneraudioContext.onWaiting", "body": ["innerAudioContext.onWaiting((${1})=>{", "\t${2}", "});"], "description": "音频加载中事件，当音频因为数据不足，需要停下来加载时会触发"}, "innerAudioContext.onSeeking": {"prefix": "inneraudioContext.onSeeking", "body": ["innerAudioContext.onSeeking((${1})=>{", "\t${2}", "});"], "description": "音频进行seek操作事件"}, "innerAudioContext.onSeeked": {"prefix": "inneraudioContext.onSeeked", "body": ["innerAudioContext.onSeeked((${1})=>{", "\t${2}", "});"], "description": "音频完成seek操作事件"}, "innerAudioContext.offCanplay": {"prefix": "inneraudioContext.offCanplay", "body": ["innerAudioContext.offCanplay((${1})=>{", "\t${2}", "});"], "description": "取消监听onCanplay事件"}, "innerAudioContext.offPlay": {"prefix": "inneraudioContext.offPlay", "body": ["innerAudioContext.offPlay((${1})=>{", "\t${2}", "});"], "description": "取消监听onPlay事件"}, "innerAudioContext.offStop": {"prefix": "inneraudioContext.offStop", "body": ["innerAudioContext.offStop((${1})=>{", "\t${2}", "});"], "description": "取消监听onStop事件"}, "innerAudioContext.offEnded": {"prefix": "inneraudioContext.offEnded", "body": ["innerAudioContext.offEnded((${1})=>{", "\t${2}", "});"], "description": "取消监听onEnded事件"}, "innerAudioContext.offTimeUpdate": {"prefix": "inneraudioContext.offTimeUpdate", "body": ["innerAudioContext.offTimeUpdate((${1})=>{", "\t${2}", "});"], "description": "取消监听onTimeUpdate事件"}, "innerAudioContext.offError": {"prefix": "inneraudioContext.offError", "body": ["innerAudioContext.offError((${1})=>{", "\t${2}", "});"], "description": "取消监听onError事件"}, "innerAudioContext.offWaiting": {"prefix": "inneraudioContext.offWaiting", "body": ["innerAudioContext.offWaiting((${1})=>{", "\t${2}", "});"], "description": "取消监听onWaiting事件"}, "innerAudioContext.offSeeking": {"prefix": "inneraudioContext.offSeeking", "body": ["innerAudioContext.offSeeking((${1})=>{", "\t${2}", "});"], "description": "取消监听onSeeking事件"}, "innerAudioContext.offSeeked": {"prefix": "inneraudioContext.offSeeked", "body": ["innerAudioContext.offSeeked((${1})=>{", "\t${2}", "});"], "description": "取消监听onSeeked事件"}, "Taro.createAudioContext": {"prefix": "Taro.createAudioContext", "body": ["${1:const audioContext = }Taro.createAudioContext(${2}, this);"], "description": "创建并返回audio上下文audioContext对象。在自定义组件下，第二个参数传入组件实例this，以操作组件内<audio/>组件。1.6.0 起不再维护"}, "audioContext.setSrc": {"prefix": "audioContext.setSrc", "body": ["audioContext.setSrc('${1}');"], "description": "设置音频的地址。1.6.0 起不再维护"}, "audioContext.play": {"prefix": "audioContext.play", "body": ["audioContext.play();"], "description": "播放。1.6.0 起不再维护"}, "audioContext.pause": {"prefix": "audioContext.pause", "body": ["audioContext.pause();"], "description": "暂停。1.6.0 起不再维护"}, "audioContext.seek": {"prefix": "audioContext.seek", "body": ["audioContext.seek(${1});"], "description": "跳转到指定位置，单位 s。1.6.0 起不再维护"}, "Taro.stopBackgroundAudio": {"prefix": "Taro.stopBackgroundAudio", "body": ["Taro.stopBackgroundAudio();"], "description": "停止播放音乐。1.2.0 起不再维护"}, "Taro.seekBackgroundAudio": {"prefix": "Taro.seekBackgroundAudio", "body": ["Taro.seekBackgroundAudio({", "\tposition: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "控制音乐播放进度(秒)。1.2.0 起不再维护"}, "Taro.playBackgroundAudio": {"prefix": "Taro.playBackgroundAudio", "body": ["Taro.playBackgroundAudio({", "\tdataUrl: '${1}',", "\ttitle: '${2}',", "\tcoverImgUrl: '${3}',", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "使用后台播放器播放音乐，对于微信客户端来说，只能同时有一个后台音乐在播放。1.2.0 起不再维护"}, "Taro.pauseBackgroundAudio": {"prefix": "Taro.pauseBackgroundAudio", "body": ["Taro.playBackgroundAudio();"], "description": "暂停播放音乐。1.2.0 起不再维护"}, "Taro.onBackgroundAudioStop": {"prefix": "Taro.onBackgroundAudioStop", "body": ["Taro.onBackgroundAudioStop((${1:result})=>{${2}});"], "description": "监听音乐停止。1.2.0 起不再维护"}, "Taro.onBackgroundAudioPlay": {"prefix": "Taro.onBackgroundAudioPlay", "body": ["Taro.onBackgroundAudioPlay((${1:result})=>{${2}});"], "description": "监听音乐播放。1.2.0 起不再维护"}, "Taro.onBackgroundAudioPause": {"prefix": "Taro.onBackgroundAudioPause", "body": ["Taro.onBackgroundAudioPause((${1:result})=>{${2}});"], "description": "监听音乐暂停。1.2.0 起不再维护"}, "Taro.getBackgroundAudioPlayerState": {"prefix": "Taro.getBackgroundAudioPlayerState", "body": ["Taro.getBackgroundAudioPlayerState({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取后台音乐播放状态。1.2.0 起不再维护"}, "Taro.getBackgroundAudioManager": {"prefix": "Taro.getBackgroundAudioManager", "body": ["// properties(Read only)(duration,currentTime,paused,buffered)", "// properties(src(m4a, aac, mp3, wav),startTime,title,epname,singer,coverImgUrl,webUrl,protocol)", "${1:const backAudioManager = }Taro.getBackgroundAudioManager();"], "description": "获取全局唯一的背景音频管理器 backgroundAudioManager。"}, "backAudioManager.src": {"prefix": "backAudioManager.src", "body": ["backAudioManager.src = ${1};"], "description": "音频的数据源，默认为空字符串，当设置了新的 src 时，会自动开始播放 ，目前支持的格式有 m4a, aac, mp3, wav。"}, "backAudioManager.play": {"prefix": "backAudioManager.play", "body": ["backAudioManager.play();"], "description": "播放"}, "backAudioManager.pause": {"prefix": "backAudioManager.pause", "body": ["backAudioManager.pause();"], "description": "暂停"}, "backAudioManager.stop": {"prefix": "backAudioManager.stop", "body": ["backAudioManager.stop();"], "description": "停止"}, "backAudioManager.seek": {"prefix": "backAudioManager.seek", "body": ["backAudioManager.seek(${1});"], "description": "跳转到指定位置，单位 s。精确到小数点后 3 位，即支持 ms 级别精确度。"}, "backAudioManager.onCanplay": {"prefix": "backAudioManager.onCanplay", "body": ["backAudioManager.onCanplay(${1:()=>{", "\t\t${2}", "\t}});"], "description": "背景音频进入可以播放状态，但不保证后面可以流畅播放"}, "backAudioManager.onPlay": {"prefix": "backAudioManager.onPlay", "body": ["backAudioManager.onPlay(()=>{", "\t${1}", "});"], "description": "背景音频播放事件"}, "backAudioManager.onPause": {"prefix": "backAudioManager.onPause", "body": ["backAudioManager.onPause(()=>{", "\t${1}", "});"], "description": "背景音频暂停事件"}, "backAudioManager.onStop": {"prefix": "backAudioManager.onStop", "body": ["backAudioManager.onStop(()=>{", "\t${1}", "});"], "description": "背景音频停止事件"}, "backAudioManager.onEnded": {"prefix": "backAudioManager.onEnded", "body": ["backAudioManager.onEnded(()=>{", "\t${1}", "});"], "description": "背景音频自然播放结束事件"}, "backAudioManager.onTimeUpdate": {"prefix": "backAudioManager.onTimeUpdate", "body": ["backAudioManager.onTimeUpdate((${1})=>{", "\t${2}", "});"], "description": "背景音频播放进度更新事件"}, "backAudioManager.onPrev": {"prefix": "backAudioManager.onPrev", "body": ["backAudioManager.onPrev((${1})=>{", "\t${2}", "});"], "description": "用户在系统音乐播放面板点击上一曲事件（iOS only）"}, "backAudioManager.onNext": {"prefix": "backAudioManager.onNext", "body": ["backAudioManager.onNext((${1})=>{", "\t${2}", "});"], "description": "用户在系统音乐播放面板点击下一曲事件（iOS only）"}, "backAudioManager.onError": {"prefix": "backAudioManager.onError", "body": ["backAudioManager.onError((${1:errMsg})=>{", "\t${2}", "});"], "description": "背景音频播放错误事件"}, "backAudioManager.onWaiting": {"prefix": "backAudioManager.onWaiting", "body": ["backAudioManager.onWaiting((${1})=>{", "\t${2}", "});"], "description": "音频加载中事件，当音频因为数据不足，需要停下来加载时会触发"}, "Taro.createLivePusherContext": {"prefix": "Taro.createLivePusherContext", "body": ["${1:const livePusherContext = }Taro.createLivePusherContext('${2:live-pusher}'${3:, this})"], "description": "创建并返回 live-pusher 上下文 LivePusherContext 对象，LivePusherContext 与页面的 <live-pusher /> 组件绑定，一个页面只能有一个 live-pusher，通过它可以操作对应的 <live-pusher/> 组件。 在自定义组件下，第一个参数传入组件实例this，以操作组件内 <live-pusher/> 组件。"}, "livePusherContext.play": {"prefix": "livePusherContext.play", "body": ["livePusherContext.play({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "播放推流"}, "livePusherContext.stop": {"prefix": "livePusherContext.stop", "body": ["livePusherContext.stop({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止推流"}, "livePusherContext.pause": {"prefix": "livePusherContext.pause", "body": ["livePusherContext.pause({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "暂停推流"}, "livePusherContext.resume": {"prefix": "livePusherContext.resume", "body": ["livePusherContext.resume({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "恢复推流"}, "livePusherContext.switchCamera": {"prefix": "livePusherContext.switchCamera", "body": ["livePusherContext.switchCamera({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "切换前后摄像头"}, "livePusherContext.snapshot": {"prefix": "livePusherContext.snapshot", "body": ["livePusherContext.snapshot({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "快照"}, "livePusherContext.toggleTorch": {"prefix": "livePusherContext.toggleTorch", "body": ["livePusherContext.toggleTorch({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "切换闪光灯"}, "Taro.createLivePlayerContext": {"prefix": "Taro.createLivePlayerContext", "body": ["${1:const livePlayerContext = }Taro.createLivePlayerContext('${2:live-player}'${3:, this})"], "description": "操作对应的 <live-player/> 组件。 创建并返回 live-player 上下文 LivePlayerContext 对象。在自定义组件下，第二个参数传入组件实例this，以操作组件内 <live-player/> 组件。"}, "livePlayerContext.play": {"prefix": "livePlayerContext.play", "body": ["livePlayerContext.play({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "播放"}, "livePlayerContext.stop": {"prefix": "livePlayerContext.stop", "body": ["livePlayerContext.stop({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止"}, "livePlayerContext.mute": {"prefix": "livePlayerContext.mute", "body": ["livePlayerContext.mute({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "静音"}, "livePlayerContext.pause": {"prefix": "livePlayerContext.pause", "body": ["livePlayerContext.pause({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "暂停"}, "livePlayerContext.resume": {"prefix": "livePlayerContext.resume", "body": ["livePlayerContext.resume({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "恢复"}, "livePlayerContext.requestFullScreen": {"prefix": "livePlayerContext.requestFullScreen", "body": ["livePlayerContext.requestFullScreen({", "\tdirection: ${1:0},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "进入全屏"}, "livePlayerContext.exitFullScreen": {"prefix": "livePlayerContext.exitFullScreen", "body": ["livePlayerContext.exitFullScreen({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "退出全屏"}, "Taro.stopRecord": {"prefix": "Taro.stopRecord", "body": ["Taro.stopRecord();"], "description": "停止录音。1.6.0 起不再维护"}, "Taro.startRecord": {"prefix": "Taro.startRecord", "body": ["Taro.startRecord({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始录音。需要用户授权 scope.record。1.6.0 起不再维护"}, "Taro.getRecorderManager": {"prefix": "Taro.getRecorderManager", "body": ["${1:const recordManager = }Taro.getRecorderManager();"], "description": "获取全局唯一的录音管理器recorderManager。"}, "recordManager.start": {"prefix": "recordManager.start", "body": ["recordManager.start({", "\tduration: ${1},", "\tsampleRate: ${2:44100},", "\tnumberOfChannels: ${3:2},", "\tformat: ${4:'aac'}", "});"], "description": "开始录音"}, "recordManager.pause": {"prefix": "recordManager.pause", "body": ["recordManager.pause();"], "description": "暂停录音"}, "recordManager.resume": {"prefix": "recordManager.resume", "body": ["recordManager.resume();"], "description": "恢复录音"}, "recordManager.stop": {"prefix": "recordManager.stop", "body": ["recordManager.stop();"], "description": "停止录音"}, "recordManager.onStart": {"prefix": "recordManager.onStart", "body": ["recordManager.onStart((${1})=>{", "\t${2}", "});"], "description": "录音开始事件"}, "recordManager.onPause": {"prefix": "recordManager.onPause", "body": ["recordManager.onPause((${1})=>{", "\t${2}", "});"], "description": "录音暂停事件"}, "recordManager.onStop": {"prefix": "recordManager.onStop", "body": ["recordManager.onStop((${1:result})=>{", "\t${2}", "});"], "description": "录音停止事件，会回调文件地址"}, "recordManager.onFrameRecorded": {"prefix": "recordManager.onFrameRecorded", "body": ["recordManager.onFrameRecorded((${1:result})=>{", "\t${2}", "});"], "description": "已录制完指定帧大小的文件，会回调录音分片结果数据"}, "recordManager.onError": {"prefix": "recordManager.onError", "body": ["recordManager.onError((${1:errMsg})=>{", "\t${2}", "});"], "description": "录音错误事件, 会回调错误信息"}, "Taro.createCameraContext": {"prefix": "Taro.createCameraContext", "body": ["${1:const cameraContext = }Taro.createCameraContext(${2:this});"], "description": "创建并返回 camera 上下文 cameraContext 对象，cameraContext 与页面的 camera 组件绑定，一个页面只能有一个camera，通过它可以操作对应的 <camera/> 组件。1.6.0 开始支持"}, "cameraContext.takePhoto": {"prefix": "cameraContext.takePhoto", "body": ["cameraContext.takePhoto({", "\tquality: ${1:normal},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "拍照，可指定质量(high, normal, low，默认normal)，成功则返回图片"}, "cameraContext.startRecord": {"prefix": "cameraContext.startRecord", "body": ["cameraContext.startRecord({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "\ttimeoutCallback: (result)=>{", "\t\t${2}", "\t},", "});"], "description": "开始录像"}, "cameraContext.stopRecord": {"prefix": "cameraContext.stopRecord", "body": ["cameraContext.stopRecord({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "结束录像，成功则返回封面与视频"}, "Taro.createMediaContainer": {"prefix": "Taro.createMediaContainer", "body": ["Taro.createMediaContainer();"], "description": "创建音视频处理容器，最终可将容器中的轨道合成一个视频"}, "Taro.createMediaRecorder": {"prefix": "Taro.createMediaRecorder", "body": ["const res = Taro.createMediaRecorder({", "\t${1}", "});"], "description": "创建 WebGL 画面录制器，可逐帧录制在 WebGL 上渲染的画面并导出视频文件"}, "Taro.createVideoDecoder": {"prefix": "Taro.createVideoDecoder", "body": ["const res = Taro.createVideoDecoder();"], "description": "创建视频解码器，可逐帧获取解码后的数据"}, "Taro.stopLocationUpdate": {"prefix": "Taro.stopLocationUpdate", "body": ["Taro.stopLocationUpdate({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "关闭监听实时位置变化，前后台都停止消息接收"}, "Taro.startLocationUpdateBackground": {"prefix": "Taro.startLocationUpdateBackground", "body": ["Taro.startLocationUpdateBackground({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "开启小程序进入前后台时均接收位置消息，需引导用户开启授权。授权以后，小程序在运行中或进入后台均可接受位置消息变化。"}, "Taro.startLocationUpdate": {"prefix": "Taro.startLocationUpdate", "body": ["Taro.startLocationUpdate({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "开启小程序进入前台时接收位置消息"}, "Taro.openLocation": {"prefix": "Taro.openLocation", "body": ["Taro.openLocation({", "\tlatitude: ${1:0},", "\tlongitude: ${2:0},", "\tscale: ${3:18},", "\tname: '${4}',", "\taddress: '${5}',", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "​使用微信内置地图查看位置。 需要用户授权 scope.userLocation"}, "Taro.onLocationChangeError": {"prefix": "Taro.onLocationChangeError", "body": ["Taro.onLocationChangeError((res) => {", "\tconsole.log(res)", "})"], "description": "监听持续定位接口返回失败时触发"}, "Taro.onLocationChange": {"prefix": "Taro.onLocationChange", "body": ["Taro.onLocationChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听实时地理位置变化事件，需结合 Taro.startLocationUpdateBackground、Taro.startLocationUpdate 使用。"}, "Taro.offLocationChangeError": {"prefix": "Taro.offLocationChangeError", "body": ["Taro.offLocationChangeError((res) => {", "\tconsole.log(res)", "})"], "description": "消监听持续定位接口返回失败时触发"}, "Taro.offLocationChange": {"prefix": "Taro.offLocationChange", "body": ["Taro.offLocationChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听实时地理位置变化事件"}, "Taro.getLocation": {"prefix": "Taro.getLocation", "body": ["Taro.getLocation({", "\ttype: '${1:wgs84}',", "\taltitude: ${2:false},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取当前的地理位置、速度。"}, "Taro.getFuzzyLocation": {"prefix": "Taro.getFuzzyLocation", "body": ["Taro.getFuzzyLocation({", "\ttype: 'wgs84',", "\tsuccess (res) {", "\t\tconst latitude = res.latitude", "\t\tconst longitude = res.longitude", "\t},", "})"], "description": "获取当前的模糊地理位置"}, "Taro.choosePoi": {"prefix": "Taro.choosePoi", "body": ["Taro.choosePoi({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "打开POI列表选择位置，支持模糊定位（精确到市）和精确定位混选。"}, "Taro.chooseLocation": {"prefix": "Taro.chooseLocation", "body": ["Taro.chooseLocation({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "打开地图选择位置。需要用户授权 scope.userLocation"}, "Taro.saveFileToDisk": {"prefix": "Taro.saveFileToDisk", "body": ["Taro.saveFileToDisk({", "\tfilePath: `${Taro.env.USER_DATA_PATH}/hello.txt`,", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "保存文件系统的文件到用户磁盘，仅在 PC 端支持"}, "Taro.saveFile": {"prefix": "Taro.saveFile", "body": ["Taro.saveFile({", "\ttempFilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "保存文件到本地。注意：saveFile会把临时文件移动，因此调用成功后传入的 tempFilePath 将不可用"}, "Taro.removeSavedFile": {"prefix": "Taro.removeSavedFile", "body": ["Taro.removeSavedFile({", "\tfilePath: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "删除本地存储的文件"}, "Taro.openDocument": {"prefix": "Taro.openDocument", "body": ["Taro.openDocument({", "\tfilePath: ${1},", "\tfileType: ${2:'docx'},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "新开页面打开文档，支持格式：doc, xls, ppt, pdf, docx, xlsx, pptx"}, "Taro.getSavedFileList": {"prefix": "Taro.getSavedFileList", "body": ["Taro.getSavedFileList({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取本地已保存的文件列表"}, "Taro.getSavedFileInfo": {"prefix": "Taro.getSavedFileInfo", "body": ["Taro.getSavedFileInfo({", "\tfilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取本地文件的文件信息。此接口只能用于获取已保存到本地的文件，若需要获取临时文件信息，请使用 Taro.getFileInfo 接口"}, "Taro.getFileSystemManager": {"prefix": "Taro.getFileSystemManager", "body": ["${1:const fileManager = }Taro.getFileSystemManager();"], "description": "获取全局唯一的文件管理器。"}, "fileManager.appendFile": {"prefix": "fileManager.appendFile", "body": ["fileManager.appendFile({", "\tfilePath: '${1}',", "\tdata: '${2}',", "\tencoding:'${3:utf8}',", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "在文件结尾追加内容"}, "fileManager.appendFileSync": {"prefix": "fileManager.appendFileSync", "body": ["try{", "\tfileManager.appendFileSync('${1}','${2}','${3:utf8}');", "}catch(e){", "\t${4}", "}"], "description": "在文件结尾追加内容,同步接口"}, "fileManager.access": {"prefix": "fileManager.access", "body": ["fileManager.access({", "\tpath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "判断文件/目录是否存在"}, "fileManager.accessSync": {"prefix": "fileManager.accessSync", "body": ["try{", "\tfileManager.accessSync('${1}');", "}catch(e){", "\t${2}", "}"], "description": "判断文件/目录是否存在,同步接口"}, "fileManager.copyFile": {"prefix": "fileManager.copyFile", "body": ["fileManager.copyFile({", "\tsrcPath: '${1}',", "\tdestPath: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "复制文件"}, "fileManager.copyFileSync": {"prefix": "fileManager.copyFileSync", "body": ["try{", "\tfileManager.copyFileSync('${1}','${2}');", "}catch(e){", "\t${3}", "}"], "description": "复制文件,同步接口"}, "fileManager.getSavedFileList": {"prefix": "fileManager.getSavedFileList", "body": ["fileManager.getSavedFileList({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取该小程序下已保存的本地缓存文件列表"}, "fileManager.getFileInfo": {"prefix": "fileManager.getFileInfo", "body": ["fileManager.getFileInfo({", "\tfilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取该小程序下的 本地临时文件 或 本地缓存文件 信息"}, "fileManager.mkdir": {"prefix": "fileManager.mkdir", "body": ["fileManager.mkdir({", "\tdirPath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "创建目录"}, "fileManager.mkdirSync": {"prefix": "fileManager.mkdirSync", "body": ["try{", "\tfileManager.mkdirSync('${1}');", "}catch(e){", "\t${2}", "}"], "description": "创建目录,同步接口"}, "fileManager.removeSavedFile": {"prefix": "fileManager.removeSavedFile", "body": ["fileManager.removeSavedFile({", "\tfilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "删除该小程序下已保存的本地缓存文件"}, "fileManager.readFile": {"prefix": "fileManager.readFile", "body": ["fileManager.readFile({", "\tfilePath: '${1}',", "\tencoding: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "读取本地文件内容"}, "fileManager.readFileSync": {"prefix": "fileManager.readFileSync", "body": ["try{", "\tlet ${1:fileData} = fileManager.readFileSync('${2}','${3}');", "}catch(e){", "\t${4}", "}"], "description": "读取本地文件内容,同步接口"}, "fileManager.readdir": {"prefix": "fileManager.readdir", "body": ["fileManager.readdir({", "\tdirPath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "读取目录内文件列表"}, "fileManager.readdirSync": {"prefix": "fileManager.readdirSync", "body": ["try{", "\tlet ${1:fileList} = fileManager.readdirSync('${2}');", "}catch(e){", "\t${3}", "}"], "description": "读取目录内文件列表,同步接口"}, "fileManager.rename": {"prefix": "fileManager.rename", "body": ["fileManager.rename({", "\toldPath: '${1}',", "\tnewPath: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "重命名文件，可以把文件从 oldPath 移动到 newPath"}, "fileManager.renameSync": {"prefix": "fileManager.renameSync", "body": ["try{", "\tfileManager.renameSync('${1}','${2}');", "}catch(e){", "\t${3}", "}"], "description": "重命名文件，可以把文件从 oldPath 移动到 newPath,同步接口"}, "fileManager.rmdir": {"prefix": "fileManager.rmdir", "body": ["fileManager.rmdir({", "\tdirPath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "删除目录"}, "fileManager.rmdirSync": {"prefix": "fileManager.rmdirSync", "body": ["try{", "\tfileManager.rmdirSync('${1}');", "}catch(e){", "\t${2}", "}"], "description": "删除目录,同步接口"}, "fileManager.saveFile": {"prefix": "fileManager.saveFile", "body": ["fileManager.saveFile({", "\ttempFilePath: '${1}',", "\tfilePath: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "保存临时文件到本地。此接口会移动临时文件，因此调用成功后，tempFilePath 将不可用"}, "fileManager.saveFileSync": {"prefix": "fileManager.saveFileSync", "body": ["try{", "\tlet ${1:fileNumber} = fileManager.saveFileSync('${2}','${3}');", "}catch(e){", "\t${4}", "}"], "description": "保存临时文件到本地。此接口会移动临时文件，因此调用成功后，tempFilePath 将不可用,同步接口"}, "fileManager.stat": {"prefix": "fileManager.stat", "body": ["fileManager.stat({", "\tpath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取文件 Stats 对象"}, "fileManager.statSync": {"prefix": "fileManager.statSync", "body": ["try{", "\tlet ${1:fileStat} = fileManager.statSync('${2}');", "}catch(e){", "\t${3}", "}"], "description": "获取文件 Stats 对象,同步接口"}, "fileStat.isDirectory": {"prefix": "fileStat.isDirectory", "body": ["fileStat.isDirectory();"], "description": "判断当前文件是否一个目录"}, "fileStat.isFile": {"prefix": "fileStat.isFile", "body": ["fileStat.isFile();"], "description": "判断当前文件是否一个普通文件"}, "fileManager.unlink": {"prefix": "fileManager.unlink", "body": ["fileManager.unlink({", "\tfilePath: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "删除文件"}, "fileManager.unlinkSync": {"prefix": "fileManager.unlinkSync", "body": ["try{", "\tfileManager.unlinkSync('${1}');", "}catch(e){", "\t${2}", "}"], "description": "删除文件,同步接口"}, "fileManager.unzip": {"prefix": "fileManager.unzip", "body": ["fileManager.unzip({", "\tzipFilePath: '${1}',", "\ttargetPath: '${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "解压文件"}, "fileManager.writeFile": {"prefix": "fileManager.writeFile", "body": ["fileManager.writeFile({", "\tfilePath: '${1}',", "\tdata: '${2}',", "\tencoding: '${3:utf8}',", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "写文件"}, "fileManager.writeFileSync": {"prefix": "fileManager.writeFileSync", "body": ["try{", "\tfileManager.writeFileSync('${1}','${2}','${3:utf8}');", "}catch(e){", "\t${4}", "}"], "description": "写文件,同步接口"}, "Taro.getFileInfo": {"prefix": "Taro.getFileInfo", "body": ["Taro.getFileInfo({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取该小程序下的 本地临时文件 或 本地缓存文件 信息"}, "Taro.pluginLogin": {"prefix": "Taro.pluginLogin", "body": ["Taro.pluginLogin({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "该接口仅在小程序插件中可调用，调用接口获得插件用户标志凭证（code）。插件可以此凭证换取用于识别用户的标识 openpid。用户不同、宿主小程序不同或插件不同的情况下，该标识均不相同，即当且仅当同一个用户在同一个宿主小程序中使用同一个插件时，openpid 才会相同"}, "Taro.login": {"prefix": "Taro.login", "body": ["Taro.login({", "\ttimeout:${1:10000},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "调用接口Taro.login() 获取临时登录凭证（code）"}, "Taro.checkSession": {"prefix": "Taro.checkSession", "body": ["Taro.checkSession({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "校验用户当前session_key是否有效。"}, "Taro.getAccountInfoSync": {"prefix": "Taro.getAccountInfoSync", "body": ["const accountInfo = Taro.getAccountInfoSync();", "${1}"], "description": "获取当前账号信息"}, "Taro.getUserProfile": {"prefix": "Taro.getUserProfile", "body": ["Taro.getUserProfile({", "\tdesc: '用于完善会员资料',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取用户信息。每次请求都会弹出授权窗口，用户同意后返回 userInfo。"}, "Taro.getUserInfo": {"prefix": "Taro.getUserInfo", "body": ["Taro.getUserInfo({", "\twithCredentials: '${1:false}',", "\tlang: '${2:zh_CN}',", "\ttimeout:${3:10000},", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "当用户未授权过，调用该接口将直接报错；当用户授权过，可以使用该接口获取用户信息"}, "Taro.authorizeForMiniProgram": {"prefix": "Taro.authorizeForMiniProgram", "body": ["Taro.authorizeForMiniProgram({", "\tscope: 'scope.record',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t\tTaro.startRecord()", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "仅小程序插件中能调用该接口，用法同 Taro.authorize。目前仅支持三种 scope"}, "Taro.authorize": {"prefix": "Taro.authorize", "body": ["Taro.authorize({", "\tscope: '${1}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "提前向用户发起授权请求。调用后会立刻弹窗询问用户是否同意授权小程序使用某项功能或获取用户的某些数据，但不会实际调用对应接口。如果用户之前已经同意授权，则不会出现弹窗，直接返回成功。"}, "Taro.openSetting": {"prefix": "Taro.openSetting", "body": ["Taro.openSetting({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "调起客户端小程序设置界面，返回用户设置的操作结果"}, "Taro.getSetting": {"prefix": "Taro.getSetting", "body": ["Taro.getSetting({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取用户的当前设置"}, "Taro.chooseAddress": {"prefix": "Taro.chooseAddress", "body": ["Taro.chooseAddress({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "调起用户编辑收货地址原生界面，并在编辑完成后返回用户选择的地址。"}, "Taro.openCard": {"prefix": "Taro.openCard", "body": ["Taro.openCard({", "\tcardList: [${1}],", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "查看微信卡包中的卡券"}, "Taro.addCard": {"prefix": "Taro.addCard", "body": ["Taro.addCard({", "\tcardList: [${1}],", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "批量添加卡券"}, "Taro.chooseInvoiceTitle": {"prefix": "Taro.chooseInvoiceTitle", "body": ["Taro.chooseInvoiceTitle({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "选择用户的发票抬头"}, "Taro.chooseInvoice": {"prefix": "Taro.chooseInvoice", "body": ["Taro.chooseInvoice({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "选择用户已有的发票"}, "Taro.startSoterAuthentication": {"prefix": "Taro.startSoterAuthentication", "body": ["Taro.startSoterAuthentication({", "\trequestAuthModes:['fingerPrint'],", "\tchallenge:'${1}',", "\tauthContent:'${2}',", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始SOTER生物认证"}, "Taro.checkIsSupportSoterAuthentication": {"prefix": "Taro.checkIsSupportSoterAuthentication", "body": ["Taro.checkIsSupportSoterAuthentication({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取本机支持的 SOTER 生物认证方式"}, "Taro.checkIsSoterEnrolledInDevice": {"prefix": "Taro.checkIsSoterEnrolledInDevice", "body": ["Taro.checkIsSoterEnrolledInDevice({", "\tcheckAuthMode:{${1}},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取设备内是否录入如指纹等生物信息的接口"}, "Taro.shareToWeRun": {"prefix": "Taro.shareToWeRun", "body": ["Taro.shareToWeRun({", "\trecordList:[],", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "分享数据到微信运动。"}, "Taro.getWeRunData": {"prefix": "Taro.getWeRunData", "body": ["Taro.getWeRunData({", "\ttimeout:${1:10000},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取用户过去三十天微信运动步数，需要先调用 Taro.login 接口"}, "Taro.requestSubscribeMessage": {"prefix": "Taro.requestSubscribeMessage", "body": ["Taro.requestSubscribeMessage({", "\ttmplIds: [''],", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "请求订阅消息"}, "Taro.requestSubscribeDeviceMessage": {"prefix": "Taro.requestSubscribeDeviceMessage", "body": ["Taro.requestSubscribeDeviceMessage({", "\ttmplIds: ['xxxxx'],", "\tsn: 'xxxx',", "\tsnTicket: 'xxxxx',", "\tmodelId: 'xxx',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "订阅设备消息接口，调用后弹出授权框，用户同意后会允许开发者给用户发送订阅模版消息。当用户点击“允许”按钮时，模板消息会被添加到用户的小程序设置页，通过 wx.getSetting 接口可获取用户对相关模板消息的订阅状态。"}, "Taro.showRedPackage": {"prefix": "Taro.showRedPackage", "body": ["Taro.showRedPackage({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "拉取h5领取红包封面页。获取参考红包封面地址参考 微信红包封面开发平台。"}, "Taro.addVideoToFavorites": {"prefix": "Taro.addVideoToFavorites", "body": ["Taro.downloadFile({", "  url: ${1:URL}, // 下载url", "  success (res) {", "    // 下载完成后收藏", "    Taro.addVideoToFavorites({", "      videoPath: res.tempFilePath,", "      success() {},", "      fail: console.error,", "    })", "  },", "  fail: console.error,", "})"], "description": "收藏视频"}, "Taro.addFileToFavorites": {"prefix": "Taro.addFileToFavorites", "body": ["Taro.downloadFile({", "  url: ${1:URL}, // 下载url", "  success (res) {", "    // 下载完成后收藏", "    Taro.addFileToFavorites({", "      videoPath: res.tempFilePath,", "      success() {},", "      fail: console.error,", "    })", "  },", "  fail: console.error,", "})"], "description": "收藏文件"}, "Taro.checkIsAddedToMyMiniProgram": {"prefix": "Taro.checkIsAddedToMyMiniProgram", "body": ["Taro.checkIsAddedToMyMiniProgram({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "检查小程序是否被添加至 「我的小程序」"}, "Taro.chooseLicensePlate": {"prefix": "Taro.chooseLicensePlate", "body": ["Taro.chooseLicensePlate({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "选择车牌号"}, "Taro.stopBluetoothDevicesDiscovery": {"prefix": "Taro.stopBluetoothDevicesDiscovery", "body": ["Taro.stopBluetoothDevicesDiscovery({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止搜寻附近的蓝牙外围设备。若已经找到需要的蓝牙设备并不需要继续搜索时，建议调用该接口停止蓝牙搜索。"}, "Taro.startBluetoothDevicesDiscovery": {"prefix": "Taro.startBluetoothDevicesDiscovery", "body": ["Taro.startBluetoothDevicesDiscovery({", "\tservices:[${1}]", "\tallowDuplicatesKey:${2:false}", "\tinterval:${3:0}", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始搜寻附近的蓝牙外围设备。注意，该操作比较耗费系统资源，请在搜索并连接到设备后调用 stop 方法停止搜索。"}, "Taro.openBluetoothAdapter": {"prefix": "Taro.openBluetoothAdapter", "body": ["Taro.openBluetoothAdapter({", "\tsuccess: (result)=>{", "\t\t${1}", "\t,", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "初始化小程序蓝牙模块，生效周期为调用Taro.openBluetoothAdapter至调用Taro.closeBluetoothAdapter或小程序被销毁为止。"}, "Taro.onBluetoothDeviceFound": {"prefix": "Taro.onBluetoothDeviceFound", "body": ["Taro.onBluetoothDeviceFound((result) => {", "\t${1}", "});"], "description": "监听寻找到新设备的事件"}, "Taro.onBluetoothAdapterStateChange": {"prefix": "Taro.onBluetoothAdapterStateChange", "body": ["Taro.onBluetoothAdapterStateChange((result) => {", "\t${1}", "});"], "description": "监听蓝牙适配器状态变化事件"}, "Taro.offBluetoothDeviceFound": {"prefix": "Taro.offBluetoothDeviceFound", "body": ["Taro.offBluetoothDeviceFound((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听寻找到新设备的事件"}, "Taro.offBluetoothAdapterStateChange": {"prefix": "Taro.offBluetoothAdapterStateChange", "body": ["Taro.offBluetoothAdapterStateChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听蓝牙适配器状态变化事件"}, "Taro.makeBluetoothPair": {"prefix": "Taro.makeBluetoothPair", "body": ["Taro.makeBluetoothPair({", "\tdeviceId: '',", "\tpin: '',", "\ttimeout: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "蓝牙配对接口，仅安卓支持"}, "Taro.isBluetoothDevicePaired": {"prefix": "Taro.isBluetoothDevicePaired", "body": ["Taro.isBluetoothDevicePaired({", "\tdeviceId: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "查询蓝牙设备是否配对，仅安卓支持"}, "Taro.getConnectedBluetoothDevices": {"prefix": "Taro.getConnectedBluetoothDevices", "body": ["Taro.getConnectedBluetoothDevices({", "\tservices: [${1}],", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "根据 uuid 获取处于已连接状态的设备"}, "Taro.getBluetoothDevices": {"prefix": "Taro.getBluetoothDevices", "body": ["Taro.getBluetoothDevices({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取在小程序蓝牙模块生效期间所有已发现的蓝牙设备，包括已经和本机处于连接状态的设备。"}, "Taro.getBluetoothAdapterState": {"prefix": "Taro.getBluetoothAdapterState", "body": ["Taro.getBluetoothAdapterState({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取本机蓝牙适配器状态"}, "Taro.closeBluetoothAdapter": {"prefix": "Taro.closeBluetoothAdapter", "body": ["Taro.closeBluetoothAdapter({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭蓝牙模块，使其进入未初始化状态。调用该方法将断开所有已建立的链接并释放系统资源。"}, "Taro.createBLEConnection": {"prefix": "Taro.createBLEConnection", "body": ["Taro.createBLEConnection({", "\tdeviceId: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "连接低功耗蓝牙设备。"}, "Taro.closeBLEConnection": {"prefix": "Taro.closeBLEConnection", "body": ["Taro.closeBLEConnection({", "\tdeviceId: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "断开与低功耗蓝牙设备的连接"}, "Taro.onBLEConnectionStateChange": {"prefix": "Taro.onBLEConnectionStateChange", "body": ["Taro.onBLEConnectionStateChange((result) => {", "\t${1}", "});"], "description": "监听低功耗蓝牙连接状态的改变事件，包括开发者主动连接或断开连接，设备丢失，连接异常断开等等"}, "Taro.getBLEDeviceServices": {"prefix": "Taro.getBLEDeviceServices", "body": ["Taro.getBLEDeviceServices({", "\tdeviceId: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取蓝牙设备所有 service（服务）"}, "Taro.getBLEDeviceCharacteristics": {"prefix": "Taro.getBLEDeviceCharacteristics", "body": ["Taro.getBLEDeviceCharacteristics({", "\tdeviceId: ${1},", "\tservices: ${2},", "\tsuccess: (result)=>{", "\t\t${3}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取蓝牙设备某个服务中的所有 characteristic（特征值）"}, "Taro.readBLECharacteristicValue": {"prefix": "Taro.readBLECharacteristicValue", "body": ["Taro.readBLECharacteristicValue({", "\tdeviceId: ${1},", "\tservices: ${2},", "\tcharacteristicId: ${3},", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "读取低功耗蓝牙设备的特征值的二进制数据值。注意：必须设备的特征值支持read才可以成功调用，具体参照 characteristic 的 properties 属性"}, "Taro.writeBLECharacteristicValue": {"prefix": "Taro.writeBLECharacteristicValue", "body": ["Taro.writeBLECharacteristicValue({", "\tdeviceId: ${1},", "\tservices: ${2},", "\tcharacteristicId: ${3},", "\tvalue: ${4:<ArrayBuffer类型>},", "\tsuccess: (result)=>{", "\t\t${5}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "向低功耗蓝牙设备特征值中写入二进制数据。注意：必须设备的特征值支持write才可以成功调用，具体参照 characteristic 的 properties 属性"}, "Taro.notifyBLECharacteristicValueChange": {"prefix": "Taro.notifyBLECharacteristicValueChange", "body": ["Taro.notifyBLECharacteristicValueChange({", "\tdeviceId: ${1},", "\tservices: ${2},", "\tcharacteristicId: ${3},", "\tstate: ${4:true},", "\tvalue: ${5:<ArrayBuffer类型>},", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "启用低功耗蓝牙设备特征值变化时的 notify 功能，订阅特征值。注意：必须设备的特征值支持notify或者indicate才可以成功调用，具体参照 characteristic 的 properties 属性"}, "Taro.onBLECharacteristicValueChange": {"prefix": "Taro.onBLECharacteristicValueChange", "body": ["Taro.onBLECharacteristicValueChange((result) => {", "\t${1}", "});"], "description": "监听低功耗蓝牙设备的特征值变化。必须先启用notify接口才能接收到设备推送的notification。"}, "Taro.stopBeaconDiscovery": {"prefix": "Taro.stopBeaconDiscovery", "body": ["Taro.stopBeaconDiscovery({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止搜索附近的iBeacon设备"}, "Taro.startBeaconDiscovery": {"prefix": "Taro.startBeaconDiscovery", "body": ["Taro.startBeaconDiscovery({", "\tuuids: [${1}],", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始搜索附近的iBeacon设备"}, "Taro.onBeaconUpdate": {"prefix": "Taro.onBeaconUpdate", "body": ["Taro.onBeaconUpdate((result) => {", "\t${1}", "});"], "description": "监听 iBeacon 设备的更新事件"}, "Taro.onBeaconServiceChange": {"prefix": "Taro.onBeaconServiceChange", "body": ["Taro.onBeaconServiceChange((result) => {", "\t${1}", "});"], "description": "监听 iBeacon 服务的状态变化"}, "Taro.offBeaconUpdate": {"prefix": "Taro.offBeaconUpdate", "body": ["Taro.offBeaconUpdate((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听 iBeacon 设备更新事件"}, "Taro.offBeaconServiceChange": {"prefix": "Taro.offBeaconServiceChange", "body": ["Taro.offBeaconServiceChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听 iBeacon 服务状态变化事件"}, "Taro.getBeacons": {"prefix": "Taro.getBeacons", "body": ["Taro.getBeacons({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取所有已搜索到的iBeacon设备"}, "Taro.stopHCE": {"prefix": "Taro.stopHCE", "body": ["Taro.stopHCE({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭 NFC 模块。仅在安卓系统下有效。"}, "Taro.startHCE": {"prefix": "Taro.startHCE", "body": ["Taro.startHCE({", "\taid_list: [${1}],", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "初始化NFC模块"}, "Taro.sendHCEMessage": {"prefix": "Taro.sendHCEMessage", "body": ["Taro.sendHCEMessage({", "\tdata: ${1:<ArrayBuffer类型>},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "发送 NFC 消息。仅在安卓系统下有效。"}, "Taro.onHCEMessage": {"prefix": "Taro.onHCEMessage", "body": ["Taro.onHCEMessage((result) => {", "\t${1}", "});"], "description": "监听 NFC 设备的消息回调，并在回调中处理。"}, "Taro.offHCEMessage": {"prefix": "Taro.offHCEMessage", "body": ["Taro.offHCEMessage((res) => {", "\tconsole.log(res)", "})"], "description": "接收 NFC 设备消息事件，取消事件监听。"}, "Taro.getNFCAdapter": {"prefix": "Taro.getNFCAdapter", "body": ["const res = Taro.getNFCAdapter();"], "description": "获取 NFC 实例"}, "Taro.getHCEState": {"prefix": "Taro.getHCEState", "body": ["Taro.getHCEState({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "判断当前设备是否支持 HCE 能力"}, "Taro.stopWifi": {"prefix": "Taro.stopWifi", "body": ["Taro.stopWifi({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "关闭Wi-Fi模块。"}, "Taro.startWifi": {"prefix": "Taro.startWifi", "body": ["Taro.startWifi({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "初始化Wi-Fi模块。"}, "Taro.setWifiList": {"prefix": "Taro.setWifiList", "body": ["Taro.setWifiList({", "\twifiList: [", "\t\t{", "\t\t\tSSID: ${1},", "\t\t\tBSSID: ${2},", "\t\t\tpassword: ${3}", "\t\t}", "\t],", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "iOS特有接口，在 onGetWifiList 回调后，利用接口设置 wifiList 中 AP 的相关信息。"}, "Taro.onWifiConnectedWithPartialInfo": {"prefix": "Taro.onWifiConnectedWithPartialInfo", "body": ["Taro.onWifiConnectedWithPartialInfo((res) => {", "\tconsole.log(res)", "})"], "description": "监听连接上 Wi-Fi 的事件"}, "Taro.onWifiConnected": {"prefix": "Taro.onWifiConnected", "body": ["Taro.onWifiConnected((res) => {", "\tconsole.log(res)", "})"], "description": "监听连接上 Wi-Fi 的事件。"}, "Taro.onGetWifiList": {"prefix": "Taro.onGetWifiList", "body": ["Taro.onGetWifiList((result) => {", "\t${1}", "});"], "description": "监听在获取到Wi-Fi列表数据时的事件，在回调中将返回wifiList。"}, "Taro.offWifiConnectedWithPartialInfo": {"prefix": "Taro.offWifiConnectedWithPartialInfo", "body": ["Taro.offWifiConnectedWithPartialInfo((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听连接上 Wi-Fi 的事件"}, "Taro.offWifiConnected": {"prefix": "Taro.offWifiConnected", "body": ["Taro.offWifiConnected((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听连接上 Wi-Fi 的事件。"}, "Taro.offGetWifiList": {"prefix": "Taro.offGetWifiList", "body": ["Taro.offGetWifiList((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听获取到 Wi-Fi 列表数据事件。"}, "Taro.getWifiList": {"prefix": "Taro.getWifiList", "body": ["Taro.getWifiList({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "请求获取Wi-Fi列表，在onGetWifiList注册的回调中返回wifiList数据。"}, "Taro.getConnectedWifi": {"prefix": "Taro.getConnectedWifi", "body": ["Taro.getConnectedWifi({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取已连接中的Wi-Fi信息"}, "Taro.connectWifi": {"prefix": "Taro.connectWifi", "body": ["Taro.connectWifi({", "\tSSID: ${1},", "\tBSSID: ${2},", "\tpassword: ${3},", "\tsuccess: (result)=>{", "\t\t${4}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "连接Wi-Fi。若已知Wi-Fi信息，可以直接利用该接口连接。"}, "Taro.addPhoneRepeatCalendar": {"prefix": "Taro.addPhoneRepeatCalendar", "body": ["Taro.addPhoneRepeatCalendar({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "向系统日历添加重复事件"}, "Taro.addPhoneCalendar": {"prefix": "Taro.addPhoneCalendar", "body": ["Taro.addPhoneCalendar({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "向系统日历添加事件"}, "Taro.chooseContact": {"prefix": "Taro.chooseContact", "body": ["Taro.chooseContact({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "添加手机通讯录联系人。用户可以选择将该表单以「新增联系人」或「添加到已有联系人」的方式，写入手机系统通讯录。"}, "Taro.addPhoneContact": {"prefix": "Taro.addPhoneContact", "body": ["Taro.addPhoneContact({", "\tphotoFilePath: ${1},", "\tnickName: ${2},", "\tlastName: ${3},", "\tmiddleName: ${4},", "\tfirstName: ${5},", "\tmobilePhoneNumber: ${6},", "\tweChatNumber: ${7},", "\temail: ${8},", "\tsuccess: (result)=>{", "\t\t${9}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "调用后，用户可以选择将该表单以“新增联系人”或“添加到已有联系人”的方式，写入手机系统通讯录，完成手机通讯录联系人和联系方式的增加。此API参数非常多，请参考文档。"}, "Taro.checkIsOpenAccessibility": {"prefix": "Taro.checkIsOpenAccessibility", "body": ["Taro.checkIsOpenAccessibility({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "检测是否开启视觉无障碍功能。"}, "Taro.getBatteryInfoSync": {"prefix": "Taro.getBatteryInfoSync", "body": ["const res = Taro.getBatteryInfoSync();"], "description": "Taro.getBatteryInfo 的同步版本"}, "Taro.getBatteryInfo": {"prefix": "Taro.getBatteryInfo", "body": ["Taro.getBatteryInfo({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取设备电量。同步 API Taro.getBatteryInfoSync 在 iOS 上不可用。"}, "Taro.setClipboardData": {"prefix": "Taro.setClipboardData", "body": ["Taro.setClipboardData({", "\tdata: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "设置系统剪贴板的内容"}, "Taro.getClipboardData": {"prefix": "Taro.getClipboardData", "body": ["Taro.getClipboardData({", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取系统剪贴板内容"}, "Taro.onNetworkWeakChange": {"prefix": "Taro.onNetworkWeakChange", "body": ["Taro.onNetworkWeakChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听弱网状态变化事件"}, "Taro.onNetworkStatusChange": {"prefix": "Taro.onNetworkStatusChange", "body": ["Taro.onNetworkStatusChange((result) => {", "\t${1}", "});"], "description": "监听网络状态变化。"}, "Taro.offNetworkWeakChange": {"prefix": "Taro.offNetworkWeakChange", "body": ["Taro.offNetworkWeakChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听弱网状态变化事件"}, "Taro.offNetworkStatusChange": {"prefix": "Taro.offNetworkStatusChange", "body": ["Taro.offNetworkStatusChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听网络状态变化事件，参数为空，则取消所有的事件监听。"}, "Taro.getNetworkType": {"prefix": "Taro.getNetworkType", "body": ["Taro.getNetworkType({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取网络类型"}, "Taro.getLocalIPAddress": {"prefix": "Taro.getLocalIPAddress", "body": ["Taro.getLocalIPAddress({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取局域网IP地址。"}, "Taro.setVisualEffectOnCapture": {"prefix": "Taro.setVisualEffectOnCapture", "body": ["Taro.setVisualEffectOnCapture({", "\tvisualEffect: '',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": ""}, "Taro.setScreenBrightness": {"prefix": "Taro.setScreenBrightness", "body": ["Taro.setScreenBrightness({", "\tvalue: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "设置屏幕亮度"}, "Taro.setKeepScreenOn": {"prefix": "Taro.setKeepScreenOn", "body": ["Taro.setKeepScreenOn({", "\tkeepScreenOn: ${1:true},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "设置是否保持常亮状态。仅在当前小程序生效，离开小程序后设置失效。"}, "Taro.onUserCaptureScreen": {"prefix": "Taro.onUserCaptureScreen", "body": ["Taro.onUserCaptureScreen((result)=>{", "\t${1}", "});"], "description": "监听用户主动截屏事件，用户使用系统截屏按键截屏时触发此事件"}, "Taro.onScreenRecordingStateChanged": {"prefix": "Taro.onScreenRecordingStateChanged", "body": ["Taro.onScreenRecordingStateChanged((res) => {", "\tconsole.log(res)", "})"], "description": "监听用户录屏事件"}, "Taro.offUserCaptureScreen": {"prefix": "Taro.offUserCaptureScreen", "body": ["Taro.offUserCaptureScreen((res) => {", "\tconsole.log(res)", "})"], "description": "用户主动截屏事件。取消事件监听。"}, "Taro.offScreenRecordingStateChanged": {"prefix": "Taro.offScreenRecordingStateChanged", "body": ["Taro.offScreenRecordingStateChanged((res) => {", "\tconsole.log(res)", "})"], "description": "取消用户录屏事件的监听函数"}, "Taro.getScreenRecordingState": {"prefix": "Taro.getScreenRecordingState", "body": ["Taro.getScreenRecordingState({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "查询用户是否在录屏"}, "Taro.getScreenBrightness": {"prefix": "Taro.getScreenBrightness", "body": ["Taro.getScreenBrightness({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取屏幕亮度。"}, "Taro.onKeyboardHeightChange": {"prefix": "Taro.onKeyboardHeightChange", "body": ["Taro.onKeyboardHeightChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听键盘高度变化"}, "Taro.offKeyboardHeightChange": {"prefix": "Taro.offKeyboardHeightChange", "body": ["Taro.offKeyboardHeightChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听键盘高度变化事件。"}, "Taro.hideKeyboard": {"prefix": "Taro.hideKeyboard", "body": ["Taro.hideKeyboard({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "在input、textarea等focus拉起键盘之后，手动调用此接口收起键盘"}, "Taro.getSelectedTextRange": {"prefix": "Taro.getSelectedTextRange", "body": ["Taro.getSelectedTextRange({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "在input、textarea等focus之后，获取输入框的光标位置。注意：只有在focus的时候调用此接口才有效。"}, "Taro.makePhoneCall": {"prefix": "Taro.makePhoneCall", "body": ["Taro.makePhoneCall({", "\tphoneNumber: ${1},", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "拨打电话"}, "Taro.stopAccelerometer": {"prefix": "Taro.stopAccelerometer", "body": ["Taro.stopAccelerometer({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止监听加速度数据。"}, "Taro.startAccelerometer": {"prefix": "Taro.startAccelerometer", "body": ["Taro.startAccelerometer({", "\tinterval: '${1:normal}',", "\tsuccess: (result)=>{", "\t\t${2}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始监听加速度数据。"}, "Taro.onAccelerometerChange": {"prefix": "Taro.onAccelerometerChange", "body": ["Taro.onAccelerometerChange((result) => {", "\t${1}", "});"], "description": "监听加速度数据，频率：5次/秒，接口调用后会自动开始监听，可使用 Taro.stopAccelerometer 停止监听。"}, "Taro.offAccelerometerChange": {"prefix": "Taro.offAccelerometerChange", "body": ["Taro.offAccelerometerChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听加速度数据事件，参数为空，则取消所有的事件监听。"}, "Taro.stopCompass": {"prefix": "Taro.stopCompass", "body": ["Taro.stopCompass({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "停止监听罗盘数据。"}, "Taro.startCompass": {"prefix": "Taro.startCompass", "body": ["Taro.startCompass({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "开始监听罗盘数据。"}, "Taro.onCompassChange": {"prefix": "Taro.onCompassChange", "body": ["Taro.onCompassChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听罗盘数据变化事件。频率：5 次/秒，接口调用后会自动开始监听，可使用 Taro.stopCompass 停止监听。"}, "Taro.offCompassChange": {"prefix": "Taro.offCompassChange", "body": ["Taro.offCompassChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听罗盘数据变化事件，参数为空，则取消所有的事件监听。"}, "Taro.stopDeviceMotionListening": {"prefix": "Taro.stopDeviceMotionListening", "body": ["Taro.stopDeviceMotionListening({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "停止监听设备方向的变化。"}, "Taro.startDeviceMotionListening": {"prefix": "Taro.startDeviceMotionListening", "body": ["Taro.startDeviceMotionListening({", "\tinterval: 'normal',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "开始监听设备方向的变化。"}, "Taro.onDeviceMotionChange": {"prefix": "Taro.onDeviceMotionChange", "body": ["Taro.onDeviceMotionChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听设备方向变化事件。频率根据 Taro.startDeviceMotionListening() 的 interval 参数。可以使用 Taro.stopDeviceMotionListening() 停止监听。"}, "Taro.offDeviceMotionChange": {"prefix": "Taro.offDeviceMotionChange", "body": ["Taro.offDeviceMotionChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听设备方向变化事件，参数为空，则取消所有的事件监听。"}, "Taro.stopGyroscope": {"prefix": "Taro.stopGyroscope", "body": ["Taro.stopGyroscope({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "停止监听陀螺仪数据。"}, "Taro.startGyroscope": {"prefix": "Taro.startGyroscope", "body": ["Taro.startGyroscope({", "\tinterval: 'normal',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "开始监听陀螺仪数据。"}, "Taro.onGyroscopeChange": {"prefix": "Taro.onGyroscopeChange", "body": ["Taro.onGyroscopeChange((res) => {", "\tconsole.log(res)", "})"], "description": "监听陀螺仪数据变化事件。频率根据 Taro.startGyroscope() 的 interval 参数。可以使用 Taro.stopGyroscope() 停止监听。"}, "Taro.offGyroscopeChange": {"prefix": "Taro.offGyroscopeChange", "body": ["Taro.offGyroscopeChange((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听陀螺仪数据变化事件。"}, "Taro.onMemoryWarning": {"prefix": "Taro.onMemoryWarning", "body": ["Taro.onMemoryWarning((result) => {", "\t${1}", "});"], "description": "监听内存不足的告警事件，Android下有告警等级划分，只有LOW和CRITICAL会回调开发者；iOS无等级划分"}, "Taro.offMemoryWarning": {"prefix": "Taro.offMemoryWarning", "body": ["Taro.offMemoryWarning((res) => {", "\tconsole.log(res)", "})"], "description": "取消监听内存不足告警事件。"}, "Taro.scanCode": {"prefix": "Taro.scanCode", "body": ["Taro.scanCode({", "\tonlyFromCamera: ${1:false},", "\tscanType: ['${2:qrCode}','${3:barCode}','${4:datamatrix}','${5:pdf417}'],", "\tsuccess: (result)=>{", "\t\t${6}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "调起客户端扫码界面，扫码成功后返回对应的结果"}, "Taro.sendSms": {"prefix": "Taro.sendSms", "body": ["Taro.sendSms({", "\tphoneNumber: '${1}',", "\tcontent: '${2}',", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "拉起手机发送短信界面"}, "Taro.vibrateShort": {"prefix": "Taro.vibrateShort", "body": ["Taro.vibrateShort({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "使手机发生较短时间的振动（15ms）"}, "Taro.vibrateLong": {"prefix": "Taro.vibrateLong", "body": ["Taro.vibrateLong({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "使手机发生较长时间的振动（400ms）"}, "Taro.getInferenceEnvInfo": {"prefix": "Taro.getInferenceEnvInfo", "body": ["Taro.getInferenceEnvInfo({", "\tsuccess (res) {", "\t\tconsole.log(res)", "\t},", "\tfail (error) {", "\t\tconsole.error(error)", "\t}", "})"], "description": "获取通用AI推理引擎版本"}, "Taro.createInferenceSession": {"prefix": "Taro.createInferenceSession", "body": ["const inferenceSession = Taro.createInferenceSession({", "\t${1}", "});"], "description": "创建 AI 推理 Session"}, "Taro.createWorker": {"prefix": "Taro.createWorker", "body": ["const worker = Taro.createWorker('workers/request/index.js') // 文件名指定 worker 的入口文件路径，绝对路径", "worker.onMessage(function (res) {", "\tconsole.log(res)", "})", "worker.postMessage({", "\tmsg: 'hello worker'", "})", "worker.terminate()"], "description": "创建一个Worker线程，并返回Worker实例，目前限制最多只能创建一个Worker，创建下一个Worker前请调用Worker.terminate。"}, "worker.postMessage": {"prefix": "worker.postMessage", "body": ["worker.postMessage(${1});"], "description": "向Worker线程发送的消息。"}, "worker.onMessage": {"prefix": "worker.onMessage", "body": ["worker.onMessage((result) => {${1}});"], "description": "监听Worker线程向当前线程发送的消息"}, "worker.terminate": {"prefix": "worker.terminate", "body": ["worker.terminate();"], "description": "结束当前Worker线程，仅限在主线程Worker实例上调用。"}, "Taro.createSelectorQuery": {"prefix": "Taro.createSelectorQuery", "body": ["const ${1:selectorQuery} = Taro.createSelectorQuery();"], "description": "返回一个 SelectorQuery 对象实例。"}, "selectorQuery.in": {"prefix": "selectorQuery.in", "body": ["selectorQuery.in(${1:this});"], "description": "将选择器的选取范围更改为自定义组件 component 内（初始时，选择器仅选取页面范围的节点，不会选取任何自定义组件中的节点）。"}, "selectorQuery.select": {"prefix": "selectorQuery.select", "body": ["selectorQuery.select(${2:selector});"], "description": "在当前页面下选择第一个匹配选择器 selector 的节点，返回一个 NodesRef 对象实例，可以用于获取节点信息"}, "selectorQuery.selectAll": {"prefix": "selectorQuery.selectAll", "body": ["selectorQuery.selectAll();"], "description": "在当前页面下选择匹配选择器 selector 的所有节点。"}, "selectorQuery.selectViewport": {"prefix": "selectorQuery.selectViewport", "body": ["selectorQuery.selectViewport();"], "description": "选择显示区域，可用于获取显示区域的尺寸、滚动位置等信息。"}, "selectorQuery.exec": {"prefix": "selectorQuery.exec", "body": ["selectorQuery.exec(result)=>{", "\t${1}", "});"], "description": "执行所有的请求，请求结果按请求次序构成数组，在callback的第一个参数中返回。"}, "Taro.createIntersectionObserver": {"prefix": "Taro.createIntersectionObserver", "body": ["const ${1:intersectionObserver} = Taro.createIntersectionObserver(${2:this},${3});"], "description": "创建并返回一个 IntersectionObserver 对象实例"}, "intersectionObserver.relativeTo": {"prefix": "intersectionObserver.relativeTo", "body": ["intersectionObserver.relativeTo(${1:selector},${2});"], "description": "使用选择器指定一个节点，作为参照区域之一"}, "intersectionObserver.relativeToViewport": {"prefix": "intersectionObserver.relativeToViewport", "body": ["intersectionObserver.relativeToViewport(${1});"], "description": "指定页面显示区域作为参照区域之一"}, "intersectionObserver.observe": {"prefix": "intersectionObserver.observe", "body": ["intersectionObserver.observe(${1:selector},(result)=>{", "\t${2}", "});"], "description": "指定目标节点并开始监听相交状态变化情况"}, "intersectionObserver.disconnect": {"prefix": "intersectionObserver.disconnect", "body": ["intersectionObserver.disconnect();"], "description": "停止监听。回调函数将不再触发"}, "Taro.createMediaQueryObserver": {"prefix": "Taro.createMediaQueryObserver", "body": ["let createMediaQueryObserver", "if (process.env.TARO_ENV === 'weapp') {", "\t// 小程序没有组件实例，只能获取Page级组件实例", "\tcreateMediaQueryObserver = Taro.getCurrentInstance().page.createMediaQueryObserver", "} else if (process.env.TARO_ENV === 'h5') {", "\tcreateMediaQueryObserver= Taro.createMediaQueryObserver", "}", "const mediaQueryObserver = createMediaQueryObserver()"], "description": "创建并返回一个 MediaQueryObserver 对象实例。在自定义组件或包含自定义组件的页面中，应使用 this.createMediaQueryObserver() 来代替。"}, "Taro.getExtConfigSync": {"prefix": "Taro.getExtConfigSync", "body": ["${1:const extConfig = }Taro.getExtConfigSync();"], "description": "同步获取第三方平台自定义的数据字段"}, "Taro.getExtConfig": {"prefix": "Taro.getExtConfig", "body": ["Taro.getExtConfig({", "\tsuccess: (result)=>{", "\t\t${1}", "\t},", "\tfail: ()=>{},", "\tcomplete: ()=>{}", "});"], "description": "获取第三方平台自定义的数据字段"}}