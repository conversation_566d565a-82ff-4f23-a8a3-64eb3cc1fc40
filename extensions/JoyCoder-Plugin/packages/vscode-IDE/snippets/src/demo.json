{"Print to console": {"url": "https://code.visualstudio.com/docs/editor/userdefinedsnippets#_creating-your-own-snippets", "scope": "javascript,typescript", "prefix": "log", "body": ["console.log('$1');", "$2"], "description": "Log output to console"}, "For Loop": {"prefix": ["for", "for-const"], "body": ["for (const ${2:element} of ${1:array}) {", "\t$0", "}"], "description": "A for loop."}, "Taro Hooks": {"scope": "javascript,typescript,javascriptreact,typescriptreact", "prefix": "use", "body": ["${1|useLaunch,useError|}((res) => {", "  console.log('${1}', res)", "})"], "description": "Taro Hooks: https://taro-docs.jd.com/docs/hooks"}, "API snippet 模版1": {"prefix": "aaaaa", "body": ["\"Taro.${1:getEnv}\":{", "\t\"prefix\":\"Taro.${1:getEnv}\",", "\t\"body\":[", "\t\t\"const res = Taro.${1:getEnv}();\",", "\t],", "\t\"description\":\"${2}\"", "},"], "description": ""}, "API snippet 模版2": {"prefix": "bbbbb", "body": ["\"Taro.${1:openSystemBluetoothSetting}\":{", "\t\"prefix\":\"Taro.${1:openSystemBluetoothSetting}\",", "\t\"body\":[", "\t\t\"Taro.${1:openSystemBluetoothSetting}({\",", "\t\t\"\\tsuccess (res) {\",", "\t\t\"\\t\\tconsole.log(res)\",", "\t\t\"\\t},\",", "\t\t\"\\tfail (error) {\",", "\t\t\"\\t\\tconsole.error(error)\",", "\t\t\"\\t}\",", "\t\t\"})\",", "\t],", "\t\"description\":\"${2}\"", "},"], "description": ""}, "API snippet 模版3": {"prefix": "ccccc", "body": ["\"Taro.${1:onUnhandledRejection}\":{", "\t\"prefix\":\"Taro.${1:onUnhandledRejection}\",", "\t\"body\":[", "\t\t\"Taro.${1:onUnhandledRejection}((res) => {\",", "\t\t\"\\tconsole.log(res)\",", "\t\t\"})\",", "\t],", "\t\"description\":\"${2}\"", "},"], "description": ""}}