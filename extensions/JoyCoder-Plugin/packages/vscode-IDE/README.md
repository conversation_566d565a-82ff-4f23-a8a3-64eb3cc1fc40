## JoyCoder

基于大模型的智能化能力，为Visual Studio Code 打造提效插件，进行AI智能化赋能，包含：代码预测、图片转代码 、代码生成、技术问答、代码翻译、用例生成、BUG查找、知识库等一系列功能，帮助研发提高工作效率，让编程变得更轻松。并且不止AI，在常规开发流程上还包含：应用创建、物料组件管理、接口代码自动化生成等功能

- 复制群号，或点此加咚咚群 👉🏻 [10204632088](http://joycoder.jd.com?timeline=1)

### 核心特性

- [🔥 代码预测补全](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/aiCompletions):

- [🤖 AI智能编码](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/ai):

  基于 JoyCoder-Pro等自研模型 的AI技术问答、代码翻译、代码分析、代码生成、代码重构、安全扫描等智能编码辅助工具，支持聊天窗和编辑器的快捷操作

- [🗺 图片生成代码](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/aiapp):

  输入 UI 草图后，GPT可根据指定的 UI 框架输出相应代码

  注释生成代码、单行代码预测、多行代码预测

- [🚀 快速开始创建](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/quickstart):

  解决 CLI 繁琐操作以及配置固化的问题，提供一键创建项目的能力

- [🔧 命令行工厂](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/clifactory):

  脚手架到用户之间的桥梁，摆脱脚手架命令行需要记忆和拼参的烦恼

- [💻 编码辅助](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/codeassist):

  基于语法识别的编码辅助，例如组件导入，CSS 自动补全等

- [🔆 工具辅助](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/tutorial/devtools):

  支持各类工具的集成，例如 H5 调试、代码检查、Coding 跳转等

- [🎉 团队定制化](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/config):

  通过远程配置实现不同团队或项目的个性化功能

#### 官方文档

[官方文档](http://joycoder.jd.com/)

#### 常见问题

[常见问题](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/vscode/faq)

#### 历史版本

[历史版本](http://joycoder.jd.com/jdhopenplatform/JoyCoder/docs/vscode/changelog)
