import * as vscode from 'vscode';
import { FE_FILE_TYPES, getFocusCodeInfo, reportRd } from '@joycoder/shared';

// ip悬浮时弹窗webterminal链接
function ipProvideHover(document, position) {
  const { line } = getFocusCodeInfo(document, position);
  const lineText = line.text || '';

  const ipMatch = lineText.match(/((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/g);
  if (ipMatch) {
    const links = ipMatch.map(
      (ip) => `[${ip}](https://bastion-web.jd.com/mfa.html?ip=${ip}&platform=Linux&accountname=admin&source=noah)`
    );
    const hover = new vscode.Hover(
      new vscode.MarkdownString(`使用Noah WebTerminal登录机器(只支持gamma、idc机器)：\n\n${links.join('\n\n')}`)
    );
    reportRd(4);
    return hover;
  }
}

// npm包快速查看
function npmProvideHover(document, position) {
  const { line, word } = getFocusCodeInfo(document, position);
  const lineText = line.text || '';

  const importPattern = /import\s+.*\s+from\s+['|"](.*)['|"]/;
  const requirePattern = /\brequire\b\(['|"](.*)['|"]\)/;
  const result = lineText.match(importPattern) || lineText.match(requirePattern);
  if (result) {
    let compName: string = (result && result[1]) || '';
    // 提取包名：@tarojs/components/View  =>  @tarojs/components
    const packageNamePattern = /^(@[^\/]+\/[^\/]+|[^@\/][^\/]*)/;
    const match = packageNamePattern.exec(compName);
    if (match) {
      compName = match[0] || '';
    }
    // 引入的是npm包 && 鼠标悬浮在包名
    if (/^(@|\w)/.test(compName) && compName.includes(word)) {
      // @ 或 字符串开头的认为是npm包
      const md = new vscode.MarkdownString(
        `[查看NPM包文档](command:JoyCode.browser.open?"http://npm.jd.com/package/${compName}")`
      );
      md.isTrusted = true;
      const hover = new vscode.Hover(md);

      reportRd(4);
      return hover;
    }
  }
}

export default function (context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.languages.registerHoverProvider('*', {
      provideHover: ipProvideHover,
    }),
    vscode.languages.registerHoverProvider(FE_FILE_TYPES, {
      provideHover: npmProvideHover,
    })
  );
}
