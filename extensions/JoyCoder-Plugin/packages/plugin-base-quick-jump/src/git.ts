import * as vscode from 'vscode';
import simpleGit from 'simple-git';
import { to } from 'await-to-js';
import { lstatSync } from 'fs-extra';
import { Logger, reportRd, getProjectRootDirByFilePath, openInBrowser } from '@joycoder/shared';

async function goGitOrigin(fileUri: vscode.Uri, type: string) {
  const projectPath = getProjectRootDirByFilePath(fileUri.fsPath);
  const git = simpleGit(projectPath, { binary: 'git' });
  const [, status] = await to(git.status());
  const currentBranch = status?.current || 'master';
  const [err, remotes] = await to(git.getRemotes(true));
  if (err || !remotes || !remotes?.length) {
    return Logger.showErrorMessage('获取远程仓库地址失败');
  }

  let url = '';
  let remote = remotes?.[0]?.refs?.fetch;
  // *****************:jx/jxapp.git  -> https://coding.jd.com/jx/jxapp.git
  remote = remote.startsWith('http') ? remote : `https://coding.jd.com/${remote.match(/:(.*)/)?.[1] || ''}`;
  if (type == 'open') {
    const isFile = lstatSync(fileUri.fsPath).isFile();
    url =
      remote.replace('.git', '') +
      (isFile ? '/blob/' : '/tree/') +
      currentBranch +
      fileUri.fsPath.replace(projectPath, '');
  } else {
    url = remote.replace('.git', '') + '/compare/master...' + currentBranch;
  }

  openInBrowser(url);
  reportRd(13);
}

export const gitCommands = [
  vscode.commands.registerCommand('JoyCode.quickJump.openGitOrigin', async (fileUri: vscode.Uri) => {
    await goGitOrigin(fileUri, 'open');
  }),
  vscode.commands.registerCommand('JoyCode.quickJump.diffGitOrigin', async (fileUri: vscode.Uri) => {
    await goGitOrigin(fileUri, 'diff');
  }),
];
