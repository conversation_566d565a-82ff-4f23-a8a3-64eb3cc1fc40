import * as vscode from 'vscode';
import { pathExists, remove as removeDir } from 'fs-extra';
import simpleGit from 'simple-git';
import path from 'path';
import { PLUGIN_ID } from '@joycoder/shared';

export function getTemplatePath(targetDirName: string) {
  const joycoderRootPath = vscode.extensions.getExtension(PLUGIN_ID)?.extensionPath || '';
  return path.join(joycoderRootPath, `./temp/${targetDirName}`);
}

/**
 *
 * @param repository 仓库地址
 * @param targetDirName 仓库克隆到的文件夹名称
 * @returns
 */
export async function loadTemplates(repository = '*****************:JoyCoder/JoyCoder-VSCode-Template.git') {
  const targetDirName = path.parse(repository).name;

  try {
    const targetPath = getTemplatePath(targetDirName);

    const exists = await pathExists(targetPath);
    if (exists) await removeDir(targetPath);

    await simpleGit().clone(repository, targetPath);
    return Promise.resolve(targetPath);
  } catch (error) {
    return Promise.reject(error);
  }
}
