/**
 * 京喜小程序仓库特有逻辑
 */
import * as vscode from 'vscode';
import * as path from 'path';
import * as parser from '@babel/parser';
import traverse from '@babel/traverse';
import generator from '@babel/generator';
import * as types from '@babel/types';
import simpleGit from 'simple-git';
import { parse, stringify } from 'comment-json';
import { pathExists, readFile, writeFile, writeFileSync } from 'fs-extra';
import { to } from 'await-to-js';
import { getProjectRootDirByFilePath, safeExecSync } from '@joycoder/shared';

export async function dealJxappPages(templateDirName: string, userTargetPath: string) {
  const projectUri = getProjectRootDirByFilePath(userTargetPath);

  const git = simpleGit(projectUri, { binary: 'git' });
  const [err, remotes] = await to(git.getRemotes(true));
  const remote = remotes?.[0]?.refs?.push;

  // 目前只支持 jxapp 下的 src/pages 页面（taro项目）
  if (err || !remote || !remote?.endsWith('jx/jxapp.git')) return;

  // 项目中的相对路径，如src/pages/pingou/testbbb
  const _asRelativePath = vscode.workspace.asRelativePath(userTargetPath);
  // 如果是创建页面就要往 app.config.ts 写入新页面路径
  if (!templateDirName.startsWith('page-') || !_asRelativePath.startsWith('src/pages')) {
    return;
  }
  // src下的相对路径，如：pages/pingou/testbbb
  const sep = '/';
  const parhArr = _asRelativePath.split(sep);
  const _srcRelativePath = parhArr.splice(1).join(sep);

  if (projectUri) {
    // 1. 操作 src/app.config.ts
    const configPath = path.resolve(projectUri, 'src/app.config.ts');
    const exists = await pathExists(configPath);
    if (exists) {
      readFile(configPath, 'utf-8', (err, data) => {
        if (err) return;
        const configAST = parser.parse(data, {
          sourceType: 'module',
          plugins: ['typescript'],
        });
        let nodeCode: any = null;
        traverse(configAST, {
          Program: ({ node }) => {
            // @ts-ignore
            const { elements } = node.body[0].declarations[0].init?.properties[2].value;
            if (!elements || !elements.length) {
              return;
            }
            let index = 0,
              _insertFlag = false;
            const _sep = _srcRelativePath.split(path.sep);
            for (; index < elements.length; index++) {
              const element = elements[index];
              const _rootPath = element.properties[0].value.value;
              const _rootPathArr = _rootPath.split('/').filter((item: string) => !!item);
              if (_sep.slice(0, _rootPathArr.length).join('/') == _rootPathArr.join('/')) {
                const _pathV = _srcRelativePath.substring(_rootPath.length);
                const _pagePath = path.join(..._pathV.split(path.sep), 'index');
                element.properties[1].value.elements.push(types.stringLiteral(_pagePath));
                _insertFlag = true;
                break;
              }
            }
            if (!_insertFlag) {
              let _rootPath = '';
              let _pagePath = '';
              if (_sep[0] == 'pages' && _sep.length > 1) {
                _rootPath = path.join(_sep[0], _sep[1]) + '/';
                _pagePath = path.join(..._sep.splice(2), 'index');
              } else {
                _rootPath = _sep[0] + '/';
                _pagePath = 'index';
              }
              elements.push(
                types.objectExpression([
                  types.objectProperty(types.identifier('root'), types.stringLiteral(_rootPath)),
                  types.objectProperty(
                    types.identifier('pages'),
                    types.arrayExpression([types.stringLiteral(_pagePath)])
                  ),
                ])
              );
            }
            nodeCode = node;
          },
        });
        if (nodeCode) {
          const _code = generator(nodeCode, {
            retainLines: true,
            // jsescOption: {
            //   minimal: true, // To avoid Chinese characters escaped
            // },
          }).code.replace(new RegExp('((]|}),)[\n]{2,}', 'g'), '$1\n');
          writeFileSync(configPath, _code);
          safeExecSync(`cd "${projectUri}";npx prettier --write ${configPath}`);
        }
      });
    }
    // 2. 操作 wxapp/app.pg.json
    const wxapp_configPath = path.resolve(projectUri, 'wxapp/app.pg.json');
    readFile(wxapp_configPath, 'utf-8', (err, dataStr) => {
      if (err) return;
      const data = parse(dataStr) as any;
      const elements = data?.subPackages;
      if (!elements) return;
      let index = 0,
        _insertFlag = false;
      const _sep = _srcRelativePath.split(path.sep);
      for (; index < elements.length; index++) {
        const element = elements[index];
        const _rootPath = element.root;
        const _rootPathArr = _rootPath.split('/').filter((item: any) => !!item);
        if (_sep.slice(0, _rootPathArr.length).join('/') == _rootPathArr.join('/')) {
          const _pathV = _srcRelativePath.substring(_rootPath.length);
          const _pagePath = path.join(..._pathV.split(path.sep), 'index');
          element.pages.push(_pagePath);
          _insertFlag = true;
          break;
        }
      }
      if (!_insertFlag) {
        let _rootPath = '';
        let _pagePath = '';
        if (_sep[0] == 'pages' && _sep.length > 1) {
          _rootPath = path.join(_sep[0], _sep[1]) + '/';
          _pagePath = path.join(..._sep.splice(2), 'index');
        } else {
          _rootPath = _sep[0] + '/';
          _pagePath = 'index';
        }
        elements.push({
          root: _rootPath,
          pages: _pagePath,
        });
      }
      writeFile(wxapp_configPath, stringify(data, null, 4));
    });
  }
}
