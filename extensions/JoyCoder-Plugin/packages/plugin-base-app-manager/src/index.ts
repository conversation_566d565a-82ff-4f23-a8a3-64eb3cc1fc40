import * as vscode from 'vscode';
import * as path from 'path';
import { pathExists, copy, readdir, stat, readFile, writeFile, remove as removeDir } from 'fs-extra';
import {
  openInVscode,
  Logger,
  reportRd,
  getCurrentWorkspaceUri,
  getRemoteConfigSync,
  executeCommandDirectly,
  getProjectRootDirByFilePath,
} from '@joycoder/shared';
import { ViewLoader } from '@joycoder/web';
import { loadTemplates } from './template';
import { dealJxappPages } from './dealJxappPages';
import to from 'await-to-js';

export default (context: vscode.ExtensionContext) => {
  const command = vscode.commands.registerCommand('JoyCode.createProject.start', (uri) => {
    // 用户右击的文件路径 || 当前打开的第一个工作区的根目录
    const curUri = uri || getCurrentWorkspaceUri(undefined, true);
    if (!curUri.fsPath) return; // 未打开项目的情况

    const { quickStartConfig = [] } = getRemoteConfigSync() || {};
    if (!quickStartConfig.length) {
      Logger.showInformationMessage('暂未配置快速开始模版~');
      return;
    }

    const that_webview = openInVscode({
      pageName: 'createProject',
      pageTitle: 'JoyCode:快速创建',
      layout: { orientation: 0, groups: [{}] },
      viewColumn: vscode.ViewColumn.One,
      onDidReceiveMessage({ type, content }) {
        switch (type) {
          case 'cp-webview-loaded':
            // webview初次打开时，更新用户右键点击的目标路径至webview
            getAndPostTargetPath(curUri, that_webview);
            break;
          case 'openFolderDialog':
            // 打开路径选择器重新选择路径
            showOpenDialog(curUri, content, that_webview);
            break;
          case 'cp-webview-submit':
            // 提交创建表单
            create(content, 'git', that_webview);
            reportRd(19);
            break;
          case 'cp-webview-message':
            Logger.showErrorMessage(content);
          case 'executeCommandDirectly':
            create(content, 'cli', that_webview);
            break;
          default:
            break;
        }
      },
      onDidDispose() {},
    });
    // webview已经打开时用户重新点击右键触发快速创建，更新用户右键点击的目标路径至webview
    getAndPostTargetPath(curUri, that_webview);
    reportRd(18);
  });
  context.subscriptions.push(command);
};

/**
 * 获取用户选择的模版创建路径并发送到webview
 * @param uri
 * @param _webview
 */
async function getAndPostTargetPath(uri: vscode.Uri, _webview: ViewLoader | null) {
  let _path = uri.fsPath;
  try {
    const fileStat = await vscode.workspace.fs.stat(uri);
    // 如果是文件就要获取它的上一级目录地址
    if (fileStat.type == 1) {
      _path = path.resolve(_path, '..');
      console.log(_path);
    }
  } catch (error) {
    console.log(error);
  }
  _webview?.postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: 'getCurrentFilePath',
      data: _path,
    },
  });
}

/**
 * 打开文件浏览器选择模版生成的目标文件夹
 * @param uri
 * @param content
 * @param _webview
 */
async function showOpenDialog(uri: vscode.Uri, content: string, _webview: ViewLoader | null) {
  const _uris = await vscode.window.showOpenDialog({
    canSelectFolders: true,
    canSelectFiles: false,
    canSelectMany: false,
    // @ts-ignore
    defaultUri: {
      ...uri,
      path: content,
      fsPath: content,
    },
  });
  if (_uris && _uris.length) {
    _webview?.postMessageToWebview({
      type: 'COMMON',
      payload: {
        type: 'getCurrentFilePath',
        data: _uris[0].fsPath,
      },
    });
  }
}

/**
 * 创建模板文件
 * @param content
 * @param type
 * @param _webview
 * @returns
 */
async function create(
  {
    name, // 用户输入的项目名称
    path: userSelectedPath, // 用户选择的目标路径
    repository, // 模版所在的仓库地址
    templateDirName, // 模版在上述仓库中的文件路径
    formItemConf, // 水滴上配置的表单项
    commands,
    type: contentType,
  }: {
    name: string;
    path: string;
    repository: string;
    templateDirName: string;
    formItemConf: any;
    commands: any;
    type: string;
  },
  type,
  _webview: ViewLoader | null
) {
  // 方式1： 通过cli创建模版
  if (type == 'cli') {
    executeCommandDirectly(
      handleCommandData({
        formItemConf,
        path: userSelectedPath,
        commands,
        type: contentType,
      })
    );
    createDoneMsg(_webview);
    return;
  }

  // 校验项目是否已存在（cli创建模版时目标文件夹非必填，所以无法做重复校验）
  const userTargetPath = path.resolve(userSelectedPath, name); // 模版最终被拷贝到的文件夹
  const exists = await pathExists(userTargetPath);

  if (exists) {
    createErrorMsg(_webview);
    return Logger.showErrorMessage(`此目录下已存在相同名称的文件夹~(${userTargetPath})`);
  }

  // 方式2：通过仓库下载模版
  const [error, repoTargetPath] = await to(loadTemplates(repository));
  if (error) {
    createErrorMsg(_webview);
    return Logger.showErrorMessage(`模板创建失败~(1001:${error.message})`);
  }

  // 将模版拷贝至用户选择的路径
  // 当前模版的完整路径：仓库路径+仓库中的模版路径
  const curTplPath = path.resolve(repoTargetPath, `./${templateDirName}`);
  copy(curTplPath, userTargetPath, async (err) => {
    if (err) {
      createErrorMsg(_webview);
      Logger.showErrorMessage('模板创建失败~(1002)');
      return console.error(err);
    }

    // 删除模版中的git信息
    await removeDir(path.resolve(userTargetPath, './.git'));
    // 替换模版中的信息，如页面title等
    await replaceText(userTargetPath, formItemConf);

    dealJxappPages(templateDirName, userTargetPath);

    Logger.showInformationMessage(`模版创建成功~ (路径：${userTargetPath})`);
    createDoneMsg(_webview);
  });
}

/**
 * 用表单中用户输入的内容替换模板文件里的文本
 * @param _path
 * @param formItemConf
 * @returns
 */
async function replaceText(_path: string, formItemConf: any) {
  const files = await readdir(_path);

  files.map(async (item) => {
    const cwd = path.join(_path, item);
    const cwdStat = await stat(cwd);
    if (cwdStat.isDirectory()) {
      replaceText(cwd, formItemConf);
    } else {
      readFile(cwd, (err, data) => {
        if (err) {
          return;
        }

        let _data = data.toString();

        formItemConf.forEach((element: { replaceReg: string | RegExp; value: string }) => {
          if (element.replaceReg) {
            _data = _data.replace(new RegExp(element.replaceReg, 'g'), element.value);
          }
        });
        writeFile(cwd, _data);
      });
    }
  });
}

/**
 * 创建模板成功提示
 * @param _webview
 */
function createDoneMsg(_webview: ViewLoader | null, type = 'createTemplateDone') {
  _webview?.postMessageToWebview({
    type: 'COMMON',
    payload: {
      type,
    },
  });
}

/**
 * 创建模板失败提示
 * @param _webview
 */
function createErrorMsg(_webview: ViewLoader | null) {
  createDoneMsg(_webview, 'createTemplateError');
}

/**
 * 预处理自定义命令数据，目前只对taro类型定制化了；
 * @returns commands
 */
function handleCommandData(content) {
  const { formItemConf, type, commands, path } = content;
  if (!formItemConf || !formItemConf.length) return commands;
  let command = commands[1];
  formItemConf.forEach((item) => {
    switch (type) {
      case '5': // Taro Cli命令逻辑
        command += ` --${item.name} ${item.value}`;
    }
  });
  if (type == 5 && command.includes('taro create')) {
    const targetPath = getProjectRootDirByFilePath(path, 'package.json');
    if (targetPath) {
      commands[0] = 'cd ' + targetPath;
    }
  }
  commands[1] = command;
  return commands;
}
