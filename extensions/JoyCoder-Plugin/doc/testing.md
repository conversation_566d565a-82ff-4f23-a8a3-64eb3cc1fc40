# 测试文档

## 概述

本项目使用 Jest 作为测试框架。测试文件主要位于 `packages/agent-driven/src` 目录下的 `__tests__` 文件夹中。

## 运行测试

### 运行所有测试

要运行所有测试，请在项目根目录执行以下命令：

```bash
pnpm test
```

### 运行特定测试

要运行特定的测试文件，可以使用以下命令：

```bash
pnpm run test:tools
```

这将运行 `packages/agent-driven/src/core/prompts/__tests__/tools.test.ts` 文件中的测试。

#### tools.test.ts 测试说明

`tools.test.ts` 文件包含了对 `getToolDescriptions` 函数的测试。这些测试涵盖了以下方面：

1. 基本工具描述的生成
2. 浏览器相关工具的条件包含
3. MCP 相关工具的条件包含
4. 特定工具（如 use_command, use_read_file, use_write_file）描述的正确性

运行这些测试可以确保工具描述生成功能在各种情况下都能正常工作。

## 测试文件结构

测试文件通常与被测试的源文件位于同一目录下的 `__tests__` 文件夹中。例如：

- `src/core/prompts/tools.ts` 的测试文件位于 `src/core/prompts/__tests__/tools.test.ts`

## 添加新的测试

1. 在适当的 `__tests__` 目录下创建一个新的测试文件，命名为 `[filename].test.ts`
2. 导入需要测试的函数或类
3. 使用 Jest 的 `describe` 和 `it` 函数来组织和编写测试用例
4. 运行测试以确保它们通过

例如：

```typescript
import { someFunction } from '../someFile';

describe('someFunction', () => {
  it('should do something', () => {
    const result = someFunction();
    expect(result).toBe(expectedValue);
  });
});
```

5. 如果需要，在 `package.json` 中添加新的 npm 脚本来运行特定的测试文件或测试套件

## 注意事项

- 确保在提交代码之前运行所有测试并确保它们通过
- 保持测试代码的简洁和可读性
- 适当使用 mocks 和 stubs 来隔离被测试的单元
