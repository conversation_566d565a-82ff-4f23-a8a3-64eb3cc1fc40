lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@vscode/markdown-it-katex':
        specifier: ^1.1.1
        version: 1.1.2
    devDependencies:
      '@types/markdown-it':
        specifier: ^0.0.0
        version: 0.0.0
      '@types/vscode-notebook-renderer':
        specifier: ^1.60.0
        version: 1.72.3

packages:

  '@types/markdown-it@0.0.0':
    resolution: {integrity: sha512-rLEOTm6Wi9M8GFnIK7VczXSEThIN/eVoevpTYVk+FD/DPX3N15Sj9b3vkjjDY63U0Zw1yawf13CI92CCHpC5kw==}

  '@types/vscode-notebook-renderer@1.72.3':
    resolution: {integrity: sha512-MfmEI3A2McbUV2WaijoTgLOAs9chwHN4WmqOedl3jdtlbzJBWIQ9ZFmQdzPa3lYr5j8DJhRg3KB5AIM/BBfg9Q==}

  '@vscode/markdown-it-katex@1.1.2':
    resolution: {integrity: sha512-+4IIv5PgrmhKvW/3LpkpkGg257OViEhXkOOgCyj5KMsjsOfnRXkni8XAuuF9Ui5p3B8WnUovlDXAQNb8RJ/RaQ==}

  commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==}
    engines: {node: '>= 12'}

  katex@0.16.22:
    resolution: {integrity: sha512-XCHRdUw4lf3SKBaJe4EvgqIuWwkPSo9XoeO8GjQW94Bp7TWv9hNhzZjZ+OH9yf1UmLygb7DIT5GSFQiyt16zYg==}
    hasBin: true

snapshots:

  '@types/markdown-it@0.0.0': {}

  '@types/vscode-notebook-renderer@1.72.3': {}

  '@vscode/markdown-it-katex@1.1.2':
    dependencies:
      katex: 0.16.22

  commander@8.3.0: {}

  katex@0.16.22:
    dependencies:
      commander: 8.3.0
