lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@enonic/fnv-plus':
        specifier: ^1.3.0
        version: 1.3.0
      detect-indent:
        specifier: ^6.0.0
        version: 6.1.0
    devDependencies:
      '@jupyterlab/nbformat':
        specifier: ^3.2.9
        version: 3.6.8(crypto@1.0.1)
      '@types/markdown-it':
        specifier: 12.2.3
        version: 12.2.3

packages:

  '@enonic/fnv-plus@1.3.0':
    resolution: {integrity: sha512-BCN9uNWH8AmiP7BXBJqEinUY9KXalmRzo+L0cB/mQsmFfzODxwQrbvxCHXUNH2iP+qKkWYtB4vyy8N62PViMFw==}

  '@jupyterlab/nbformat@3.6.8':
    resolution: {integrity: sha512-HprEmm1jUJAMFlhFWZOWv8XITXUBK3hRn7kzRft5gdFZ2T7ClpDLMLunobOlJAQOP01H8GBm5Je27KIoukWgfg==}

  '@lumino/coreutils@1.12.1':
    resolution: {integrity: sha512-JLu3nTHzJk9N8ohZ85u75YxemMrmDzJdNgZztfP7F7T7mxND3YVNCkJG35a6aJ7edu1sIgCjBxOvV+hv27iYvQ==}
    peerDependencies:
      crypto: 1.0.1

  '@types/linkify-it@5.0.0':
    resolution: {integrity: sha512-sVDA58zAw4eWAffKOaQH5/5j3XeayukzDk+ewSsnv3p4yJEZHCCzMDiZM8e0OUrRvmpGZ85jf4yDHkHsgBNr9Q==}

  '@types/markdown-it@12.2.3':
    resolution: {integrity: sha512-GKMHFfv3458yYy+v/N8gjufHO6MSZKCOXpZc5GXIWWy8uldwfmPn98vp81gZ5f9SVw8YYBctgfJ22a2d7AOMeQ==}

  '@types/mdurl@2.0.0':
    resolution: {integrity: sha512-RGdgjQUZba5p6QEFAVx2OGb8rQDL/cPRG7GiedRzMcJ1tYnUANBncjbSB1NRGwbvjcPeikRABz2nshyPk1bhWg==}

  crypto@1.0.1:
    resolution: {integrity: sha1-KvG3ytgXXSTIobB3glV5SiGAMDc=, tarball: http://registry.m.jd.com/crypto/download/crypto-1.0.1.tgz}
    deprecated: This package is no longer supported. It's now a built-in Node module. If you've depended on crypto, you should switch to the one that's built-in.

  detect-indent@6.1.0:
    resolution: {integrity: sha512-reYkTUJAZb9gUuZ2RvVCNhVHdg62RHnJ7WJl8ftMi4diZ6NWlciOzQN88pUhSELEwflJht4oQDv0F0BMlwaYtA==}
    engines: {node: '>=8'}

snapshots:

  '@enonic/fnv-plus@1.3.0': {}

  '@jupyterlab/nbformat@3.6.8(crypto@1.0.1)':
    dependencies:
      '@lumino/coreutils': 1.12.1(crypto@1.0.1)
    transitivePeerDependencies:
      - crypto

  '@lumino/coreutils@1.12.1(crypto@1.0.1)':
    dependencies:
      crypto: 1.0.1

  '@types/linkify-it@5.0.0': {}

  '@types/markdown-it@12.2.3':
    dependencies:
      '@types/linkify-it': 5.0.0
      '@types/mdurl': 2.0.0

  '@types/mdurl@2.0.0': {}

  crypto@1.0.1: {}

  detect-indent@6.1.0: {}
