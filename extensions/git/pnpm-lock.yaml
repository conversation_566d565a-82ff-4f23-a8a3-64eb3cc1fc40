lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@joaomoreno/unique-names-generator':
        specifier: ^5.2.0
        version: 5.2.0
      '@vscode/extension-telemetry':
        specifier: ^0.9.8
        version: 0.9.9(tslib@2.8.1)
      '@vscode/iconv-lite-umd':
        specifier: 0.7.0
        version: 0.7.0
      byline:
        specifier: ^5.0.0
        version: 5.0.0
      file-type:
        specifier: 16.5.4
        version: 16.5.4
      jschardet:
        specifier: 3.1.4
        version: 3.1.4
      picomatch:
        specifier: 2.3.1
        version: 2.3.1
      vscode-uri:
        specifier: ^2.0.0
        version: 2.1.2
      which:
        specifier: 4.0.0
        version: 4.0.0
    devDependencies:
      '@types/byline':
        specifier: 4.2.31
        version: 4.2.31
      '@types/mocha':
        specifier: ^9.1.1
        version: 9.1.1
      '@types/node':
        specifier: 20.x
        version: 20.19.9
      '@types/picomatch':
        specifier: 2.3.0
        version: 2.3.0
      '@types/which':
        specifier: 3.0.0
        version: 3.0.0

packages:

  '@joaomoreno/unique-names-generator@5.2.0':
    resolution: {integrity: sha512-JEh3qZ85Z6syFvQlhRGRyTPI1M5VticiiP8Xl8EV0XfyfI4Mwzd6Zw28BBrEgUJCYv/cpKCQClVj3J8Tn0KFiA==}
    engines: {node: '>=8'}

  '@microsoft/1ds-core-js@4.3.9':
    resolution: {integrity: sha512-T8s5qROH7caBNiFrUpN8vgC6wg7QysVPryZKprgl3kLQQPpoMFM6ffIYvUWD74KM9fWWLU7vzFFNBWDBsrTyWg==}

  '@microsoft/1ds-post-js@4.3.9':
    resolution: {integrity: sha512-BvxI4CW8Ws+gfXKy+Y/9pmEXp88iU1GYVjkUfqXP7La59VHARTumlG5iIgMVvaifOrvSW7G6knvQM++0tEfMBQ==}

  '@microsoft/applicationinsights-channel-js@3.3.9':
    resolution: {integrity: sha512-/yEgSe6vT2ycQJkXu6VF04TB5XBurk46ECV7uo6KkNhWyDEctAk1VDWB7EqXYdwLhKMbNOYX1pvz7fj43fGNqg==}
    peerDependencies:
      tslib: '>= 1.0.0'

  '@microsoft/applicationinsights-common@3.3.9':
    resolution: {integrity: sha512-IgruOuDBxmBK9jYo7SqLJG7Z9OwmAmlvHET49srpN6pqQlEjRpjD1nfA3Ps4RSEbF89a/ad2phQaBp8jvm122g==}
    peerDependencies:
      tslib: '>= 1.0.0'

  '@microsoft/applicationinsights-core-js@3.3.9':
    resolution: {integrity: sha512-xliiE9H09xCycndlua4QjajN8q5k/ET6VCv+e0Jjodxr9+cmoOP/6QY9dun9ptokuwR8TK0qOaIJ8z4fgslVSA==}
    peerDependencies:
      tslib: '>= 1.0.0'

  '@microsoft/applicationinsights-shims@3.0.1':
    resolution: {integrity: sha512-DKwboF47H1nb33rSUfjqI6ryX29v+2QWcTrRvcQDA32AZr5Ilkr7whOOSsD1aBzwqX0RJEIP1Z81jfE3NBm/Lg==}

  '@microsoft/applicationinsights-web-basic@3.3.9':
    resolution: {integrity: sha512-8tLaAgsCpWjoaxit546RqeuECnHQPBLnOZhzTYG76oPG1ku7dNXaRNieuZLbO+XmAtg/oxntKLAVoPND8NRgcA==}
    peerDependencies:
      tslib: '>= 1.0.0'

  '@microsoft/dynamicproto-js@2.0.3':
    resolution: {integrity: sha512-JTWTU80rMy3mdxOjjpaiDQsTLZ6YSGGqsjURsY6AUQtIj0udlF/jYmhdLZu8693ZIC0T1IwYnFa0+QeiMnziBA==}

  '@nevware21/ts-async@0.5.4':
    resolution: {integrity: sha512-IBTyj29GwGlxfzXw2NPnzty+w0Adx61Eze1/lknH/XIVdxtF9UnOpk76tnrHXWa6j84a1RR9hsOcHQPFv9qJjA==}

  '@nevware21/ts-utils@0.12.5':
    resolution: {integrity: sha512-JPQZWPKQJjj7kAftdEZL0XDFfbMgXCGiUAZe0d7EhLC3QlXTlZdSckGqqRIQ2QNl0VTEZyZUvRBw6Ednw089Fw==}

  '@tokenizer/token@0.3.0':
    resolution: {integrity: sha512-OvjF+z51L3ov0OyAU0duzsYuvO01PH7x4t6DJx+guahgTnBHkhJdG7soQeTSFLWN3efnHyibZ4Z8l2EuWwJN3A==}

  '@types/byline@4.2.31':
    resolution: {integrity: sha512-TC6Ljn7tALesQMQyTNoMWoM44SNvWtCLkJDrA/TxcwE5ILkWt4zi5wbEokqiDk42S75eykAY1onPImWDybOkmQ==}

  '@types/mocha@9.1.1':
    resolution: {integrity: sha512-Z61JK7DKDtdKTWwLeElSEBcWGRLY8g95ic5FoQqI9CMx0ns/Ghep3B4DfcEimiKMvtamNVULVNKEsiwV3aQmXw==}

  '@types/node@20.19.9':
    resolution: {integrity: sha512-cuVNgarYWZqxRJDQHEB58GEONhOK79QVR/qYx4S7kcUObQvUwvFnYxJuuHUKm2aieN9X3yZB4LZsuYNU1Qphsw==}

  '@types/picomatch@2.3.0':
    resolution: {integrity: sha512-O397rnSS9iQI4OirieAtsDqvCj4+3eY1J+EPdNTKuHuRWIfUoGyzX294o8C4KJYaLqgSrd2o60c5EqCU8Zv02g==}

  '@types/which@3.0.0':
    resolution: {integrity: sha512-ASCxdbsrwNfSMXALlC3Decif9rwDMu+80KGp5zI2RLRotfMsTv7fHL8W8VDp24wymzDyIFudhUeSCugrgRFfHQ==}

  '@vscode/extension-telemetry@0.9.9':
    resolution: {integrity: sha512-WG/H+H/JRMPnpbXMufXgXlaeJwKszXfAanOERV/nkXBbYyNw0KR84JjUjSg+TgkzYEF/ttRoHTP6fFZWkXdoDQ==}
    engines: {vscode: ^1.75.0}

  '@vscode/iconv-lite-umd@0.7.0':
    resolution: {integrity: sha512-bRRFxLfg5dtAyl5XyiVWz/ZBPahpOpPrNYnnHpOpUZvam4tKH35wdhP4Kj6PbM0+KdliOsPzbGWpkxcdpNB/sg==}

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  byline@5.0.0:
    resolution: {integrity: sha512-s6webAy+R4SR8XVuJWt2V2rGvhnrhxN+9S15GNuTK3wKPOXFF6RNc+8ug2XhH+2s4f+uudG4kUVYmYOQWL2g0Q==}
    engines: {node: '>=0.10.0'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  file-type@16.5.4:
    resolution: {integrity: sha512-/yFHK0aGjFEgDJjEKP0pWCplsPFPhwyfwevf/pVxiN0tmE4L9LmwWxWukdJSHdoCli4VgQLehjJtwQBnqmsKcw==}
    engines: {node: '>=10'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  isexe@3.1.1:
    resolution: {integrity: sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==}
    engines: {node: '>=16'}

  jschardet@3.1.4:
    resolution: {integrity: sha512-/kmVISmrwVwtyYU40iQUOp3SUPk2dhNCMsZBQX0R1/jZ8maaXJ/oZIzUOiyOqcgtLnETFKYChbJ5iDC/eWmFHg==}
    engines: {node: '>=0.1.90'}

  peek-readable@4.1.0:
    resolution: {integrity: sha512-ZI3LnwUv5nOGbQzD9c2iDG6toheuXSZP5esSHBjopsXH4dg19soufvpUGA3uohi5anFtGb2lhAVdHzH6R/Evvg==}
    engines: {node: '>=8'}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  readable-stream@4.7.0:
    resolution: {integrity: sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  readable-web-to-node-stream@3.0.4:
    resolution: {integrity: sha512-9nX56alTf5bwXQ3ZDipHJhusu9NTQJ/CVPtb/XHAJCXihZeitfJvIRS4GqQ/mfIoOE3IelHMrpayVrosdHBuLw==}
    engines: {node: '>=8'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strtok3@6.3.0:
    resolution: {integrity: sha512-fZtbhtvI9I48xDSywd/somNqgUHl2L2cstmXCCif0itOf96jeW18MBSyrLuNicYQVkvpOxkZtkzujiTJ9LW5Jw==}
    engines: {node: '>=10'}

  token-types@4.2.1:
    resolution: {integrity: sha512-6udB24Q737UD/SDsKAHI9FCRP7Bqc9D/MQUV02ORQg5iskjtLJlZJNdN4kKtcdtwCeWIwIHDGaUsTsCCAa8sFQ==}
    engines: {node: '>=10'}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: http://registry.m.jd.com/tslib/download/tslib-2.8.1.tgz}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  vscode-uri@2.1.2:
    resolution: {integrity: sha512-8TEXQxlldWAuIODdukIb+TR5s+9Ds40eSJrw+1iDDA9IFORPjMELarNQE3myz5XIkWWpdprmJjm1/SxMlWOC8A==}

  which@4.0.0:
    resolution: {integrity: sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==}
    engines: {node: ^16.13.0 || >=18.0.0}
    hasBin: true

snapshots:

  '@joaomoreno/unique-names-generator@5.2.0': {}

  '@microsoft/1ds-core-js@4.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/applicationinsights-core-js': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-async': 0.5.4
      '@nevware21/ts-utils': 0.12.5
    transitivePeerDependencies:
      - tslib

  '@microsoft/1ds-post-js@4.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/1ds-core-js': 4.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-async': 0.5.4
      '@nevware21/ts-utils': 0.12.5
    transitivePeerDependencies:
      - tslib

  '@microsoft/applicationinsights-channel-js@3.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/applicationinsights-common': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-core-js': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-async': 0.5.4
      '@nevware21/ts-utils': 0.12.5
      tslib: 2.8.1

  '@microsoft/applicationinsights-common@3.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/applicationinsights-core-js': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-utils': 0.12.5
      tslib: 2.8.1

  '@microsoft/applicationinsights-core-js@3.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-async': 0.5.4
      '@nevware21/ts-utils': 0.12.5
      tslib: 2.8.1

  '@microsoft/applicationinsights-shims@3.0.1':
    dependencies:
      '@nevware21/ts-utils': 0.12.5

  '@microsoft/applicationinsights-web-basic@3.3.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/applicationinsights-channel-js': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-common': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-core-js': 3.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-shims': 3.0.1
      '@microsoft/dynamicproto-js': 2.0.3
      '@nevware21/ts-async': 0.5.4
      '@nevware21/ts-utils': 0.12.5
      tslib: 2.8.1

  '@microsoft/dynamicproto-js@2.0.3':
    dependencies:
      '@nevware21/ts-utils': 0.12.5

  '@nevware21/ts-async@0.5.4':
    dependencies:
      '@nevware21/ts-utils': 0.12.5

  '@nevware21/ts-utils@0.12.5': {}

  '@tokenizer/token@0.3.0': {}

  '@types/byline@4.2.31':
    dependencies:
      '@types/node': 20.19.9

  '@types/mocha@9.1.1': {}

  '@types/node@20.19.9':
    dependencies:
      undici-types: 6.21.0

  '@types/picomatch@2.3.0': {}

  '@types/which@3.0.0': {}

  '@vscode/extension-telemetry@0.9.9(tslib@2.8.1)':
    dependencies:
      '@microsoft/1ds-core-js': 4.3.9(tslib@2.8.1)
      '@microsoft/1ds-post-js': 4.3.9(tslib@2.8.1)
      '@microsoft/applicationinsights-web-basic': 3.3.9(tslib@2.8.1)
    transitivePeerDependencies:
      - tslib

  '@vscode/iconv-lite-umd@0.7.0': {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  base64-js@1.5.1: {}

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  byline@5.0.0: {}

  event-target-shim@5.0.1: {}

  events@3.3.0: {}

  file-type@16.5.4:
    dependencies:
      readable-web-to-node-stream: 3.0.4
      strtok3: 6.3.0
      token-types: 4.2.1

  ieee754@1.2.1: {}

  isexe@3.1.1: {}

  jschardet@3.1.4: {}

  peek-readable@4.1.0: {}

  picomatch@2.3.1: {}

  process@0.11.10: {}

  readable-stream@4.7.0:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readable-web-to-node-stream@3.0.4:
    dependencies:
      readable-stream: 4.7.0

  safe-buffer@5.2.1: {}

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strtok3@6.3.0:
    dependencies:
      '@tokenizer/token': 0.3.0
      peek-readable: 4.1.0

  token-types@4.2.1:
    dependencies:
      '@tokenizer/token': 0.3.0
      ieee754: 1.2.1

  tslib@2.8.1: {}

  undici-types@6.21.0: {}

  vscode-uri@2.1.2: {}

  which@4.0.0:
    dependencies:
      isexe: 3.1.1
