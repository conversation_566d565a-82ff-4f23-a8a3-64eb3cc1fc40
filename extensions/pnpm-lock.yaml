lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      typescript:
        specifier: ^5.8.2
        version: 5.8.3
    devDependencies:
      '@parcel/watcher':
        specifier: ^2.5.1
        version: 2.5.1
      esbuild:
        specifier: 0.25.0
        version: 0.25.0
      vscode-grammar-updater:
        specifier: ^1.1.0
        version: 1.1.0

packages:

  '@esbuild/aix-ppc64@0.25.0':
    resolution: {integrity: sha1-SZYAxeF1elJJkNXZJgHwrDzof2Q=, tarball: http://registry.m.jd.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-arm64@0.25.0':
    resolution: {integrity: sha1-ubgjFWGh37lOsx9O4Fa5KphcMk8=, tarball: http://registry.m.jd.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-arm@0.25.0':
    resolution: {integrity: sha1-ym54iJQlBfE+iKyfX30qcvn6zSs=, tarball: http://registry.m.jd.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-x64@0.25.0':
    resolution: {integrity: sha1-52XqdTusRC38nLU2Us6L050z4WM=, tarball: http://registry.m.jd.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/darwin-arm64@0.25.0':
    resolution: {integrity: sha1-+jlBZLDYnU/cOoohmJr3DvV5+iw=, tarball: http://registry.m.jd.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/darwin-x64@0.25.0':
    resolution: {integrity: sha1-kZedmNMLpufWmyLGF8yCva1g5Ho=, tarball: http://registry.m.jd.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/freebsd-arm64@0.25.0':
    resolution: {integrity: sha1-uX6XBzMQc2tDCgewmdg3CEuF6c4=, tarball: http://registry.m.jd.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/freebsd-x64@0.25.0':
    resolution: {integrity: sha1-87aU0Nph2ZEOx97/eU1ETPvztuc=, tarball: http://registry.m.jd.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-arm64@0.25.0':
    resolution: {integrity: sha1-+SH2mfFi8zIDbVZXytkDb3qZP3M=, tarball: http://registry.m.jd.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-arm@0.25.0':
    resolution: {integrity: sha1-zEkwWzxtoxfJAGiJlaQFDmzJHKM=, tarball: http://registry.m.jd.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-ia32@0.25.0':
    resolution: {integrity: sha1-Pgc2/PqxbP8ELeyAYkfix24Qnhk=, tarball: http://registry.m.jd.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-loong64@0.25.0':
    resolution: {integrity: sha1-6iv3MIg83bnfuFEkIytah1uAIMc=, tarball: http://registry.m.jd.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-mips64el@0.25.0':
    resolution: {integrity: sha1-TKursU7t4JJImAotLYuWZGQpT/E=, tarball: http://registry.m.jd.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-ppc64@0.25.0':
    resolution: {integrity: sha1-iGCkYJkUwGU3OnckLphReWWOGVE=, tarball: http://registry.m.jd.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-riscv64@0.25.0':
    resolution: {integrity: sha1-uvJuILstOM+4buKC3/hAwE9O2Yc=, tarball: http://registry.m.jd.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-s390x@0.25.0':
    resolution: {integrity: sha1-gyOvwNbLG23G6f0h79nhVCw2QKQ=, tarball: http://registry.m.jd.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-x64@0.25.0':
    resolution: {integrity: sha1-CPz2DLQA7SOC6fjg9VkLrIgQRpo=, tarball: http://registry.m.jd.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/netbsd-arm64@0.25.0':
    resolution: {integrity: sha1-k1xsdOIPciSRj74ubG/oZbbG6ls=, tarball: http://registry.m.jd.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/netbsd-x64@0.25.0':
    resolution: {integrity: sha1-QUZ3zvZtFsWk0hB1HrKIG7nBtis=, tarball: http://registry.m.jd.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/openbsd-arm64@0.25.0':
    resolution: {integrity: sha1-j9VaTQjSXNxXKETxPIjWeMhNE/c=, tarball: http://registry.m.jd.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/openbsd-x64@0.25.0':
    resolution: {integrity: sha1-DEjdsUlLvC1ry6oUKaf0Zfod7d4=, tarball: http://registry.m.jd.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/sunos-x64@0.25.0':
    resolution: {integrity: sha1-hv+Qddd5YrYN0mID1zUvkmhMjJI=, tarball: http://registry.m.jd.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-arm64@0.25.0':
    resolution: {integrity: sha1-hJxiMnwyKUZ/W1zWgb9QWIRC6Ww=, tarball: http://registry.m.jd.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-ia32@0.25.0':
    resolution: {integrity: sha1-9i60gM18ygiMtlu0am2yW3JdwHk=, tarball: http://registry.m.jd.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.0.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-x64@0.25.0':
    resolution: {integrity: sha1-yOEZowp8jWC50uItIHNyLd47cQs=, tarball: http://registry.m.jd.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.0.tgz}
    engines: {node: '>=18'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=, tarball: http://registry.m.jd.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha1-PSbc443mWQ73nEfsLFV5PAatT2c=, tarball: http://registry.m.jd.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=, tarball: http://registry.m.jd.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=, tarball: http://registry.m.jd.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==}
    engines: {node: '>= 10.0.0'}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  coffeescript@1.12.7:
    resolution: {integrity: sha512-pLXHFxQMPklVoEekowk8b3erNynC+DVJzChxS/LCBBgR6/8AJkHivkm//zbowcfc7BTCAjryuhx6gPqPRfsFoA==}
    engines: {node: '>=0.8.0'}
    hasBin: true

  cson-parser@4.0.9:
    resolution: {integrity: sha512-I79SAcCYquWnEfXYj8hBqOOWKj6eH6zX1hhX3yqmS4K3bYp7jME3UFpHPzu3rUew0oyfc0s8T6IlWGXRAheHag==}
    engines: {node: '>=10.13'}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  esbuild@0.25.0:
    resolution: {integrity: sha512-BXq5mqc8ltbaN34cDqWuYKyNhX8D/Z0J1xdtdQ8UcIIIyJyz+ZMKUt58tF3SrZ85jcfN/PZYhjR5uDQAYNVbuw==}
    engines: {node: '>=18'}
    hasBin: true

  fast-plist@0.1.2:
    resolution: {integrity: sha512-2HxzrqJhmMoxVzARjYFvkzkL2dCBB8sogU5sD8gqcZWv5UCivK9/cXM9KIPDRwU+eD3mbRDN/GhW8bO/4dtMfg==}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  vscode-grammar-updater@1.1.0:
    resolution: {integrity: sha512-rWcJXyEFK27Mh9bxfBTLaul0KiGQk0GMXj2qTDH9cy3UZVx5MrF035B03os1w4oIXwl/QDhdLnsBK0j2SNiL1A==}
    hasBin: true

snapshots:

  '@esbuild/aix-ppc64@0.25.0':
    optional: true

  '@esbuild/android-arm64@0.25.0':
    optional: true

  '@esbuild/android-arm@0.25.0':
    optional: true

  '@esbuild/android-x64@0.25.0':
    optional: true

  '@esbuild/darwin-arm64@0.25.0':
    optional: true

  '@esbuild/darwin-x64@0.25.0':
    optional: true

  '@esbuild/freebsd-arm64@0.25.0':
    optional: true

  '@esbuild/freebsd-x64@0.25.0':
    optional: true

  '@esbuild/linux-arm64@0.25.0':
    optional: true

  '@esbuild/linux-arm@0.25.0':
    optional: true

  '@esbuild/linux-ia32@0.25.0':
    optional: true

  '@esbuild/linux-loong64@0.25.0':
    optional: true

  '@esbuild/linux-mips64el@0.25.0':
    optional: true

  '@esbuild/linux-ppc64@0.25.0':
    optional: true

  '@esbuild/linux-riscv64@0.25.0':
    optional: true

  '@esbuild/linux-s390x@0.25.0':
    optional: true

  '@esbuild/linux-x64@0.25.0':
    optional: true

  '@esbuild/netbsd-arm64@0.25.0':
    optional: true

  '@esbuild/netbsd-x64@0.25.0':
    optional: true

  '@esbuild/openbsd-arm64@0.25.0':
    optional: true

  '@esbuild/openbsd-x64@0.25.0':
    optional: true

  '@esbuild/sunos-x64@0.25.0':
    optional: true

  '@esbuild/win32-arm64@0.25.0':
    optional: true

  '@esbuild/win32-ia32@0.25.0':
    optional: true

  '@esbuild/win32-x64@0.25.0':
    optional: true

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  coffeescript@1.12.7: {}

  cson-parser@4.0.9:
    dependencies:
      coffeescript: 1.12.7

  detect-libc@1.0.3: {}

  esbuild@0.25.0:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.0
      '@esbuild/android-arm': 0.25.0
      '@esbuild/android-arm64': 0.25.0
      '@esbuild/android-x64': 0.25.0
      '@esbuild/darwin-arm64': 0.25.0
      '@esbuild/darwin-x64': 0.25.0
      '@esbuild/freebsd-arm64': 0.25.0
      '@esbuild/freebsd-x64': 0.25.0
      '@esbuild/linux-arm': 0.25.0
      '@esbuild/linux-arm64': 0.25.0
      '@esbuild/linux-ia32': 0.25.0
      '@esbuild/linux-loong64': 0.25.0
      '@esbuild/linux-mips64el': 0.25.0
      '@esbuild/linux-ppc64': 0.25.0
      '@esbuild/linux-riscv64': 0.25.0
      '@esbuild/linux-s390x': 0.25.0
      '@esbuild/linux-x64': 0.25.0
      '@esbuild/netbsd-arm64': 0.25.0
      '@esbuild/netbsd-x64': 0.25.0
      '@esbuild/openbsd-arm64': 0.25.0
      '@esbuild/openbsd-x64': 0.25.0
      '@esbuild/sunos-x64': 0.25.0
      '@esbuild/win32-arm64': 0.25.0
      '@esbuild/win32-ia32': 0.25.0
      '@esbuild/win32-x64': 0.25.0

  fast-plist@0.1.2: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  is-extglob@2.1.1: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  node-addon-api@7.1.1: {}

  picomatch@2.3.1: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  typescript@5.8.3: {}

  vscode-grammar-updater@1.1.0:
    dependencies:
      cson-parser: 4.0.9
      fast-plist: 0.1.2
