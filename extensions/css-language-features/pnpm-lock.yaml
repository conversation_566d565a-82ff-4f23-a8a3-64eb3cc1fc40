lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      vscode-languageclient:
        specifier: ^10.0.0-next.13
        version: 10.0.0-next.16
      vscode-uri:
        specifier: ^3.0.8
        version: 3.1.0
    devDependencies:
      '@types/node':
        specifier: 20.x
        version: 20.19.9

packages:

  '@isaacs/balanced-match@4.0.1':
    resolution: {integrity: sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==}
    engines: {node: 20 || >=22}

  '@isaacs/brace-expansion@5.0.0':
    resolution: {integrity: sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==}
    engines: {node: 20 || >=22}

  '@types/node@20.19.9':
    resolution: {integrity: sha512-cuVNgarYWZqxRJDQHEB58GEONhOK79QVR/qYx4S7kcUObQvUwvFnYxJuuHUKm2aieN9X3yZB4LZsuYNU1Qphsw==}

  minimatch@10.0.3:
    resolution: {integrity: sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==}
    engines: {node: 20 || >=22}

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  vscode-jsonrpc@9.0.0-next.8:
    resolution: {integrity: sha512-pN6L5eiNBvUpNFBJvudaZ83klir0T/wLFCDpYhpOEsKXyhsWyYsNMzoG7BK6zJoZLHGSSsaTJDjCcPwnLgUyPQ==}
    engines: {node: '>=14.0.0'}

  vscode-languageclient@10.0.0-next.16:
    resolution: {integrity: sha512-aVJ950olGncxehPezP61wsEHjB3zgDETCThH1FTQ4V9EZ9mcVSNfjXM0SWC+VYF3nZulI2hBZe0od2Ajib4hNA==}
    engines: {vscode: ^1.91.0}

  vscode-languageserver-protocol@3.17.6-next.13:
    resolution: {integrity: sha512-IE+/j+OOqJ392KMhcexIGt9MVqcTZ4n7DVyaSp5txuC1kNUnfzxlkPzzDwo0p7hdINLCfWjbcjuW5tGYLof4Vw==}

  vscode-languageserver-types@3.17.6-next.6:
    resolution: {integrity: sha512-aiJY5/yW+xzw7KPNlwi3gQtddq/3EIn5z8X8nCgJfaiAij2R1APKePngv+MUdLdYJBVTLu+Qa0ODsT+pHgYguQ==}

  vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==}

snapshots:

  '@isaacs/balanced-match@4.0.1': {}

  '@isaacs/brace-expansion@5.0.0':
    dependencies:
      '@isaacs/balanced-match': 4.0.1

  '@types/node@20.19.9':
    dependencies:
      undici-types: 6.21.0

  minimatch@10.0.3:
    dependencies:
      '@isaacs/brace-expansion': 5.0.0

  semver@7.7.2: {}

  undici-types@6.21.0: {}

  vscode-jsonrpc@9.0.0-next.8: {}

  vscode-languageclient@10.0.0-next.16:
    dependencies:
      minimatch: 10.0.3
      semver: 7.7.2
      vscode-languageserver-protocol: 3.17.6-next.13

  vscode-languageserver-protocol@3.17.6-next.13:
    dependencies:
      vscode-jsonrpc: 9.0.0-next.8
      vscode-languageserver-types: 3.17.6-next.6

  vscode-languageserver-types@3.17.6-next.6: {}

  vscode-uri@3.1.0: {}
