lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@vscode/l10n':
        specifier: ^0.0.18
        version: 0.0.18
      vscode-css-languageservice:
        specifier: ^6.3.2
        version: 6.3.7
      vscode-languageserver:
        specifier: ^10.0.0-next.11
        version: 10.0.0-next.13
      vscode-uri:
        specifier: ^3.0.8
        version: 3.1.0
    devDependencies:
      '@types/mocha':
        specifier: ^9.1.1
        version: 9.1.1
      '@types/node':
        specifier: 20.x
        version: 20.19.9

packages:

  '@types/mocha@9.1.1':
    resolution: {integrity: sha512-Z61JK7DKDtdKTWwLeElSEBcWGRLY8g95ic5FoQqI9CMx0ns/Ghep3B4DfcEimiKMvtamNVULVNKEsiwV3aQmXw==}

  '@types/node@20.19.9':
    resolution: {integrity: sha512-cuVNgarYWZqxRJDQHEB58GEONhOK79QVR/qYx4S7kcUObQvUwvFnYxJuuHUKm2aieN9X3yZB4LZsuYNU1Qphsw==}

  '@vscode/l10n@0.0.18':
    resolution: {integrity: sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  vscode-css-languageservice@6.3.7:
    resolution: {integrity: sha512-5TmXHKllPzfkPhW4UE9sODV3E0bIOJPOk+EERKllf2SmAczjfTmYeq5txco+N3jpF8KIZ6loj/JptpHBQuVQRA==}

  vscode-jsonrpc@9.0.0-next.8:
    resolution: {integrity: sha512-pN6L5eiNBvUpNFBJvudaZ83klir0T/wLFCDpYhpOEsKXyhsWyYsNMzoG7BK6zJoZLHGSSsaTJDjCcPwnLgUyPQ==}
    engines: {node: '>=14.0.0'}

  vscode-languageserver-protocol@3.17.6-next.13:
    resolution: {integrity: sha512-IE+/j+OOqJ392KMhcexIGt9MVqcTZ4n7DVyaSp5txuC1kNUnfzxlkPzzDwo0p7hdINLCfWjbcjuW5tGYLof4Vw==}

  vscode-languageserver-textdocument@1.0.12:
    resolution: {integrity: sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==}

  vscode-languageserver-types@3.17.5:
    resolution: {integrity: sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==}

  vscode-languageserver-types@3.17.6-next.6:
    resolution: {integrity: sha512-aiJY5/yW+xzw7KPNlwi3gQtddq/3EIn5z8X8nCgJfaiAij2R1APKePngv+MUdLdYJBVTLu+Qa0ODsT+pHgYguQ==}

  vscode-languageserver@10.0.0-next.13:
    resolution: {integrity: sha512-4tSufM2XrNrrzBUGPcYh62qBYhm41yFwFZBgJ63I1dPHRh1aZPK65+TcVa3nG0/K62Q9phhk87TWdQFp+UnYFA==}
    hasBin: true

  vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==}

snapshots:

  '@types/mocha@9.1.1': {}

  '@types/node@20.19.9':
    dependencies:
      undici-types: 6.21.0

  '@vscode/l10n@0.0.18': {}

  undici-types@6.21.0: {}

  vscode-css-languageservice@6.3.7:
    dependencies:
      '@vscode/l10n': 0.0.18
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-uri: 3.1.0

  vscode-jsonrpc@9.0.0-next.8: {}

  vscode-languageserver-protocol@3.17.6-next.13:
    dependencies:
      vscode-jsonrpc: 9.0.0-next.8
      vscode-languageserver-types: 3.17.6-next.6

  vscode-languageserver-textdocument@1.0.12: {}

  vscode-languageserver-types@3.17.5: {}

  vscode-languageserver-types@3.17.6-next.6: {}

  vscode-languageserver@10.0.0-next.13:
    dependencies:
      vscode-languageserver-protocol: 3.17.6-next.13

  vscode-uri@3.1.0: {}
